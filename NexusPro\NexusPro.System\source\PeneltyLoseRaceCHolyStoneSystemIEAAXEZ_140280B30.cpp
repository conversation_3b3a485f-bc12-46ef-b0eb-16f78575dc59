﻿/*
 * Function: ?PeneltyLoseRace@CHolyStoneSystem@@IEAAXE@Z
 * Address: 0x140280B30
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __usercall CHolyStoneSystem::PeneltyLoseRace(CHolyStoneSystem *this@<rcx>, char byDestroyedRace@<dl>, double a3@<xmm0>)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  int v5; // eax@10
  unsigned __int16 v6; // ax@12
  double v7; // xmm0_8@13
  double v8; // xmm0_8@16
  __int64 v9; // [sp+0h] [bp-68h]@1
  int j; // [sp+30h] [bp-38h]@4
  CPlayer *v11; // [sp+38h] [bp-30h]@7
  int X; // [sp+40h] [bp-28h]@13
  int nAlterPoint; // [sp+44h] [bp-24h]@16
  int v14; // [sp+48h] [bp-20h]@10
  char v15; // [sp+4Ch] [bp-1Ch]@12
  char v16; // [sp+4Dh] [bp-1Bh]@12
  char v17; // [sp+4Eh] [bp-1Ah]@12
  char v18; // [sp+4Fh] [bp-19h]@12
  double v19; // [sp+50h] [bp-18h]@13
  double v20; // [sp+58h] [bp-10h]@16
  CHolyStoneSystem *v21; // [sp+70h] [bp+8h]@1
  char v22; // [sp+78h] [bp+10h]@1

  v22 = byDestroyedRace;
  v21 = this;
  v3 = &v9;
  for (signed __int64 i = 24; i > 0; --i)
  {
    *(DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  for ( j = 0; j < 2532; ++j )
  {
    v11 = &g_Player + j;
    if ( v11->m_bLive )
    {
      if ( v11->m_bOper )
      {
        if ( v11->m_byHSKQuestCode != 100 )
        {
          v14 = (unsigned __int8)v22;
          v5 = CPlayerDB::GetRaceCode(&v11->m_Param);
          if ( v14 == v5 )
          {
            v15 = CHolyStoneSystem::GetNumOfTime(&g_HolySys);
            v16 = CHolyStoneSystem::GetStartHour(&g_HolySys);
            v17 = CHolyStoneSystem::GetStartDay(&g_HolySys);
            v18 = CHolyStoneSystem::GetStartMonth(&g_HolySys);
            v6 = CHolyStoneSystem::GetStartYear(&g_HolySys);
            if ( MiningTicket::AuthLastMentalTicket(&v11->m_MinigTicket, v6, v18, v17, v16, v15) )
            {
              X = v21->m_nRaceBattlePoint[2][0];
              CPlayerDB::GetPvPPoint(&v11->m_Param);
              v19 = a3;
              v7 = (double)abs_0(X);
              if ( v7 > v19 )
              {
                CPlayerDB::GetPvPPoint(&v11->m_Param);
                X = (signed int)floor(-0.0 - v7);
              }
              a3 = (double)X;
              CPlayer::AlterPvPPoint(v11, (double)X, holy_dec, 0xFFFFFFFF);
              CPlayer::SendMsg_RaceBattlePenelty(v11, X, 0);
            }
            else
            {
              nAlterPoint = v21->m_nRaceBattlePoint[2][1];
              CPlayerDB::GetPvPPoint(&v11->m_Param);
              v20 = a3;
              v8 = (double)abs_0(nAlterPoint);
              if ( v8 > v20 )
              {
                CPlayerDB::GetPvPPoint(&v11->m_Param);
                nAlterPoint = (signed int)floor(-0.0 - v8);
              }
              a3 = (double)nAlterPoint;
              CPlayer::AlterPvPPoint(v11, (double)nAlterPoint, holy_dec, 0xFFFFFFFF);
              CPlayer::SendMsg_RaceBattlePenelty(v11, nAlterPoint, 0);
            }
          }
        }
      }
    }
  }
}


