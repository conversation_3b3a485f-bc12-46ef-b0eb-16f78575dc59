﻿/*
 * Function: ??$_Uninit_fill_n@PEAPEAU_PVP_RANK_PACKED_DATA@@_KPEAU1@V?$allocator@PEAU_PVP_RANK_PACKED_DATA@@@std@@@std@@YAXPEAPEAU_PVP_RANK_PACKED_DATA@@_KAEBQEAU1@AEAV?$allocator@PEAU_PVP_RANK_PACKED_DATA@@@0@U_Scalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x140334E50
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall std::_Uninit_fill_n<_PVP_RANK_PACKED_DATA * *,unsigned __int64,_PVP_RANK_PACKED_DATA *,std::allocator<_PVP_RANK_PACKED_DATA *>>(_PVP_RANK_PACKED_DATA **_First, unsigned __int64 _Count, _PVP_RANK_PACKED_DATA *const *_Val, std::allocator<_PVP_RANK_PACKED_DATA *> *__formal, std::_Scalar_ptr_iterator_tag a5, std::_Range_checked_iterator_tag a6)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v8; // [sp+0h] [bp-28h]@1
  _PVP_RANK_PACKED_DATA **_Firsta; // [sp+30h] [bp+8h]@1

  _Firsta = _First;
  v6 = &v8;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  stdext::unchecked_fill_n<_PVP_RANK_PACKED_DATA * *,unsigned __int64,_PVP_RANK_PACKED_DATA *>(_Firsta, _Count, _Val);
}

