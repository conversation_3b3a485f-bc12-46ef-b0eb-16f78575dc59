﻿/*
 * Function: ?cleanup@?$CHashMapPtrPool@HVCNationCodeStr@@@@QEAAXXZ
 * Address: 0x140209A80
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CHashMapPtrPool<int,CNationCodeStr>::cleanup(CHashMapPtrPool<int,CNationCodeStr> *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  std::pair<int const ,CNationCodeStr *> *v3; // rax@6
  __int64 v4; // [sp+0h] [bp-138h]@1
  stdext::_Hash<stdext::_Hmap_traits<int,CNationCodeStr *,stdext::hash_compare<int,std::less<int> >,std::allocator<std::pair<int const ,CNationCodeStr *> >,0> > *v5; // [sp+20h] [bp-118h]@4
  std::list<std::pair<int const ,CNationCodeStr *>,std::allocator<std::pair<int const ,CNationCodeStr *> > >::_Const_iterator<0> _Right; // [sp+38h] [bp-100h]@4
  std::list<std::pair<int const ,CNationCodeStr *>,std::allocator<std::pair<int const ,CNationCodeStr *> > >::_Const_iterator<0> v7; // [sp+68h] [bp-D0h]@4
  std::pair<int,CNationCodeStr *> v8; // [sp+98h] [bp-A0h]@6
  std::list<std::pair<int const ,CNationCodeStr *>,std::allocator<std::pair<int const ,CNationCodeStr *> > >::_Iterator<0> result; // [sp+B8h] [bp-80h]@4
  std::list<std::pair<int const ,CNationCodeStr *>,std::allocator<std::pair<int const ,CNationCodeStr *> > >::_Iterator<0> v10; // [sp+D0h] [bp-68h]@4
  CNationCodeStr *v11; // [sp+E8h] [bp-50h]@6
  CNationCodeStr *v12; // [sp+F0h] [bp-48h]@6
  __int64 v13; // [sp+F8h] [bp-40h]@4
  std::list<std::pair<int const ,CNationCodeStr *>,std::allocator<std::pair<int const ,CNationCodeStr *> > >::_Iterator<0> *v14; // [sp+100h] [bp-38h]@4
  std::list<std::pair<int const ,CNationCodeStr *>,std::allocator<std::pair<int const ,CNationCodeStr *> > >::_Const_iterator<0> *__that; // [sp+108h] [bp-30h]@4
  std::list<std::pair<int const ,CNationCodeStr *>,std::allocator<std::pair<int const ,CNationCodeStr *> > >::_Iterator<0> *v16; // [sp+110h] [bp-28h]@4
  std::list<std::pair<int const ,CNationCodeStr *>,std::allocator<std::pair<int const ,CNationCodeStr *> > >::_Const_iterator<0> *v17; // [sp+118h] [bp-20h]@4
  void *v18; // [sp+120h] [bp-18h]@7
  CHashMapPtrPool<int,CNationCodeStr> *v19; // [sp+140h] [bp+8h]@1

  v19 = this;
  v1 = &v4;
  for (signed __int64 i = 76; i > 0; --i)
  {
    *(DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v13 = -2i64;
  v5 = (stdext::_Hash<stdext::_Hmap_traits<int,CNationCodeStr *,stdext::hash_compare<int,std::less<int> >,std::allocator<std::pair<int const ,CNationCodeStr *> >,0> > *)&v19->m_mapData._Myfirstiter;
  v14 = stdext::_Hash<stdext::_Hmap_traits<int,CNationCodeStr *,stdext::hash_compare<int,std::less<int>>,std::allocator<std::pair<int const,CNationCodeStr *>>,0>>::end(
          (stdext::_Hash<stdext::_Hmap_traits<int,CNationCodeStr *,stdext::hash_compare<int,std::less<int> >,std::allocator<std::pair<int const ,CNationCodeStr *> >,0> > *)&v19->m_mapData._Myfirstiter,
          &result);
  __that = (std::list<std::pair<int const ,CNationCodeStr *>,std::allocator<std::pair<int const ,CNationCodeStr *> > >::_Const_iterator<0> *)v14;
  std::list<std::pair<int const,CNationCodeStr *>,std::allocator<std::pair<int const,CNationCodeStr *>>>::_Const_iterator<0>::_Const_iterator<0>(
    &_Right,
    (std::list<std::pair<int const ,CNationCodeStr *>,std::allocator<std::pair<int const ,CNationCodeStr *> > >::_Const_iterator<0> *)&v14->_Mycont);
  std::list<std::pair<int const,CNationCodeStr *>,std::allocator<std::pair<int const,CNationCodeStr *>>>::_Iterator<0>::~_Iterator<0>(&result);
  v16 = stdext::_Hash<stdext::_Hmap_traits<int,CNationCodeStr *,stdext::hash_compare<int,std::less<int>>,std::allocator<std::pair<int const,CNationCodeStr *>>,0>>::begin(
          v5,
          &v10);
  v17 = (std::list<std::pair<int const ,CNationCodeStr *>,std::allocator<std::pair<int const ,CNationCodeStr *> > >::_Const_iterator<0> *)v16;
  std::list<std::pair<int const,CNationCodeStr *>,std::allocator<std::pair<int const,CNationCodeStr *>>>::_Const_iterator<0>::_Const_iterator<0>(
    &v7,
    (std::list<std::pair<int const ,CNationCodeStr *>,std::allocator<std::pair<int const ,CNationCodeStr *> > >::_Const_iterator<0> *)&v16->_Mycont);
  std::list<std::pair<int const,CNationCodeStr *>,std::allocator<std::pair<int const,CNationCodeStr *>>>::_Iterator<0>::~_Iterator<0>(&v10);
  while ( std::list<std::pair<int const,CNationCodeStr *>,std::allocator<std::pair<int const,CNationCodeStr *>>>::_Const_iterator<0>::operator!=(
            &v7,
            &_Right) )
  {
    v3 = std::list<std::pair<int const,CNationCodeStr *>,std::allocator<std::pair<int const,CNationCodeStr *>>>::_Const_iterator<0>::operator*(&v7);
    std::pair<int,CNationCodeStr *>::pair<int,CNationCodeStr *>(&v8, v3);
    v12 = v8.second;
    v11 = v8.second;
    if ( v8.second )
      v18 = CNationCodeStr::`scalar deleting destructor'(v11, 1u);
    else
      v18 = 0;
    std::list<std::pair<int const,CNationCodeStr *>,std::allocator<std::pair<int const,CNationCodeStr *>>>::_Const_iterator<0>::operator++(&v7);
  }
  std::list<std::pair<int const,CNationCodeStr *>,std::allocator<std::pair<int const,CNationCodeStr *>>>::_Const_iterator<0>::~_Const_iterator<0>(&v7);
  std::list<std::pair<int const,CNationCodeStr *>,std::allocator<std::pair<int const,CNationCodeStr *>>>::_Const_iterator<0>::~_Const_iterator<0>(&_Right);
}


