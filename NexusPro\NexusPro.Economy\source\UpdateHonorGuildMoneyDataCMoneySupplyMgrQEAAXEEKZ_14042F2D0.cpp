﻿/*
 * Function: ?UpdateHonorGuildMoneyData@CMoneySupplyMgr@@QEAAXEEK@Z
 * Address: 0x14042F2D0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CMoneySupplyMgr::UpdateHonorGuildMoneyData(CMoneySupplyMgr * char byTradeType, char byRace, unsigned int nAmount)
{
  if()
{
    if()
{
      this->m_MS_data.dwAmount[6] += nAmount;
      if ( !byRace )
        ++this->m_MS_data.nHonorGuildRace[(unsigned __int8)byTradeType][0];
      if ( byRace == 1 )
        ++this->m_MS_data.nHonorGuildRace[(unsigned __int8)byTradeType][1];
      else
        ++this->m_MS_data.nHonorGuildRace[(unsigned __int8)byTradeType][2];
    }
  }
  else
  {
    this->m_MS_data.dwAmount[3] += nAmount;
    if ( !byRace )
      ++this->m_MS_data.nHonorGuildRace[0][0];
    if ( byRace == 1 )
      ++this->m_MS_data.nHonorGuildRace[(unsigned __int8)byTradeType][1];
    else
      ++this->m_MS_data.nHonorGuildRace[(unsigned __int8)byTradeType][2];
  }
}

