#!/usr/bin/env python3
"""
NexusPro Decompiled Code Syntax Fix Automation
Comprehensive script to fix common decompiled code syntax issues across all modules
"""

import os
import re
import sys
from pathlib import Path
from typing import List, Dict, Tuple
import time

class DecompiledCodeFixer:
    def __init__(self):
        self.fixes_applied = 0
        self.files_processed = 0
        self.errors_found = 0
        
        # Common decompiled code patterns and their fixes
        self.syntax_fixes = [
            # Fix missing type declarations
            (r'\b_DWORD\b', 'DWORD'),
            (r'\b_BYTE\b', 'BYTE'),
            (r'\b_WORD\b', 'WORD'),
            (r'\b_QWORD\b', 'QWORD'),
            
            # Fix incomplete function signatures with malformed parameters
            (r'(\w+::\w+)\(([^)]*)\s*`([^`]*)`([^)]*)\)', r'\1(\2\4)'),
            
            # Fix malformed for loops - pattern: for ( i = 8i64; i; --i )
            (r'for\s*\(\s*(\w+)\s*=\s*(\d+)i64\s*;\s*\1\s*;\s*--\1\s*\)', r'for (\1 = \2; \1 > 0; --\1)'),
            
            # Fix incomplete variable declarations in for loops
            (r'for\s*\(\s*(\w+)\s*=\s*0\s*;\s*\1\s*<\s*(\w+)\s*;\s*\+\+\1\s*\)', r'for (int \1 = 0; \1 < \2; ++\1)'),
            
            # Fix missing semicolons before opening braces
            (r'(\w+\s*\([^)]*\))\s*\n\s*\{', r'\1;\n{'),
            
            # Fix malformed pointer arithmetic
            (r'\(\s*\*\s*\(\s*_DWORD\s*\*\s*\)', '(*(DWORD *)'),
            
            # Fix undefined LODWORD macro
            (r'\bLODWORD\s*\(([^)]+)\)', r'((DWORD)(\1))'),
            
            # Fix missing variable type declarations
            (r'^(\s*)(\w+)\s*\*\s*(\w+);\s*//\s*(\w+)@(\d+)', r'\1\2 *\3; // \4@\5'),
        ]
        
        # External symbol declarations to add
        self.external_symbols = {
            '_ImageBase': 'extern "C" IMAGE_DOS_HEADER __ImageBase;',
            'EqSukList': 'extern struct EqSukData { void* pwszEpSuk; } EqSukList[16];',
            'MyMessageBox': 'extern void MyMessageBox(const char* title, const char* message);',
            '_delayLoadHelper2': 'extern "C" void* _delayLoadHelper2(void* pidd, void** ppfnIATEntry);',
            'off_14000003C': 'extern "C" DWORD off_14000003C;',
            '_security_cookie': 'extern "C" ULONG_PTR _security_cookie;',
        }

    def fix_header_conflicts(self, file_path: Path) -> bool:
        """Fix header redefinition conflicts"""
        try:
            content = file_path.read_text(encoding='utf-8')
            original_content = content
            
            # Remove conflicting ImgDelayDescr definitions
            content = re.sub(r'typedef\s+struct\s+ImgDelayDescr[^}]+}\s*ImgDelayDescr[^;]*;', '', content, flags=re.DOTALL)
            content = re.sub(r'typedef\s+ImgDelayDescr\s*\*\s*PImgDelayDescr\s*;', '', content)
            
            if content != original_content:
                file_path.write_text(content, encoding='utf-8')
                return True
                
        except Exception as e:
            print(f"Error fixing header conflicts in {file_path}: {e}")
            
        return False

    def fix_function_signature(self, content: str) -> str:
        """Fix malformed function signatures"""
        # Pattern for malformed function signatures with backticks and extra characters
        pattern = r'(\w+\s+__\w+\s+)(\w+::\w+)\(([^)]*)`([^`]*)`([^)]*)\)'
        
        def fix_signature(match):
            return_type = match.group(1)
            function_name = match.group(2)
            params_before = match.group(3)
            backtick_content = match.group(4)
            params_after = match.group(5)
            
            # Clean up the parameters
            clean_params = params_before + params_after
            clean_params = re.sub(r'\s+', ' ', clean_params).strip()
            
            return f"{return_type}{function_name}({clean_params})"
        
        return re.sub(pattern, fix_signature, content)

    def fix_incomplete_loops(self, content: str) -> str:
        """Fix incomplete and malformed loop structures"""
        fixes = [
            # Fix for loops with i64 suffix
            (r'for\s*\(\s*(\w+)\s*=\s*(\d+)i64\s*;\s*\1\s*;\s*--\1\s*\)', r'for (signed __int64 \1 = \2; \1 > 0; --\1)'),
            
            # Fix for loops missing variable declarations
            (r'for\s*\(\s*(\w+)\s*=\s*0\s*;\s*\1\s*<\s*(\w+)\s*;\s*\+\+\1\s*\)', r'for (int \1 = 0; \1 < \2; ++\1)'),
            
            # Fix malformed for loop syntax
            (r'for\s*\(\s*(\w+)\s*=\s*(\d+)\s*;\s*\1\s*<\s*(\w+)\s*;\s*\+\+\1\s*\)', r'for (int \1 = \2; \1 < \3; ++\1)'),
        ]
        
        for pattern, replacement in fixes:
            content = re.sub(pattern, replacement, content)
            
        return content

    def add_missing_declarations(self, content: str) -> str:
        """Add missing variable and type declarations"""
        lines = content.split('\n')
        modified_lines = []
        
        for line in lines:
            # Check for undefined symbols and add declarations
            for symbol, declaration in self.external_symbols.items():
                if symbol in line and declaration not in content:
                    # Add declaration at the top after includes
                    if '#include' in line and 'RFOnlineClasses.h' in line:
                        modified_lines.append(line)
                        modified_lines.append('')
                        modified_lines.append(f'// External symbol declaration')
                        modified_lines.append(declaration)
                        continue
            
            modified_lines.append(line)
        
        return '\n'.join(modified_lines)

    def fix_syntax_patterns(self, content: str) -> str:
        """Apply all syntax pattern fixes"""
        for pattern, replacement in self.syntax_fixes:
            content = re.sub(pattern, replacement, content, flags=re.MULTILINE)
        
        return content

    def fix_file(self, file_path: Path) -> bool:
        """Fix a single source file"""
        try:
            if not file_path.exists() or file_path.suffix not in ['.cpp', '.h']:
                return False
                
            content = file_path.read_text(encoding='utf-8')
            original_content = content
            
            # Apply all fixes
            content = self.fix_function_signature(content)
            content = self.fix_incomplete_loops(content)
            content = self.fix_syntax_patterns(content)
            content = self.add_missing_declarations(content)
            
            # Write back if changed
            if content != original_content:
                file_path.write_text(content, encoding='utf-8')
                self.fixes_applied += 1
                return True
                
        except Exception as e:
            print(f"Error fixing {file_path}: {e}")
            self.errors_found += 1
            
        return False

    def fix_module(self, module_path: Path) -> Dict[str, int]:
        """Fix all files in a module"""
        stats = {'files_processed': 0, 'files_fixed': 0, 'errors': 0}
        
        if not module_path.exists():
            print(f"Module path does not exist: {module_path}")
            return stats
        
        # Process source files
        source_dir = module_path / 'source'
        if source_dir.exists():
            for cpp_file in source_dir.glob('*.cpp'):
                stats['files_processed'] += 1
                if self.fix_file(cpp_file):
                    stats['files_fixed'] += 1
        
        # Process header files
        headers_dir = module_path / 'headers'
        if headers_dir.exists():
            for h_file in headers_dir.glob('*.h'):
                stats['files_processed'] += 1
                if self.fix_file(h_file):
                    stats['files_fixed'] += 1
        
        return stats

def main():
    """Main function"""
    if len(sys.argv) < 2:
        print("Usage: python fix_decompiled_syntax.py <module_name|all>")
        print("Example: python fix_decompiled_syntax.py NexusPro.Economy")
        print("         python fix_decompiled_syntax.py all")
        return 1
    
    target = sys.argv[1]
    fixer = DecompiledCodeFixer()
    
    nexuspro_path = Path('NexusPro')
    if not nexuspro_path.exists():
        print("Error: NexusPro directory not found")
        return 1
    
    start_time = time.time()
    total_stats = {'files_processed': 0, 'files_fixed': 0, 'errors': 0}
    
    if target.lower() == 'all':
        # Fix all modules
        modules = [d for d in nexuspro_path.iterdir() if d.is_dir() and d.name.startswith('NexusPro.')]
        
        print(f"🔧 Starting decompiled code syntax fixes for {len(modules)} modules...")
        
        for module_dir in modules:
            print(f"\n📁 Processing {module_dir.name}...")
            stats = fixer.fix_module(module_dir)
            
            for key in total_stats:
                total_stats[key] += stats[key]
            
            print(f"   ✅ {stats['files_fixed']}/{stats['files_processed']} files fixed")
            if stats['errors'] > 0:
                print(f"   ⚠️  {stats['errors']} errors encountered")
    
    else:
        # Fix specific module
        module_path = nexuspro_path / target
        if not module_path.exists():
            print(f"Error: Module {target} not found")
            return 1
        
        print(f"🔧 Starting decompiled code syntax fixes for {target}...")
        total_stats = fixer.fix_module(module_path)
    
    elapsed_time = time.time() - start_time
    
    print(f"\n🎉 Decompiled Code Syntax Fix Complete!")
    print(f"📊 Statistics:")
    print(f"   • Files Processed: {total_stats['files_processed']}")
    print(f"   • Files Fixed: {total_stats['files_fixed']}")
    print(f"   • Errors: {total_stats['errors']}")
    print(f"   • Processing Time: {elapsed_time:.2f} seconds")
    print(f"   • Success Rate: {(total_stats['files_fixed']/max(total_stats['files_processed'],1)*100):.1f}%")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
