﻿/*
 * Function: ??$unchecked_uninitialized_copy@V?$_Vector_const_iterator@UAreaData@@V?$allocator@UAreaData@@@std@@@std@@PEAUAreaData@@V?$allocator@UAreaData@@@2@@stdext@@YAPEAUAreaData@@V?$_Vector_const_iterator@UAreaData@@V?$allocator@UAreaData@@@std@@@std@@0PEAU1@AEAV?$allocator@UAreaData@@@3@@Z
 * Address: 0x140192FA0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


AreaData *__fastcall stdext::unchecked_uninitialized_copy<std::_Vector_const_iterator<AreaData,std::allocator<AreaData>>,AreaData *,std::allocator<AreaData>>(std::_Vector_const_iterator<AreaData,std::allocator<AreaData> > *_First, std::_Vector_const_iterator<AreaData,std::allocator<AreaData> > *_Last, AreaData *_Dest, std::allocator<AreaData> *_Al)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  std::_Vector_const_iterator<AreaData,std::allocator<AreaData> > *v6; // rax@4
  std::_Vector_const_iterator<AreaData,std::allocator<AreaData> > *v7; // rax@4
  __int64 v9; // [sp+0h] [bp-A8h]@1
  AreaData *v10; // [sp+30h] [bp-78h]@4
  std::_Range_checked_iterator_tag v11; // [sp+38h] [bp-70h]@4
  std::_Nonscalar_ptr_iterator_tag v12; // [sp+39h] [bp-6Fh]@4
  char v13; // [sp+40h] [bp-68h]@4
  std::_Vector_const_iterator<AreaData,std::allocator<AreaData> > *v14; // [sp+58h] [bp-50h]@4
  char v15; // [sp+60h] [bp-48h]@4
  std::_Vector_const_iterator<AreaData,std::allocator<AreaData> > *v16; // [sp+78h] [bp-30h]@4
  __int64 v17; // [sp+80h] [bp-28h]@4
  std::_Vector_const_iterator<AreaData,std::allocator<AreaData> > *v18; // [sp+88h] [bp-20h]@4
  std::_Vector_const_iterator<AreaData,std::allocator<AreaData> > *v19; // [sp+90h] [bp-18h]@4
  std::_Vector_const_iterator<AreaData,std::allocator<AreaData> > *v20; // [sp+98h] [bp-10h]@4
  std::_Vector_const_iterator<AreaData,std::allocator<AreaData> > *__formal; // [sp+B0h] [bp+8h]@1
  std::_Vector_const_iterator<AreaData,std::allocator<AreaData> > *__that; // [sp+B8h] [bp+10h]@1
  AreaData *v23; // [sp+C0h] [bp+18h]@1
  std::allocator<AreaData> *v24; // [sp+C8h] [bp+20h]@1

  v24 = _Al;
  v23 = _Dest;
  __that = _Last;
  __formal = _First;
  v4 = &v9;
  for (signed __int64 i = 40; i > 0; --i)
  {
    *(DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v17 = -2i64;
  memset(&v11, 0, sizeof(v11));
  v12 = std::_Ptr_cat<std::_Vector_const_iterator<AreaData,std::allocator<AreaData>>,AreaData *>(__formal, &v23);
  v14 = (std::_Vector_const_iterator<AreaData,std::allocator<AreaData> > *)&v13;
  v16 = (std::_Vector_const_iterator<AreaData,std::allocator<AreaData> > *)&v15;
  std::_Vector_const_iterator<AreaData,std::allocator<AreaData>>::_Vector_const_iterator<AreaData,std::allocator<AreaData>>(
    (std::_Vector_const_iterator<AreaData,std::allocator<AreaData> > *)&v13,
    __that);
  v18 = v6;
  v19 = v6;
  std::_Vector_const_iterator<AreaData,std::allocator<AreaData>>::_Vector_const_iterator<AreaData,std::allocator<AreaData>>(
    v16,
    __formal);
  v20 = v7;
  v10 = std::_Uninit_copy<std::_Vector_const_iterator<AreaData,std::allocator<AreaData>>,AreaData *,std::allocator<AreaData>>(
          v7,
          v19,
          v23,
          v24,
          v12,
          v11);
  std::_Vector_const_iterator<AreaData,std::allocator<AreaData>>::~_Vector_const_iterator<AreaData,std::allocator<AreaData>>(__formal);
  std::_Vector_const_iterator<AreaData,std::allocator<AreaData>>::~_Vector_const_iterator<AreaData,std::allocator<AreaData>>(__that);
  return v10;
}


