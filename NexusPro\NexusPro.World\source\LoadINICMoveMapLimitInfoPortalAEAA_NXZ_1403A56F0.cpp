﻿/*
 * Function: ?LoadINI@CMoveMapLimitInfoPortal@@AEAA_NXZ
 * Address: 0x1403A56F0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// External symbol declarations for decompiled code
extern "C" ULONG_PTR _security_cookie;



char __fastcall CMoveMapLimitInfoPortal::LoadINI(CMoveMapLimitInfoPortal *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  unsigned int v3; // eax@4
  unsigned int v4; // eax@5
  char result; // al@5
  unsigned int v6; // eax@7
  __int64 v7; // [sp+0h] [bp-898h]@1
  DWORD nSize[2]; // [sp+20h] [bp-878h]@5
  char DstBuf; // [sp+40h] [bp-858h]@4
  char v10; // [sp+41h] [bp-857h]@4
  char ReturnedString; // [sp+460h] [bp-438h]@4
  char v12; // [sp+461h] [bp-437h]@4
  CMapData *v13; // [sp+868h] [bp-30h]@6
  int v14; // [sp+878h] [bp-20h]@5
  int v15; // [sp+87Ch] [bp-1Ch]@7
  unsigned __int64 v16; // [sp+880h] [bp-18h]@4
  CMoveMapLimitInfoPortal *v17; // [sp+8A0h] [bp+8h]@1

  v17 = this;
  v1 = &v7;
  for (signed __int64 i = 548; i > 0; --i)
  {
    *(DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v16 = (unsigned __int64)&v7 ^ _security_cookie;
  DstBuf = 0;
  memset(&v10, 0, 0x3FFui64);
  ReturnedString = 0;
  memset(&v12, 0, 0x3FFui64);
  v3 = CMoveMapLimitInfo::GetInx((CMoveMapLimitInfo *)&v17->vfptr);
  sprintf_s(&DstBuf, 0x400ui64, "Map%u", v3);
  GetPrivateProfileStringA(
    "MoveMapLimitInfo",
    &DstBuf,
    "NULL",
    &ReturnedString,
    0x400u,
    "./Initialize/MoveMapLimit.ini");
  if ( !strcmp_0(&ReturnedString, "NULL") )
  {
    v14 = CMoveMapLimitInfo::GetType((CMoveMapLimitInfo *)&v17->vfptr);
    v4 = CMoveMapLimitInfo::GetInx((CMoveMapLimitInfo *)&v17->vfptr);
    *(QWORD *)nSize = &DstBuf;
    CLogFile::Write(
      &stru_1799C8F30,
      "CMoveMapLimitInfoPortal::LoadINI() : Inx(%u) Type(%d) %s NULL!",
      v4,
      (unsigned int)v14);
    result = 0;
  }
  else
  {
    v13 = CMapOperation::GetMap(&g_MapOper, &ReturnedString);
    if ( v13 )
    {
      v17->m_iMapInx = v13->m_nMapCode;
      result = 1;
    }
    else
    {
      v15 = CMoveMapLimitInfo::GetType((CMoveMapLimitInfo *)&v17->vfptr);
      v6 = CMoveMapLimitInfo::GetInx((CMoveMapLimitInfo *)&v17->vfptr);
      *(QWORD *)nSize = &ReturnedString;
      CLogFile::Write(
        &stru_1799C8F30,
        "CMoveMapLimitInfoPortal::LoadINI() : Inx(%u) Type(%d) g_MapOper.GetMap( szData(%s) ) NULL!",
        v6,
        (unsigned int)v15);
      result = 0;
    }
  }
  return result;
}


