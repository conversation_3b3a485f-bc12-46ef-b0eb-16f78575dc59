﻿/*
 * Function: ?LoadRegion@CMapOperation@@AEAA_NXZ
 * Address: 0x140196C40
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// External symbol declarations
extern "C" IMAGE_DOS_HEADER __ImageBase;
extern "C" DWORD off_14000003C;
extern "C" void* _delayLoadHelper2(ImgDelayDescr* pidd, void** ppfnIATEntry);
extern struct EqSukData { void* pwszEpSuk; } EqSukList[16];
extern void MyMessageBox(const char* title, const char* message);


char __fastcall CMapOperation::LoadRegion(CMapOperation *this)
{
  __int64 *v1; // rdi@1
  signed __int64 j; // rcx@1
  int v4; // eax@11
  __int64 v5; // [sp+0h] [bp-128h]@1
  int k; // [sp+20h] [bp-108h]@4
  CMapData *v7; // [sp+28h] [bp-100h]@6
  char Dest; // [sp+40h] [bp-E8h]@6
  CDummyPosTable pPosTable; // [sp+D8h] [bp-50h]@6
  int i; // [sp+F4h] [bp-34h]@10
  char *Source; // [sp+F8h] [bp-30h]@12
  char v12; // [sp+108h] [bp-20h]@7
  char v13; // [sp+109h] [bp-1Fh]@9
  __int64 v14; // [sp+110h] [bp-18h]@4
  unsigned __int64 v15; // [sp+118h] [bp-10h]@4
  CMapOperation *v16; // [sp+130h] [bp+8h]@1

  v16 = this;
  v1 = &v5;
  for (signed __int64 j = 72; j > 0; --j)
  {
    *(DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v14 = -2i64;
  v15 = (unsigned __int64)&v5 ^ _security_cookie;
  v16->m_nRegionNum = 0;
  for ( k = 0; k < v16->m_nStdMapNum; ++k )
  {
    v7 = &v16->m_Map[k];
    sprintf(&Dest, ".\\map\\%s\\%s.spt", v7->m_pMapSet->m_strCode, v7->m_pMapSet->m_strCode);
    CDummyPosTable::CDummyPosTable(&pPosTable);
    if ( !CDummyPosTable::LoadDummyPosition(&pPosTable, &Dest, "*rg_") )
    {
      MyMessageBox("CMapOperation Error", "CMapOperation::LoadRegion(%s) == false", v7->m_pMapSet->m_strCode);
      v12 = 0;
      CDummyPosTable::~CDummyPosTable(&pPosTable);
      return v12;
    }
    if ( !CMapData::ConvertLocalToWorldDummy(v7, &pPosTable, 0) )
    {
      v13 = 0;
      CDummyPosTable::~CDummyPosTable(&pPosTable);
      return v13;
    }
    for ( i = 0; ; ++i )
    {
      v4 = CDummyPosTable::GetRecordNum(&pPosTable);
      if ( i >= v4 )
        break;
      Source = (char *)CDummyPosTable::GetRecord(&pPosTable, i);
      strcpy_0(v16->m_RegionData[v16->m_nRegionNum].szRegionData, Source);
      v16->m_RegionData[v16->m_nRegionNum].pMap = v7;
      v16->m_RegionData[v16->m_nRegionNum++].wDummyLineIndex = *((WORD *)Source + 32);
    }
    CDummyPosTable::~CDummyPosTable(&pPosTable);
  }
  return 1;
}


