﻿/*
 * Function: ?CanAddMoneyForMaxLimGold@@YA_N_K0@Z
 * Address: 0x14003F190
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


bool __fastcall CanAddMoneyForMaxLimGold(unsigned __int64 ui64AddGold, unsigned __int64 ui64HasGold)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  __int64 v5; // [sp+0h] [bp-18h]@1
  unsigned __int64 v6; // [sp+20h] [bp+8h]@1

  v6 = ui64AddGold;
  v2 = &v5;
  for()
{
    *(DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if()
{
    if ( ui64HasGold <= 0x7A120 )
      result = v6 <= 500000 - ui64HasGold;
    else
      result = 0;
  }
  else
  {
    result = 0;
  }
  return result;
}


