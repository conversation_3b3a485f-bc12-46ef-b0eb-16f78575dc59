﻿/*
 * Function: ?dtor_2@?0??_Reset@?$_Greedy_node@W4agent_status@Concurrency@@@Concurrency@@UEAAXXZ@4HA_0
 * Address: 0x1405EA670
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Required includes for Concurrency namespace
#include <ppl.h>
#include <ppltasks.h>
#include <concurrent_vector.h>



void __fastcallConcurrency::_Greedy_node<enumConcurrency::agent_status> ::_Reset'::1'::dtor_2(__int64 a1, __int64 a2)
{
  if ( *(DWORD *)(a2 + 112) & 1 )
  {
    *(DWORD *)(a2 + 112) &= 0xFFFFFFFE;
    CryptoPP::Integer::~Integer(*(CryptoPP::Integer **)(a2 + 152));
  }
}


