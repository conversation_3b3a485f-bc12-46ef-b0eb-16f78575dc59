﻿/*
 * Function: ?DrawBitmap@CSurface@@QEAAJPEAUHBITMAP__@@KKKK@Z
 * Address: 0x140434FE0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


signed __int64 __fastcall CSurface::DrawBitmap(CSurface *this, HBITMAP__ *hBMP, unsigned int dwBMPOriginX, unsigned int dwBMPOriginY, unsigned int dwBMPWidth, unsigned int dwBMPHeight)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 result; // rax@6
  __int64 v9; // [sp+0h] [bp-188h]@1
  HDC hdc; // [sp+60h] [bp-128h]@11
  HDC hdcDest; // [sp+78h] [bp-110h]@19
  char pv; // [sp+98h] [bp-F0h]@13
  unsigned int v13; // [sp+9Ch] [bp-ECh]@14
  unsigned int v14; // [sp+A0h] [bp-E8h]@17
  int v15; // [sp+E0h] [bp-A8h]@9
  int v16; // [sp+E8h] [bp-A0h]@21
  int wDest; // [sp+ECh] [bp-9Ch]@21
  int v18; // [sp+134h] [bp-54h]@9
  unsigned int v19; // [sp+174h] [bp-14h]@7
  unsigned int v20; // [sp+178h] [bp-10h]@14
  unsigned int v21; // [sp+17Ch] [bp-Ch]@17
  CSurface *v22; // [sp+190h] [bp+8h]@1
  HBITMAP__ *h; // [sp+198h] [bp+10h]@1
  unsigned int v24; // [sp+1A0h] [bp+18h]@1
  unsigned int v25; // [sp+1A8h] [bp+20h]@1
  int dwBMPWidtha; // [sp+1B0h] [bp+28h]@16
  int dwBMPHeighta; // [sp+1B8h] [bp+30h]@19

  v25 = dwBMPOriginY;
  v24 = dwBMPOriginX;
  h = hBMP;
  v22 = this;
  v6 = &v9;
  for (signed __int64 i = 96; i > 0; --i)
  {
    *(DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  if ( hBMP && v22->m_pdds )
  {
    v19 = ((int (__fastcall *)(IDirectDrawSurface7 *))v22->m_pdds->vfptr[9].QueryInterface)(v22->m_pdds);
    if ( (v19 & 0x80000000) == 0 )
    {
      v15 = 136;
      ((void (__fastcall *)(IDirectDrawSurface7 *, int *))v22->m_pdds->vfptr[7].AddRef)(v22->m_pdds, &v15);
      if ( v18 == 4 )
      {
        result = 2147500033i64;
      }
      else
      {
        hdc = CreateCompatibleDC(0i64);
        if ( hdc )
        {
          SelectObject(hdc, h);
          GetObjectA(h, 32, &pv);
          if ( dwBMPWidth )
            v20 = dwBMPWidth;
          else
            v20 = v13;
          dwBMPWidtha = v20;
          if ( dwBMPHeight )
            v21 = dwBMPHeight;
          else
            v21 = v14;
          dwBMPHeighta = v21;
          v19 = ((int (__fastcall *)(IDirectDrawSurface7 *, HDC *))v22->m_pdds->vfptr[5].Release)(v22->m_pdds, &hdcDest);
          if ( (v19 & 0x80000000) == 0 )
          {
            StretchBlt(hdcDest, 0, 0, wDest, v16, hdc, v24, v25, dwBMPWidtha, dwBMPHeighta, 0xCC0020u);
            v19 = ((int (__fastcall *)(IDirectDrawSurface7 *, HDC))v22->m_pdds->vfptr[8].Release)(v22->m_pdds, hdcDest);
            if ( (v19 & 0x80000000) == 0 )
            {
              DeleteDC(hdc);
              result = 0;
            }
            else
            {
              result = v19;
            }
          }
          else
          {
            result = v19;
          }
        }
        else
        {
          result = 2147500037i64;
        }
      }
    }
    else
    {
      result = v19;
    }
  }
  else
  {
    result = 2147942487i64;
  }
  return result;
}


