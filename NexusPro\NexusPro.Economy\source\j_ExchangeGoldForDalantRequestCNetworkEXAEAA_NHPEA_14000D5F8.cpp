﻿/*
 * Function: j_?ExchangeGoldForDalantRequest@CNetworkEX@@AEAA_NHPEAD@Z
 * Address: 0x14000D5F8
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


bool __fastcall CNetworkEX::ExchangeGoldForDalantRequest(CNetworkEX * int n, char *pBuf)
{
  return // CNetworkEX::ExchangeGoldForDalantRequest(); // MALFORMED CONSTRUCTOR - COMMENTED OUT
}

