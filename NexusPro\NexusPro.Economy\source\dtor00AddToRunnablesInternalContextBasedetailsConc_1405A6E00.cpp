﻿/*
 * Function: ?dtor_0@?0??AddToRunnables@InternalContextBase@details@Concurrency@@MEAAXVlocation@3@@Z@4HA_4
 * Address: 0x1405A6E00
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Required includes for Concurrency namespace
#include <ppl.h>
#include <ppltasks.h>
#include <concurrent_vector.h>



void __fastcallConcurrency::details::InternalContextBase::AddToRunnables'::1'::dtor_0(__int64 a1, __int64 a2)
{
  std::_Vector_const_iterator<unsigned int,std::allocator<unsigned int> >::~_Vector_const_iterator<unsigned int,std::allocator<unsigned int>>(*(std::_Vector_const_iterator<unsigned int,std::allocator<unsigned int> > **)(a2 + 264));
}

