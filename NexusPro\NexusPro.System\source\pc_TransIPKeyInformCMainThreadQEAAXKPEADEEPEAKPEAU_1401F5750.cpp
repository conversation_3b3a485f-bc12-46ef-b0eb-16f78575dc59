﻿/*
 * Function: ?pc_TransIPKeyInform@CMainThread@@QEAAXKPEADEEPEAKPEAU_GLBID@@K_NF0PEAU_SYSTEMTIME@@JE0E0E0E3H311@Z
 * Address: 0x1401F5750
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CMainThread::pc_TransIPKeyInform(CMainThread *this, unsigned int dwAccountSerial, char *pszAccountID, char byUserDgr, char bySubDgr, unsigned int *pdwKey, _GLBID *pgidGlobal, unsigned int dwClientIP, bool bChatLock, __int16 iType, char *szCMS, _SYSTEMTIME *pstEndDate, int lRemainTime, char by<PERSON>Lock, char *sz<PERSON><PERSON><PERSON><PERSON><PERSON>, char by<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>nt, char *szAccountP<PERSON>, char by<PERSON>Lock_HintIndex, char *uszUILock_HintAnswer, char byUILockFindPassFailCount, bool bIsPcBang, int nTrans, bool bAgeLimit, unsigned int *pdwRequestMoveCharacterSerialList, unsigned int *pdwTournamentCharacterSerialList)
{
  __int64 *v25; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v27; // ax@29
  __int64 v28; // [sp+0h] [bp-C8h]@1
  char *pszCause; // [sp+20h] [bp-A8h]@23
  char v30; // [sp+40h] [bp-88h]@4
  int j; // [sp+44h] [bp-84h]@7
  char v32; // [sp+48h] [bp-80h]@4
  CUserDB *v33; // [sp+50h] [bp-78h]@10
  _WAIT_ENTER_ACCOUNT *v34; // [sp+58h] [bp-70h]@17
  int k; // [sp+60h] [bp-68h]@20
  char Dst; // [sp+78h] [bp-50h]@29
  char v37; // [sp+80h] [bp-48h]@29
  char pbyType; // [sp+A4h] [bp-24h]@29
  char v39; // [sp+A5h] [bp-23h]@29
  CLogFile *v40; // [sp+B8h] [bp-10h]@23
  CMainThread *v41; // [sp+D0h] [bp+8h]@1
  char *pszAccountIDa; // [sp+E0h] [bp+18h]@1
  char v43; // [sp+E8h] [bp+20h]@1

  v43 = byUserDgr;
  pszAccountIDa = pszAccountID;
  v41 = this;
  v25 = &v28;
  for (signed __int64 i = 48; i > 0; --i)
  {
    *(DWORD *)v25 = -*********;
    v25 = (__int64 *)((char *)v25 + 4);
  }
  v30 = 0;
  v32 = 0;
  if ( CUserDB::s_nLoginNum < v41->m_nLimUserNum || byUserDgr )
  {
    for ( j = 0; j < 2532; ++j )
    {
      v33 = &g_UserDB[j];
      if ( v33->m_bActive && v33->m_dwAccountSerial == dwAccountSerial )
      {
        CUserDB::ForceCloseCommand(v33, 1, dwClientIP, 0, "À¯·ÉÄÉ¸¯");
        v30 = 2;
        goto LABEL_29;
      }
    }
    for ( j = 0; j < 2532; ++j )
    {
      v34 = &v41->m_WaitEnterAccount[j];
      if ( !v34->m_bLoad )
      {
        _WAIT_ENTER_ACCOUNT::SetData(
          v34,
          dwAccountSerial,
          pszAccountID,
          byUserDgr,
          bySubDgr,
          pgidGlobal,
          pdwKey,
          bChatLock);
        _WAIT_ENTER_ACCOUNT::SetBillingInfo(v34, iType, szCMS, lRemainTime, pstEndDate);
        _WAIT_ENTER_ACCOUNT::SetPcBangFlag(v34, bIsPcBang);
        _WAIT_ENTER_ACCOUNT::SetTransFlag(v34, nTrans);
        _WAIT_ENTER_ACCOUNT::SetAgeLimitFlag(v34, bAgeLimit);
        if ( !v43 )
          _WAIT_ENTER_ACCOUNT::SetUILock(
            v34,
            byUILock,
            szUILockPW,
            byUILockFailCnt,
            szAccountPW,
            byUILock_HintIndex,
            uszUILock_HintAnswer,
            byUILockFindPassFailCount);
        for ( k = 0; k < 3; ++k )
        {
          v34->m_dwRequestMoveCharacterSerialList[k] = pdwRequestMoveCharacterSerialList[k];
          v34->m_dwTournamentCharacterSerialList[k] = pdwTournamentCharacterSerialList[k];
        }
        v40 = &v41->m_logBillCheck;
        ((DWORD)(pszCause) = bAgeLimit;
        CLogFile::Write(&v41->m_logBillCheck, "id:%s Bill:%d bAgeLimit:%d", pszAccountIDa, (unsigned int)iType);
        if ( unk_1414ACE84 && !CNetSocket::PushIPCheckList(&stru_1414ACB30, dwClientIP) )
        {
          v30 = 8;
          goto LABEL_29;
        }
        v32 = 1;
        break;
      }
    }
    if ( !v32 )
      v30 = 8;
  }
  else
  {
    v30 = 8;
  }
LABEL_29:
  v37 = v30;
  memcpy_0(&Dst, pgidGlobal, 8ui64);
  pbyType = 1;
  v39 = 9;
  v27 = _trans_account_report_wrac::size((_trans_account_report_wrac *)&Dst);
  CNetProcess::LoadSendMsg(unk_1414F2090, 0, &pbyType, &Dst, v27);
}


