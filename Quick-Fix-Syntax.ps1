# Quick Decompiled Code Syntax Fix for NexusPro Economy Module
param(
    [string]$ModuleName = "NexusPro.Economy"
)

Write-Host "🔧 Starting syntax fixes for $ModuleName..." -ForegroundColor Green

$modulePath = "NexusPro\$ModuleName"
if (-not (Test-Path $modulePath)) {
    Write-Error "Module path not found: $modulePath"
    exit 1
}

$sourceDir = "$modulePath\source"
$filesFixed = 0
$filesProcessed = 0

if (Test-Path $sourceDir) {
    $cppFiles = Get-ChildItem -Path $sourceDir -Filter "*.cpp"
    
    foreach ($file in $cppFiles) {
        $filesProcessed++
        
        try {
            $content = Get-Content -Path $file.FullName -Raw -Encoding UTF8
            $originalContent = $content
            
            # Apply basic fixes
            $content = $content -replace '\b_DWORD\b', 'DWORD'
            $content = $content -replace '\b_BYTE\b', 'BYTE'
            $content = $content -replace '\b_WORD\b', 'WORD'
            $content = $content -replace '\bLODWORD\s*\(([^)]+)\)', '((DWORD)($1))'
            
            # Fix for loops with i64 suffix
            $content = $content -replace 'for\s*\(\s*(\w+)\s*=\s*(\d+)i64\s*;\s*\1\s*;\s*--\1\s*\)', 'for (signed __int64 $1 = $2; $1 > 0; --$1)'
            
            # Add external declarations if needed
            if ($content -match '_ImageBase' -and $content -notmatch 'extern.*__ImageBase') {
                $content = $content -replace '(#include "../../NexusPro\.Core/headers/RFOnlineClasses\.h")', '$1' + "`n`nextern `"C`" IMAGE_DOS_HEADER __ImageBase;"
            }
            
            if ($content -match 'EqSukList' -and $content -notmatch 'extern.*EqSukList') {
                $content = $content -replace '(#include "../../NexusPro\.Core/headers/RFOnlineClasses\.h")', '$1' + "`n`nextern struct EqSukData { void* pwszEpSuk; } EqSukList[16];"
            }
            
            if ($content -match 'MyMessageBox' -and $content -notmatch 'extern.*MyMessageBox') {
                $content = $content -replace '(#include "../../NexusPro\.Core/headers/RFOnlineClasses\.h")', '$1' + "`n`nextern void MyMessageBox(const char* title, const char* message);"
            }
            
            if ($content -match '_delayLoadHelper2' -and $content -notmatch 'extern.*_delayLoadHelper2') {
                $content = $content -replace '(#include "../../NexusPro\.Core/headers/RFOnlineClasses\.h")', '$1' + "`n`nextern `"C`" void* _delayLoadHelper2(ImgDelayDescr* pidd, void** ppfnIATEntry);"
            }
            
            if ($content -ne $originalContent) {
                Set-Content -Path $file.FullName -Value $content -Encoding UTF8
                $filesFixed++
            }
            
        } catch {
            Write-Warning "Error processing $($file.Name): $_"
        }
        
        if ($filesProcessed % 20 -eq 0) {
            Write-Host "   Processed $filesProcessed files..." -ForegroundColor Yellow
        }
    }
}

Write-Host "`n✅ Syntax fixes complete!" -ForegroundColor Green
Write-Host "📊 Files processed: $filesProcessed" -ForegroundColor White
Write-Host "📊 Files fixed: $filesFixed" -ForegroundColor White

if ($filesFixed -gt 0) {
    Write-Host "`n🔨 Testing build after fixes..." -ForegroundColor Cyan
    
    $msbuildPath = "C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe"
    $projectPath = "$modulePath\$ModuleName.vcxproj"
    
    if (Test-Path $msbuildPath -and Test-Path $projectPath) {
        Write-Host "Building $ModuleName..." -ForegroundColor Yellow
        
        $buildResult = & $msbuildPath $projectPath /p:Configuration=Debug /p:Platform=x64 /v:minimal /nologo 2>&1
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "🎉 BUILD SUCCESSFUL!" -ForegroundColor Green
        } else {
            Write-Host "❌ Build failed. First few errors:" -ForegroundColor Red
            $buildResult | Select-Object -First 10 | ForEach-Object { Write-Host "   $_" -ForegroundColor Red }
        }
    }
}
