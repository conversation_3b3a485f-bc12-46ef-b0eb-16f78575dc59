﻿/*
 * Function: ?dtor_0@?0???**************************@details@Concurrency@@QEAA@XZ@4HA_2
 * Address: 0x14055CD30
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Required includes for Concurrency namespace
#include <ppl.h>
#include <ppltasks.h>
#include <concurrent_vector.h>



int __fastcallConcurrency::details::ThreadProxyFactoryManager::~ThreadProxyFactoryManager'::1'::dtor_0(__int64 a1, __int64 a2)
{
  return CryptoPP::EcPrecomputation<CryptoPP::EC2N> ::~EcPrecomputation<CryptoPP::EC2N>(*(QWORD *)(a2 + 64) + 24i64);
}


