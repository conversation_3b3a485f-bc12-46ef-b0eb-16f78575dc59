﻿/*
 * Function: ??$_Uninit_move@PEAEPEAEV?$allocator@E@std@@U_Undefined_move_tag@2@@std@@YAPEAEPEAE00AEAV?$allocator@E@0@U_Undefined_move_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x14033FB60
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


char *__fastcall std::_Uninit_move<unsigned char *,unsigned char *,std::allocator<unsigned char>,std::_Undefined_move_tag>(char *_First, char *_Last, char *_Dest, std::allocator<unsigned char> *_Al, std::_Undefined_move_tag __formal, std::_Range_checked_iterator_tag a6)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v9; // [sp+0h] [bp-28h]@1
  char *_Firsta; // [sp+30h] [bp+8h]@1

  _Firsta = _First;
  v6 = &v9;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  return stdext::unchecked_uninitialized_copy<unsigned char *,unsigned char *,std::allocator<unsigned char>>(
           _Firsta,
           _Last,
           _Dest,
           _Al);
}

