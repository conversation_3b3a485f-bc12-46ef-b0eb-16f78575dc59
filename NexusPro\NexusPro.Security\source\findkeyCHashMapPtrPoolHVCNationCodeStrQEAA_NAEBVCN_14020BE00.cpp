﻿/*
 * Function: ?findkey@?$CHashMapPtrPool@HVCNationCodeStr@@@@QEAA_NAEBVCNationCodeStr@@AEAH@Z
 * Address: 0x14020BE00
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


char __fastcall CHashMapPtrPool<int,CNationCodeStr>::findkey(CHashMapPtrPool<int,CNationCodeStr> *this, CNationCodeStr *pkData, int *kKey)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  std::pair<int const ,CNationCodeStr *> *v5; // rax@6
  __int64 v7; // [sp+0h] [bp-128h]@1
  stdext::_Hash<stdext::_Hmap_traits<int,CNationCodeStr *,stdext::hash_compare<int,std::less<int> >,std::allocator<std::pair<int const ,CNationCodeStr *> >,0> > *v8; // [sp+20h] [bp-108h]@4
  std::list<std::pair<int const ,CNationCodeStr *>,std::allocator<std::pair<int const ,CNationCodeStr *> > >::_Const_iterator<0> _Right; // [sp+38h] [bp-F0h]@4
  std::list<std::pair<int const ,CNationCodeStr *>,std::allocator<std::pair<int const ,CNationCodeStr *> > >::_Const_iterator<0> v10; // [sp+68h] [bp-C0h]@4
  std::pair<int,CNationCodeStr *> v11; // [sp+98h] [bp-90h]@6
  std::list<std::pair<int const ,CNationCodeStr *>,std::allocator<std::pair<int const ,CNationCodeStr *> > >::_Iterator<0> result; // [sp+B8h] [bp-70h]@4
  std::list<std::pair<int const ,CNationCodeStr *>,std::allocator<std::pair<int const ,CNationCodeStr *> > >::_Iterator<0> v13; // [sp+D0h] [bp-58h]@4
  char v14; // [sp+E8h] [bp-40h]@7
  __int64 v15; // [sp+F0h] [bp-38h]@4
  std::list<std::pair<int const ,CNationCodeStr *>,std::allocator<std::pair<int const ,CNationCodeStr *> > >::_Iterator<0> *v16; // [sp+F8h] [bp-30h]@4
  std::list<std::pair<int const ,CNationCodeStr *>,std::allocator<std::pair<int const ,CNationCodeStr *> > >::_Const_iterator<0> *__that; // [sp+100h] [bp-28h]@4
  std::list<std::pair<int const ,CNationCodeStr *>,std::allocator<std::pair<int const ,CNationCodeStr *> > >::_Iterator<0> *v18; // [sp+108h] [bp-20h]@4
  std::list<std::pair<int const ,CNationCodeStr *>,std::allocator<std::pair<int const ,CNationCodeStr *> > >::_Const_iterator<0> *v19; // [sp+110h] [bp-18h]@4
  CHashMapPtrPool<int,CNationCodeStr> *v20; // [sp+130h] [bp+8h]@1
  CNationCodeStr *rhs; // [sp+138h] [bp+10h]@1
  int *v22; // [sp+140h] [bp+18h]@1

  v22 = kKey;
  rhs = pkData;
  v20 = this;
  v3 = &v7;
  for (signed __int64 i = 72; i > 0; --i)
  {
    *(DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v15 = -2i64;
  v8 = (stdext::_Hash<stdext::_Hmap_traits<int,CNationCodeStr *,stdext::hash_compare<int,std::less<int> >,std::allocator<std::pair<int const ,CNationCodeStr *> >,0> > *)&v20->m_mapData._Myfirstiter;
  v16 = stdext::_Hash<stdext::_Hmap_traits<int,CNationCodeStr *,stdext::hash_compare<int,std::less<int>>,std::allocator<std::pair<int const,CNationCodeStr *>>,0>>::end(
          (stdext::_Hash<stdext::_Hmap_traits<int,CNationCodeStr *,stdext::hash_compare<int,std::less<int> >,std::allocator<std::pair<int const ,CNationCodeStr *> >,0> > *)&v20->m_mapData._Myfirstiter,
          &result);
  __that = (std::list<std::pair<int const ,CNationCodeStr *>,std::allocator<std::pair<int const ,CNationCodeStr *> > >::_Const_iterator<0> *)v16;
  std::list<std::pair<int const,CNationCodeStr *>,std::allocator<std::pair<int const,CNationCodeStr *>>>::_Const_iterator<0>::_Const_iterator<0>(
    &_Right,
    (std::list<std::pair<int const ,CNationCodeStr *>,std::allocator<std::pair<int const ,CNationCodeStr *> > >::_Const_iterator<0> *)&v16->_Mycont);
  std::list<std::pair<int const,CNationCodeStr *>,std::allocator<std::pair<int const,CNationCodeStr *>>>::_Iterator<0>::~_Iterator<0>(&result);
  v18 = stdext::_Hash<stdext::_Hmap_traits<int,CNationCodeStr *,stdext::hash_compare<int,std::less<int>>,std::allocator<std::pair<int const,CNationCodeStr *>>,0>>::begin(
          v8,
          &v13);
  v19 = (std::list<std::pair<int const ,CNationCodeStr *>,std::allocator<std::pair<int const ,CNationCodeStr *> > >::_Const_iterator<0> *)v18;
  std::list<std::pair<int const,CNationCodeStr *>,std::allocator<std::pair<int const,CNationCodeStr *>>>::_Const_iterator<0>::_Const_iterator<0>(
    &v10,
    (std::list<std::pair<int const ,CNationCodeStr *>,std::allocator<std::pair<int const ,CNationCodeStr *> > >::_Const_iterator<0> *)&v18->_Mycont);
  std::list<std::pair<int const,CNationCodeStr *>,std::allocator<std::pair<int const,CNationCodeStr *>>>::_Iterator<0>::~_Iterator<0>(&v13);
  while ( std::list<std::pair<int const,CNationCodeStr *>,std::allocator<std::pair<int const,CNationCodeStr *>>>::_Const_iterator<0>::operator!=(
            &v10,
            &_Right) )
  {
    v5 = std::list<std::pair<int const,CNationCodeStr *>,std::allocator<std::pair<int const,CNationCodeStr *>>>::_Const_iterator<0>::operator*(&v10);
    std::pair<int,CNationCodeStr *>::pair<int,CNationCodeStr *>(&v11, v5);
    if ( operator==(v11.second, rhs) )
    {
      *v22 = v11.first;
      v14 = 1;
      std::list<std::pair<int const,CNationCodeStr *>,std::allocator<std::pair<int const,CNationCodeStr *>>>::_Const_iterator<0>::~_Const_iterator<0>(&v10);
      std::list<std::pair<int const,CNationCodeStr *>,std::allocator<std::pair<int const,CNationCodeStr *>>>::_Const_iterator<0>::~_Const_iterator<0>(&_Right);
      return v14;
    }
    std::list<std::pair<int const,CNationCodeStr *>,std::allocator<std::pair<int const,CNationCodeStr *>>>::_Const_iterator<0>::operator++(&v10);
  }
  std::list<std::pair<int const,CNationCodeStr *>,std::allocator<std::pair<int const,CNationCodeStr *>>>::_Const_iterator<0>::~_Const_iterator<0>(&v10);
  std::list<std::pair<int const,CNationCodeStr *>,std::allocator<std::pair<int const,CNationCodeStr *>>>::_Const_iterator<0>::~_Const_iterator<0>(&_Right);
  return 0;
}


