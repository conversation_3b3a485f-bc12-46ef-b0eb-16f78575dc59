﻿/*
 * Function: ?SetBlock@_mon_block@@QEAA_NPEAU_mon_block_fld@@PEAVCMapData@@PEAPEAU_dummy_position@@@Z
 * Address: 0x140189E60
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// External symbol declarations for decompiled code
extern "C" ULONG_PTR _security_cookie;



char __fastcall _mon_block::SetBlock(_mon_block *this, _mon_block_fld *pBlkRec, CMapData *pMap, _dummy_position **ppDumPos)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  size_t v6; // rax@4
  __int64 v8; // [sp+0h] [bp-78h]@1
  char Dest; // [sp+28h] [bp-50h]@4
  unsigned int j; // [sp+54h] [bp-24h]@7
  unsigned __int64 v11; // [sp+60h] [bp-18h]@4
  _mon_block *v12; // [sp+80h] [bp+8h]@1
  _mon_block_fld *v13; // [sp+88h] [bp+10h]@1
  _dummy_position **v14; // [sp+98h] [bp+20h]@1

  v14 = ppDumPos;
  v13 = pBlkRec;
  v12 = this;
  v4 = &v8;
  for (signed __int64 i = 28; i > 0; --i)
  {
    *(DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v11 = (unsigned __int64)&v8 ^ _security_cookie;
  v12->m_pBlkRec = pBlkRec;
  v12->m_pMap = pMap;
  sprintf(&Dest, "%sbarea", pMap->m_pMapSet->m_strCode);
  v6 = strlen_0(&Dest);
  v12->m_bBossBlock = !strncmp(v13->m_strCode, &Dest, v6);
  for ( j = 0; j < v13->m_dwDummyNum; ++j )
    v12->m_pDumPos[j] = v14[j];
  return 1;
}


