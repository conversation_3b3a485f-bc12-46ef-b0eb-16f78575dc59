﻿/*
 * Function: ?TrunkCreateCostIsFreeRequest@CNetworkEX@@AEAA_NHPEAD@Z
 * Address: 0x1401D63E0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


char __fastcall CNetworkEX::TrunkCreateCostIsFreeRequest(CNetworkEX *this, int n, char *pBuf)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@6
  __int64 v6; // [sp+0h] [bp-88h]@1
  CPlayer *v7; // [sp+30h] [bp-58h]@4
  char szMsg; // [sp+44h] [bp-44h]@7
  char pbyType; // [sp+64h] [bp-24h]@7
  char v10; // [sp+65h] [bp-23h]@7

  v3 = &v6;
  for()
{
    *(DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v7 = &g_Player + n;
  if()
{
    szMsg = CPlayer::pc_TrunkCreateCostIsFreeRequest(v7);
    pbyType = 34;
    v10 = 25;
    CNetProcess::LoadSendMsg(unk_1414F2088, v7->m_ObjID.m_wIndex, &pbyType, &szMsg, 1u);
    result = 1;
  }
  else
  {
    result = 1;
  }
  return result;
}


