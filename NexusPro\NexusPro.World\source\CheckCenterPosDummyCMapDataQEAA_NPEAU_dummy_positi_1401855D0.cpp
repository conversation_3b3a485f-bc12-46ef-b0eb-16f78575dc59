﻿/*
 * Function: ?CheckCenterPosDummy@CMapData@@QEAA_NPEAU_dummy_position@@@Z
 * Address: 0x1401855D0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// External symbol declarations
extern "C" IMAGE_DOS_HEADER __ImageBase;
extern "C" DWORD off_14000003C;
extern "C" void* _delayLoadHelper2(ImgDelayDescr* pidd, void** ppfnIATEntry);
extern struct EqSukData { void* pwszEpSuk; } EqSukList[16];
extern void MyMessageBox(const char* title, const char* message);


char __fastcall CMapData::CheckCenterPosDummy(CMapData *this, _dummy_position *pPos)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int32 v4; // eax@7
  char result; // al@8
  __int64 v6; // [sp+0h] [bp-68h]@1
  float v7[7]; // [sp+28h] [bp-40h]@6
  int j; // [sp+44h] [bp-24h]@4
  CExtDummy *v9; // [sp+48h] [bp-20h]@7
  CLevel *v10; // [sp+50h] [bp-18h]@9
  CMapData *v11; // [sp+70h] [bp+8h]@1
  _dummy_position *v12; // [sp+78h] [bp+10h]@1

  v12 = pPos;
  v11 = this;
  v2 = &v6;
  for (signed __int64 i = 24; i > 0; --i)
  {
    *(DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  for ( j = 0; j < 3; ++j )
    v7[j] = (float)((pPos->m_zLocalMin[j] + pPos->m_zLocalMax[j]) / 2);
  v4 = pPos->m_wLineIndex;
  v9 = &v11->m_Dummy;
  if ( CExtDummy::GetWorldFromLocal(&v11->m_Dummy, (float (*)[3])pPos->m_fCenterPos, v4, v7) )
  {
    v10 = &v11->m_Level;
    v12->m_fCenterPos[1] = CLevel::GetFirstYpos(&v11->m_Level, v12->m_fCenterPos, v12->m_fMin, v12->m_fMax);
    v12->m_bPosAble = 1;
    if ( v12->m_fCenterPos[1] == -65535.0 )
    {
      CLogFile::Write(&stru_1799C8F30, "CheckPosDummy: Map(%s), Dum(%s)", v11->m_pMapSet->m_strCode, v12);
      v12->m_bPosAble = 0;
    }
    v12->m_fDirection[1] = v12->m_fCenterPos[1];
    result = 1;
  }
  else
  {
    MyMessageBox("CMapData Error", "CheckCenterPosDummy map:%s, dummy:%s", v11->m_pMapSet->m_strCode, v12);
    result = 0;
  }
  return result;
}


