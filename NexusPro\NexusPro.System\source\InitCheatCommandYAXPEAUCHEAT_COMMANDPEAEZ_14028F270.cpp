﻿/*
 * Function: ?InitCheatCommand@@YAXPEAUCHEAT_COMMAND@@PEAE@Z
 * Address: 0x14028F270
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// External symbol declarations for decompiled code
extern "C" ULONG_PTR _security_cookie;



void __fastcall InitCheatCommand(CHEAT_COMMAND *pCmdList, char *byCommandSizeList)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char v4; // al@6
  unsigned int v5; // eax@7
  __int64 v6; // [sp+0h] [bp-F8h]@1
  int j; // [sp+30h] [bp-C8h]@4
  const char **v8; // [sp+38h] [bp-C0h]@5
  char Dest; // [sp+50h] [bp-A8h]@7
  unsigned __int64 v10; // [sp+E0h] [bp-18h]@4
  CHEAT_COMMAND *v11; // [sp+100h] [bp+8h]@1
  char *v12; // [sp+108h] [bp+10h]@1

  v12 = byCommandSizeList;
  v11 = pCmdList;
  v2 = &v6;
  for (signed __int64 i = 60; i > 0; --i)
  {
    *(DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v10 = (unsigned __int64)&v6 ^ _security_cookie;
  for ( j = 0; ; ++j )
  {
    v8 = &v11[j].pwszCommand;
    if ( !*v8 )
      break;
    v4 = strlen_0(*v8);
    v12[j] = v4;
  }
  v5 = GetKorLocalTime();
  sprintf(&Dest, "..\\ZoneServerLog\\ServiceLog\\Cheat%d.log", v5);
  CLogFile::SetWriteLogFile(&s_logCheat, &Dest, 1, 0, 1, 1);
}


