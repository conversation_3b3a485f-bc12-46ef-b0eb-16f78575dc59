﻿/*
 * Function: ?Init<PERSON>en@CDummyDraw@@SAXXZ
 * Address: 0x14019D050
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void CDummyDraw::InitPen(void)
{
  __int64 *v0; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v2; // [sp+0h] [bp-78h]@1
  HPEN v3; // [sp+20h] [bp-58h]@6
  COLORREF color; // [sp+38h] [bp-40h]@4
  int v5; // [sp+3Ch] [bp-3Ch]@4
  int v6; // [sp+40h] [bp-38h]@4
  int v7; // [sp+44h] [bp-34h]@4
  int v8; // [sp+48h] [bp-30h]@4
  int v9; // [sp+4Ch] [bp-2Ch]@4
  int v10; // [sp+50h] [bp-28h]@4
  int v11; // [sp+54h] [bp-24h]@4
  int v12; // [sp+58h] [bp-20h]@4
  int v13; // [sp+5Ch] [bp-1Ch]@4
  int j; // [sp+64h] [bp-14h]@4

  v0 = &v2;
  for (signed __int64 i = 28; i > 0; --i)
  {
    *(DWORD *)v0 = -858993460;
    v0 = (__int64 *)((char *)v0 + 4);
  }
  color = 7883506;
  v5 = 0xFFFFFF;
  v6 = 5173094;
  v7 = 1355506;
  v8 = 44546;
  v9 = 15878752;
  v10 = 57540;
  v11 = 13107455;
  v12 = 0;
  v13 = 328965;
  for ( j = 0; j < 10; ++j )
  {
    v3 = CreatePen(0, 1, *(&color + j));
    if ( v3 )
      CDummyDraw::s_hPen[j] = v3;
  }
  v3 = CreatePen(0, 1, 0xFF05FFu);
  if ( v3 )
    CDummyDraw::s_hDirection = v3;
}


