﻿/*
 * Function: ?_UpdateNewEconomy@@YAXPEAU_economy_calc_data@@@Z
 * Address: 0x1402A48E0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// External symbol declarations are now in NexusProCommon.h and RFOnlineClasses.h

void __fastcall _UpdateNewEconomy(_economy_calc_data *pData)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  float v3; // xmm0_4@6
  int v4; // eax@11
  float v5; // xmm0_4@11
  float v6; // xmm0_4@21
  float v7; // xmm0_4@23
  float v8; // xmm0_4@31
  __int64 v9; // [sp+0h] [bp-B8h]@1
  float v10; // [sp+28h] [bp-90h]@11
  float v11; // [sp+2Ch] [bp-8Ch]@14
  float v12; // [sp+30h] [bp-88h]@14
  float pfAvrTradeMoney[7]; // [sp+58h] [bp-60h]@6
  int nRaceCode; // [sp+74h] [bp-44h]@4
  float v15; // [sp+78h] [bp-40h]@14
  float v16; // [sp+88h] [bp-30h]@25
  float v17; // [sp+8Ch] [bp-2Ch]@28
  float v18; // [sp+90h] [bp-28h]@28
  float v19; // [sp+A4h] [bp-14h]@21
  float v20; // [sp+A8h] [bp-10h]@23
  float v21; // [sp+ACh] [bp-Ch]@28
  _economy_calc_data *v22; // [sp+C0h] [bp+8h]@1

  v22 = pData;
  v1 = &v9;
  for ( i = 44i64; i; --i )
  {
    *(DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  for ( nRaceCode = 0; nRaceCode < 3; ++nRaceCode )
  {
    v3 = sqrt_0(v22->dTradeGold[nRaceCode] + v22->dTradeDalant[nRaceCode] / 1000.0 + 1.0);
    pfAvrTradeMoney[nRaceCode] = v3;
    if ( pfAvrTradeMoney[nRaceCode] < 1.0 )
      pfAvrTradeMoney[nRaceCode] = FLOAT_1_0;
  }
  for ( nRaceCode = 0; nRaceCode < 3; ++nRaceCode )
  {
    v4 = _CalcPayExgRatePerRace(pfAvrTradeMoney, nRaceCode);
    v22->out_fPayExgRate[nRaceCode] = (float)v4;
    v5 = v22->dTradeGold[nRaceCode] + v22->dTradeDalant[nRaceCode] / v22->out_fPayExgRate[nRaceCode];
    *(&v10 + nRaceCode) = v5;
    if ( *(&v10 + nRaceCode) < 1.0 )
      *(&v10 + nRaceCode) = FLOAT_1_0;
  }
  v15 = (float)((float)(v10 + v11) + v12) / 3.0;
  if ( v15 < 1.0 )
    v15 = FLOAT_1_0;
  for ( nRaceCode = 0; nRaceCode < 3; ++nRaceCode )
  {
    v22->out_wEconomyGuide[nRaceCode] = (signed int)ffloor((float)((float)(*(&v10 + nRaceCode) / v15) * 100.0) + 0.5);
    v22->out_fTexRate[nRaceCode] = (float)((float)(*(&v10 + nRaceCode) / v15) * (float)EqSukList[14].nIndex) / 100.0;
    v22->out_dwTexRate[nRaceCode] = (signed int)ffloor(v22->out_fTexRate[nRaceCode] * 10000.0);
  }
  for ( nRaceCode = 0; nRaceCode < 3; ++nRaceCode )
  {
    v6 = v22->dOreCutCount[nRaceCode][0] * 1.5
       + v22->dOreCutCount[nRaceCode][1] * 2.0
       + v22->dOreCutCount[nRaceCode][2] * 2.5;
    v19 = v6;
    if ( v6 <= 0.0 )
      v19 = FLOAT_1_0;
    v7 = v22->dOreMineCount[nRaceCode][0] * 1.5
       + v22->dOreMineCount[nRaceCode][1] * 2.0
       + v22->dOreMineCount[nRaceCode][2] * 2.5;
    v20 = v7;
    if ( v7 <= 0.0 )
      v20 = FLOAT_1_0;
    *(&v16 + nRaceCode) = (float)(v19 / v20) + (float)((float)S((DWORD)(EqSukList[15].pwszEpSuk) / 10000.0);
    if ( *(&v16 + nRaceCode) > 1.0 )
      *(&v16 + nRaceCode) = FLOAT_1_0;
  }
  v21 = (float)((float)(v16 + v17) + v18) / 3.0;
  if ( v21 <= 0.0 )
  {
    for ( nRaceCode = 0; nRaceCode < 3; ++nRaceCode )
      v22->out_fOreRate[nRaceCode] = FLOAT_1_0;
  }
  else
  {
    for ( nRaceCode = 0; nRaceCode < 3; ++nRaceCode )
    {
      v8 = (float)((float)(*(&v16 + nRaceCode) / v21) * 0.25) + *(&v16 + nRaceCode) * 0.75;
      v22->out_fOreRate[nRaceCode] = v8;
      if ( v22->out_fOreRate[nRaceCode] > 1.0 )
        v22->out_fOreRate[nRaceCode] = FLOAT_1_0;
    }
  }
}


