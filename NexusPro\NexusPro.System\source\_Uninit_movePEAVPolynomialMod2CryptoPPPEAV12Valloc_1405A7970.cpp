﻿/*
 * Function: ??$_Uninit_move@PEAVPolynomialMod2@CryptoPP@@PEAV12@V?$allocator@VPolynomialMod2@CryptoPP@@@std@@U_Undefined_move_tag@4@@std@@YAPEAVPolynomialMod2@CryptoPP@@PEAV12@00AEAV?$allocator@VPolynomialMod2@CryptoPP@@@0@U_Undefined_move_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x1405A7970
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


int std::_Uninit_move<CryptoPP::PolynomialMod2 *,CryptoPP::PolynomialMod2 *,std::allocator<CryptoPP::PolynomialMod2>,std::_Undefined_move_tag>()
{
  return stdext::unchecked_uninitialized_copy<CryptoPP::PolynomialMod2 *,CryptoPP::PolynomialMod2 *,std::allocator<CryptoPP::PolynomialMod2>>();
}

