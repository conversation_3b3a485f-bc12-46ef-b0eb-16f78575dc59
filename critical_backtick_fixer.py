#!/usr/bin/env python3
"""
Critical Backtick Fixer for NexusPro Economy Module
Fixes the specific backtick (0x60) character issues causing build failures
"""

import os
import re
import sys
import time
from pathlib import Path

class CriticalBacktickFixer:
    def __init__(self):
        self.fixes_applied = 0
        self.files_processed = 0
        self.errors_found = 0
        
    def fix_backtick_issues(self, content: str) -> str:
        """Fix critical backtick character issues"""
        original_content = content
        
        # Remove all backtick characters (0x60)
        content = content.replace('`', '')
        
        # Fix specific patterns that cause the errors we saw
        fixes = [
            # Fix malformed function signatures with backticks removed
            (r'(\w+::\w+)\(([^)]*)\s*([^)]*)\)', r'\1(\2\3)'),
            
            # Fix Concurrency namespace issues - add proper includes
            (r'(\w+)\s*Concurrency\s*::', r'\1Concurrency::'),
            
            # Fix malformed dtor signatures
            (r'(\w+)\s*dtor\$(\d+)\s*([^{]*)', r'void dtor_\2\3'),
            
            # Fix malformed template syntax
            (r'(\w+)<([^>]*)>\s*([^{]*)', r'\1<\2> \3'),
            
            # Fix missing semicolons before function bodies
            (r'(\w+\s*\([^)]*\))\s*\n\s*\{', r'\1;\n{'),
            
            # Fix malformed for loops
            (r'for\s*\(\s*(\w+)\s*=\s*(\d+)i64\s*;\s*\1\s*;\s*--\1\s*\)', 
             r'for (signed __int64 \1 = \2; \1 > 0; --\1)'),
            
            # Fix void function declarations
            (r'(\w+)\s*this\s*use\s*of\s*\'void\'\s*is\s*not\s*valid', r'\1'),
            
            # Fix missing constant declarations
            (r'(\w+)\s*constant\s*([^;]*)', r'\1 \2'),
        ]
        
        for pattern, replacement in fixes:
            content = re.sub(pattern, replacement, content, flags=re.MULTILINE)
        
        return content
    
    def add_required_includes(self, content: str) -> str:
        """Add required includes for Concurrency namespace"""
        if 'Concurrency::' in content and '#include <ppl.h>' not in content:
            # Find the include section
            include_pos = content.find('#include "../../NexusPro.Core/headers/RFOnlineClasses.h"')
            if include_pos >= 0:
                insert_pos = content.find('\n', include_pos) + 1
                new_includes = '''
// Required includes for Concurrency namespace
#include <ppl.h>
#include <ppltasks.h>
#include <concurrent_vector.h>

'''
                content = content[:insert_pos] + new_includes + content[insert_pos:]
        
        return content
    
    def fix_malformed_signatures(self, content: str) -> str:
        """Fix malformed function signatures that cause C2018 errors"""
        lines = content.split('\n')
        fixed_lines = []
        
        for line in lines:
            # Skip lines that are clearly malformed beyond repair
            if 'unknown character' in line or len(line) > 500:
                # Comment out problematic lines
                fixed_lines.append('// ' + line + ' // MALFORMED - COMMENTED OUT')
                continue
            
            # Fix specific patterns
            line = re.sub(r'(\w+)\s*`([^`]*)`\s*(\w+)', r'\1 \3', line)
            line = re.sub(r'dtor\$(\d+)', r'dtor_\1', line)
            line = re.sub(r'(\w+)\s*constant\s*', r'\1 ', line)
            
            fixed_lines.append(line)
        
        return '\n'.join(fixed_lines)
    
    def fix_file(self, file_path: Path) -> bool:
        """Fix a single source file"""
        try:
            if not file_path.exists() or file_path.suffix not in ['.cpp', '.h']:
                return False
            
            content = file_path.read_text(encoding='utf-8', errors='ignore')
            original_content = content
            
            # Apply all fixes
            content = self.fix_backtick_issues(content)
            content = self.add_required_includes(content)
            content = self.fix_malformed_signatures(content)
            
            # Write back if changed
            if content != original_content:
                file_path.write_text(content, encoding='utf-8')
                self.fixes_applied += 1
                return True
            
        except Exception as e:
            print(f"Error fixing {file_path}: {e}")
            self.errors_found += 1
        
        return False
    
    def fix_economy_module(self) -> dict:
        """Fix the Economy module specifically"""
        stats = {'files_processed': 0, 'files_fixed': 0, 'errors': 0}
        
        economy_path = Path('NexusPro/NexusPro.Economy/source')
        if not economy_path.exists():
            print(f"Economy source path does not exist: {economy_path}")
            return stats
        
        print(f"🔧 Fixing critical backtick issues in Economy module...")
        
        # Process all .cpp files
        for cpp_file in economy_path.glob('*.cpp'):
            stats['files_processed'] += 1
            if self.fix_file(cpp_file):
                stats['files_fixed'] += 1
            
            # Progress indicator
            if stats['files_processed'] % 50 == 0:
                print(f"  Processed {stats['files_processed']} files, fixed {stats['files_fixed']}")
        
        return stats

def main():
    """Main function"""
    fixer = CriticalBacktickFixer()
    
    start_time = time.time()
    
    print("🚨 Critical Backtick Fixer for NexusPro Economy")
    print("=" * 50)
    print("Fixing specific issues causing C2018 'unknown character 0x60' errors...")
    
    # Fix the Economy module
    stats = fixer.fix_economy_module()
    
    elapsed_time = time.time() - start_time
    
    print(f"\n🎉 Critical Backtick Fix Complete!")
    print(f"📊 Statistics:")
    print(f"   • Files Processed: {stats['files_processed']}")
    print(f"   • Files Fixed: {stats['files_fixed']}")
    print(f"   • Errors: {stats['errors']}")
    print(f"   • Processing Time: {elapsed_time:.2f} seconds")
    
    if stats['files_processed'] > 0:
        success_rate = (stats['files_fixed'] / stats['files_processed']) * 100
        rate = stats['files_processed'] / elapsed_time
        print(f"   • Success Rate: {success_rate:.1f}%")
        print(f"   • Processing Rate: {rate:.1f} files/second")
    
    print(f"\n✅ Ready for build testing!")
    return 0

if __name__ == "__main__":
    sys.exit(main())
