# NexusPro Decompiled Code Syntax Fix Automation
# PowerShell script to fix common decompiled code syntax issues

param(
    [Parameter(Mandatory=$true)]
    [string]$Target  # Module name or "all"
)

function Fix-DecompiledCodeSyntax {
    param(
        [string]$FilePath
    )
    
    $fixesApplied = 0
    
    try {
        $content = Get-Content -Path $FilePath -Raw -Encoding UTF8
        $originalContent = $content
        
        # Fix 1: Replace decompiled type names with standard types
        $content = $content -replace '\b_DWORD\b', 'DWORD'
        $content = $content -replace '\b_BYTE\b', 'BYTE'
        $content = $content -replace '\b_WORD\b', 'WORD'
        $content = $content -replace '\b_QWORD\b', 'QWORD'
        
        # Fix 2: Fix malformed for loops with i64 suffix
        $content = $content -replace 'for\s*\(\s*(\w+)\s*=\s*(\d+)i64\s*;\s*\1\s*;\s*--\1\s*\)', 'for (signed __int64 $1 = $2; $1 > 0; --$1)'
        
        # Fix 3: Fix incomplete function signatures with backticks
        $content = $content -replace '(\w+::\w+)\(([^)]*)`([^`]*)`([^)]*)\)', '$1($2$4)'
        
        # Fix 4: Fix LODWORD macro usage
        $content = $content -replace '\bLODWORD\s*\(([^)]+)\)', '((DWORD)($1))'
        
        # Fix 5: Add missing variable declarations in for loops
        $content = $content -replace 'for\s*\(\s*(\w+)\s*=\s*0\s*;\s*\1\s*<\s*(\w+)\s*;\s*\+\+\1\s*\)', 'for (int $1 = 0; $1 < $2; ++$1)'
        
        # Fix 6: Fix malformed pointer casts
        $content = $content -replace '\(\s*\*\s*\(\s*_DWORD\s*\*\s*\)', '(*(DWORD *)'
        
        # Fix 7: Fix missing semicolons before opening braces (simple cases)
        $content = $content -replace '(\w+\s*\([^)]*\))\s*\r?\n\s*\{', '$1;$2{'
        
        # Fix 8: Add external symbol declarations if missing
        if ($content -match '_ImageBase' -and $content -notmatch 'extern.*__ImageBase') {
            $includePos = $content.IndexOf('#include "../../NexusPro.Core/headers/RFOnlineClasses.h"')
            if ($includePos -ge 0) {
                $insertPos = $content.IndexOf("`n", $includePos) + 1
                $content = $content.Insert($insertPos, "`n// External symbol declarations`nextern `"C`" IMAGE_DOS_HEADER __ImageBase;`n")
            }
        }
        
        if ($content -match 'EqSukList' -and $content -notmatch 'extern.*EqSukList') {
            $includePos = $content.IndexOf('#include "../../NexusPro.Core/headers/RFOnlineClasses.h"')
            if ($includePos -ge 0) {
                $insertPos = $content.IndexOf("`n", $includePos) + 1
                $content = $content.Insert($insertPos, "`nextern struct EqSukData { void* pwszEpSuk; } EqSukList[16];`n")
            }
        }
        
        if ($content -match 'MyMessageBox' -and $content -notmatch 'extern.*MyMessageBox') {
            $includePos = $content.IndexOf('#include "../../NexusPro.Core/headers/RFOnlineClasses.h"')
            if ($includePos -ge 0) {
                $insertPos = $content.IndexOf("`n", $includePos) + 1
                $content = $content.Insert($insertPos, "`nextern void MyMessageBox(const char* title, const char* message);`n")
            }
        }
        
        if ($content -match '_delayLoadHelper2' -and $content -notmatch 'extern.*_delayLoadHelper2') {
            $includePos = $content.IndexOf('#include "../../NexusPro.Core/headers/RFOnlineClasses.h"')
            if ($includePos -ge 0) {
                $insertPos = $content.IndexOf("`n", $includePos) + 1
                $content = $content.Insert($insertPos, "`nextern `"C`" void* _delayLoadHelper2(ImgDelayDescr* pidd, void** ppfnIATEntry);`n")
            }
        }
        
        # Count fixes applied
        if ($content -ne $originalContent) {
            Set-Content -Path $FilePath -Value $content -Encoding UTF8
            $fixesApplied = 1
        }
        
    } catch {
        Write-Warning "Error processing file $FilePath`: $_"
        return 0
    }
    
    return $fixesApplied
}

function Fix-Module {
    param(
        [string]$ModulePath
    )
    
    $stats = @{
        FilesProcessed = 0
        FilesFixed = 0
        Errors = 0
    }
    
    if (-not (Test-Path $ModulePath)) {
        Write-Warning "Module path does not exist: $ModulePath"
        return $stats
    }
    
    # Process source files
    $sourceDir = Join-Path $ModulePath "source"
    if (Test-Path $sourceDir) {
        $cppFiles = Get-ChildItem -Path $sourceDir -Filter "*.cpp" -File
        foreach ($file in $cppFiles) {
            $stats.FilesProcessed++
            $fixed = Fix-DecompiledCodeSyntax -FilePath $file.FullName
            $stats.FilesFixed += $fixed
            
            if ($stats.FilesProcessed % 50 -eq 0) {
                Write-Host "   Processed $($stats.FilesProcessed) files..." -ForegroundColor Yellow
            }
        }
    }
    
    # Process header files
    $headersDir = Join-Path $ModulePath "headers"
    if (Test-Path $headersDir) {
        $hFiles = Get-ChildItem -Path $headersDir -Filter "*.h" -File
        foreach ($file in $hFiles) {
            $stats.FilesProcessed++
            $fixed = Fix-DecompiledCodeSyntax -FilePath $file.FullName
            $stats.FilesFixed += $fixed
        }
    }
    
    return $stats
}

# Main execution
$startTime = Get-Date
$nexusProPath = "NexusPro"

if (-not (Test-Path $nexusProPath)) {
    Write-Error "NexusPro directory not found"
    exit 1
}

$totalStats = @{
    FilesProcessed = 0
    FilesFixed = 0
    Errors = 0
}

if ($Target.ToLower() -eq "all") {
    # Fix all modules
    $modules = Get-ChildItem -Path $nexusProPath -Directory | Where-Object { $_.Name.StartsWith("NexusPro.") }
    
    Write-Host "🔧 Starting decompiled code syntax fixes for $($modules.Count) modules..." -ForegroundColor Green
    
    foreach ($module in $modules) {
        Write-Host "`n📁 Processing $($module.Name)..." -ForegroundColor Cyan
        $stats = Fix-Module -ModulePath $module.FullName
        
        $totalStats.FilesProcessed += $stats.FilesProcessed
        $totalStats.FilesFixed += $stats.FilesFixed
        $totalStats.Errors += $stats.Errors
        
        Write-Host "   ✅ $($stats.FilesFixed)/$($stats.FilesProcessed) files fixed" -ForegroundColor Green
        if ($stats.Errors -gt 0) {
            Write-Host "   ⚠️  $($stats.Errors) errors encountered" -ForegroundColor Yellow
        }
    }
} else {
    # Fix specific module
    $modulePath = Join-Path $nexusProPath $Target
    if (-not (Test-Path $modulePath)) {
        Write-Error "Module $Target not found"
        exit 1
    }
    
    Write-Host "🔧 Starting decompiled code syntax fixes for $Target..." -ForegroundColor Green
    $totalStats = Fix-Module -ModulePath $modulePath
}

$elapsedTime = (Get-Date) - $startTime

Write-Host "`n🎉 Decompiled Code Syntax Fix Complete!" -ForegroundColor Green
Write-Host "📊 Statistics:" -ForegroundColor White
Write-Host "   • Files Processed: $($totalStats.FilesProcessed)" -ForegroundColor White
Write-Host "   • Files Fixed: $($totalStats.FilesFixed)" -ForegroundColor White
Write-Host "   • Errors: $($totalStats.Errors)" -ForegroundColor White
Write-Host "   • Processing Time: $($elapsedTime.TotalSeconds.ToString('F2')) seconds" -ForegroundColor White

if ($totalStats.FilesProcessed -gt 0) {
    $successRate = ($totalStats.FilesFixed / $totalStats.FilesProcessed * 100)
    Write-Host "   • Success Rate: $($successRate.ToString('F1'))%" -ForegroundColor White
}

Write-Host "`n✅ Ready for build testing!" -ForegroundColor Green
