﻿/*
 * Function: ?dtor_0@?0??wait_for_one@agent@Concurrency@@SAX_KPEAPEAV12@AEAW4agent_status@2@AEA_KI@Z@4HA_0
 * Address: 0x1406290F0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Required includes for Concurrency namespace
#include <ppl.h>
#include <ppltasks.h>
#include <concurrent_vector.h>



void __fastcallConcurrency::agent::wait_for_one'::1'::dtor_0(__int64 a1, __int64 a2)
{
  CryptoPP::PolynomialMod2::~PolynomialMod2((void *)(a2 + 64));
}

