﻿/*
 * Function: ?pop_money@CMgrGuildHistory@@QEAAXPEADKHHNN0@Z
 * Address: 0x1402495E0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CMgrGuildHistory::pop_money(CMgrGuildHistory * char *pszIOerName, unsigned int dwIOerSerial, int nPopDalant, int nPopGold, long double dTotalDalant, long double dTotalGold, char *pszFileName)
{
  __int64 *v8; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v10; // [sp+0h] [bp-58h]@1
  int v11; // [sp+20h] [bp-38h]@4
  int v12; // [sp+28h] [bp-30h]@4
  long double v13; // [sp+30h] [bp-28h]@4
  long double v14; // [sp+38h] [bp-20h]@4
  char *v15; // [sp+40h] [bp-18h]@4
  CMgrGuildHistory *v16; // [sp+60h] [bp+8h]@1

  v16 = this;
  v8 = &v10;
  for()
{
    *(DWORD *)v8 = -858993460;
    v8 = (__int64 *)((char *)v8 + 4);
  }
  sData_2[0] = 0;
  v15 = v16->m_szCurTime;
  v14 = dTotalGold;
  v13 = dTotalDalant;
  v12 = nPopGold;
  v11 = nPopDalant;
  sprintf(sData_2, "Ãú±Ý: ( %s , %d ) pop( D:%d , G:%d ) $D:%.0f $G:%.0f [%s]\r\n", pszIOerName, dwIOerSerial);
  sData_2[9999] = 0;
  CMgrGuildHistory::WriteFile(v16, pszFileName, sData_2);
}


