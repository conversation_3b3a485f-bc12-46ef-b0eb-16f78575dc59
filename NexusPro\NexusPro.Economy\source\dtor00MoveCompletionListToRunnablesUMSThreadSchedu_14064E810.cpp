﻿/*
 * Function: ?dtor_0@?0??MoveCompletionListToRunnables@UMSThreadScheduler@details@Concurrency@@QEAA_NVlocation@3@@Z@4HA_1
 * Address: 0x14064E810
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Required includes for Concurrency namespace
#include <ppl.h>
#include <ppltasks.h>
#include <concurrent_vector.h>



int __fastcallConcurrency::details::UMSThreadScheduler::MoveCompletionListToRunnables'::1'::dtor_0(__int64 a1, __int64 a2)
{
  __int64 v2; // rcx@1

  v2 = *(QWORD *)(a2 + 152);
  return std::_Vector_iterator<unsigned short,std::allocator<unsigned short> >::~_Vector_iterator<unsigned short,std::allocator<unsigned short>>();
}


