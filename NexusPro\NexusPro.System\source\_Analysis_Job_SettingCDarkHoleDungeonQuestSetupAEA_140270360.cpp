﻿/*
 * Function: ?_Analysis_Job_Setting@CDarkHoleDungeonQuestSetup@@AEAA_NPEAUstrFILE@@@Z
 * Address: 0x140270360
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// External symbol declarations for decompiled code
extern "C" ULONG_PTR _security_cookie;



bool __fastcall CDarkHoleDungeonQuestSetup::_Analysis_Job_Setting(CDarkHoleDungeonQuestSetup *this, strFILE *fstr)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  __int64 v5; // rax@9
  __int64 v6; // [sp+0h] [bp-F8h]@1
  char poutszWord; // [sp+30h] [bp-C8h]@11
  _dh_job_setup *v8; // [sp+C0h] [bp-38h]@11
  _dh_job_setup *v9; // [sp+C8h] [bp-30h]@8
  __int64 v10; // [sp+D0h] [bp-28h]@4
  _dh_job_setup *v11; // [sp+D8h] [bp-20h]@9
  unsigned __int64 v12; // [sp+E0h] [bp-18h]@4
  CDarkHoleDungeonQuestSetup *pSetup; // [sp+100h] [bp+8h]@1
  strFILE *fstra; // [sp+108h] [bp+10h]@1

  fstra = fstr;
  pSetup = this;
  v2 = &v6;
  for (signed __int64 i = 60; i > 0; --i)
  {
    *(DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v10 = -2i64;
  v12 = (unsigned __int64)&v6 ^ _security_cookie;
  if ( pSetup->m_pCurLoadQuest )
  {
    if ( pSetup->m_pCurLoadMission )
    {
      v9 = (_dh_job_setup *)operator new(0xB0ui64);
      if ( v9 )
      {
        _dh_job_setup::_dh_job_setup(v9);
        v11 = (_dh_job_setup *)v5;
      }
      else
      {
        v11 = 0;
      }
      v8 = v11;
      pSetup->m_pCurLoadMission->EmbJobSetup[pSetup->m_pCurLoadMission->nEmbJobSetupNum] = v11;
      pSetup->m_pCurLoadJob = pSetup->m_pCurLoadMission->EmbJobSetup[pSetup->m_pCurLoadMission->nEmbJobSetupNum++];
      if ( strFILE::word(fstra, &poutszWord) )
      {
        if ( strlen_0(&poutszWord) <= 0x20 )
        {
          strcpy_0(pSetup->m_pCurLoadJob->szJobTitle, &poutszWord);
          result = 1;
        }
        else
        {
          sprintf(pSetup->m_szLoadErrMsg, "job title (%s) size over .. must be set up less than %d", &poutszWord, 32i64);
          result = _false(fstra, pSetup);
        }
      }
      else
      {
        sprintf(pSetup->m_szLoadErrMsg, "job title load error");
        result = _false(fstra, pSetup);
      }
    }
    else
    {
      result = _false(fstr, pSetup);
    }
  }
  else
  {
    result = _false(fstr, pSetup);
  }
  return result;
}


