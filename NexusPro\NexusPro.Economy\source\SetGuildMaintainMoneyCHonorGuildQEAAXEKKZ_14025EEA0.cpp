﻿/*
 * Function: ?SetGuildMaintainMoney@CHonorGuild@@QEAAXEKK@Z
 * Address: 0x14025EEA0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// External symbol declarations for decompiled code
extern "C" ULONG_PTR _security_cookie;



void __usercall CHonorGuild::SetGuildMaintainMoney(CHonorGuild *this@<rcx>, char byRace@<dl>, unsigned int dwTax@<r8d>, unsigned int dwSeller@<r9d>, double a5@<xmm0>)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  int v7; // eax@12
  CMoneySupplyMgr *v8; // rax@12
  __int64 v9; // [sp+0h] [bp-A8h]@1
  char *v10; // [sp+30h] [bp-78h]@4
  int j; // [sp+38h] [bp-70h]@4
  unsigned int nAmount; // [sp+3Ch] [bp-6Ch]@7
  CGuild *v13; // [sp+40h] [bp-68h]@8
  _qry_case_in_atrade_tax v14; // [sp+58h] [bp-50h]@12
  unsigned __int64 v15; // [sp+90h] [bp-18h]@4
  CHonorGuild *v16; // [sp+B0h] [bp+8h]@1
  char v17; // [sp+B8h] [bp+10h]@1
  unsigned int v18; // [sp+C0h] [bp+18h]@1
  unsigned int v19; // [sp+C8h] [bp+20h]@1

  v19 = dwSeller;
  v18 = dwTax;
  v17 = byRace;
  v16 = this;
  v5 = &v9;
  for (signed __int64 i = 40; i > 0; --i)
{
    *(DWORD *)v5 = -*********;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v15 = (unsigned __int64)&v9 ^ _security_cookie;
  v10 = &v16->m_pCurrHonorGuild[(unsigned __int8)byRace]->byListNum;
  for ( j = 0; j < (unsigned __int8)*v10; ++j )
  {
    nAmount = (unsigned __int8)v10[47 * j + 48] * v18 / 0x64;
    if()
{
      v13 = GetGuildDataFromSerial(g_Guild, 500, *(DWORD *)&v10[47 * j + 2]);
      if()
{
        CGuild::GetTotalDalant(v13);
        a5 = a5 + (double)(signed int)nAmount;
        if()
{
          CGuild::GetTotalDalant(v13);
          a5 = 1000000000.0 - a5;
          nAmount = (signed int)floor(a5);
        }
        if()
{
          _qry_case_in_atrade_tax::_qry_case_in_atrade_tax(&v14);
          v14.byRace = v17;
          v14.in_seller = v19;
          v14.dwGuildSerial = *(DWORD *)&v10[47 * j + 2];
          *(QWORD *)&v14.out_totalgold = 0;
          a5 = 0.0;
          *(QWORD *)&v14.out_totaldalant = 0;
          v14.in_dalant = nAmount;
          v7 = _qry_case_in_atrade_tax::size(&v14);
          CMainThread::PushDQSData(&g_Main, 0xFFFFFFFF, 0, -120, &v14.byRace, v7);
          v8 = CMoneySupplyMgr::Instance();
          CMoneySupplyMgr::UpdateHonorGuildMoneyData(v8, 0, v17, nAmount);
        }
      }
    }
  }
}


