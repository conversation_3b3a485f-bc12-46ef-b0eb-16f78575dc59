﻿/*
 * Function: ?UpdateGateRewardMoneyData@CMoneySupplyMgr@@QEAAXEHPEADK@Z
 * Address: 0x14042E520
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CMoneySupplyMgr::UpdateGateRewardMoneyData(CMoneySupplyMgr *this, char byRace, int nLv, char *szClass, unsigned int nAmount)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-28h]@1
  CMoneySupplyMgr *v8; // [sp+30h] [bp+8h]@1
  const char *Str1; // [sp+48h] [bp+20h]@1

  Str1 = szClass;
  v8 = this;
  v5 = &v7;
  for()
{
    *(DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v8->m_MS_data.dwAmount[2] += nAmount;
  switch()
{
    case 30:
      ++v8->m_MS_data.ms_data[2].nLv[0];
      break;
    case 40:
      ++v8->m_MS_data.ms_data[2].nLv[1];
      break;
    case 50:
      ++v8->m_MS_data.ms_data[2].nLv[2];
      break;
    case 60:
      ++v8->m_MS_data.ms_data[2].nLv[3];
      break;
  }
  if ( !byRace )
    ++v8->m_MS_data.ms_data[2].nRace[0];
  if ( byRace == 1 )
    ++v8->m_MS_data.ms_data[2].nRace[1];
  else
    ++v8->m_MS_data.ms_data[2].nRace[2];
  if ( !strcmp_0(szClass, "BWB0") )
  {
    ++v8->m_MS_data.ms_data[2].nClass[0];
  }
  else if ( !strcmp_0(Str1, "BWF2") )
  {
    ++v8->m_MS_data.ms_data[2].nClass[4];
  }
  else if ( !strcmp_0(Str1, "BWS3") )
  {
    ++v8->m_MS_data.ms_data[2].nClass[5];
  }
  else if ( !strcmp_0(Str1, "BRB0") )
  {
    ++v8->m_MS_data.ms_data[2].nClass[6];
  }
  else if ( !strcmp_0(Str1, "BRF1") )
  {
    ++v8->m_MS_data.ms_data[2].nClass[7];
  }
  else if ( !strcmp_0(Str1, "BRF2") )
  {
    ++v8->m_MS_data.ms_data[2].nClass[8];
  }
  else if ( !strcmp_0(Str1, "BRS1") )
  {
    ++v8->m_MS_data.ms_data[2].nClass[9];
  }
  else if ( !strcmp_0(Str1, "BRS2") )
  {
    ++v8->m_MS_data.ms_data[2].nClass[10];
  }
  else if ( !strcmp_0(Str1, "BRS3") )
  {
    ++v8->m_MS_data.ms_data[2].nClass[11];
  }
  else if ( !strcmp_0(Str1, "BFB0") )
  {
    ++v8->m_MS_data.ms_data[2].nClass[12];
  }
  else if ( !strcmp_0(Str1, "BFF1") )
  {
    ++v8->m_MS_data.ms_data[2].nClass[13];
  }
  else if ( !strcmp_0(Str1, "BFF2") )
  {
    ++v8->m_MS_data.ms_data[2].nClass[14];
  }
  else if ( !strcmp_0(Str1, "BFS1") )
  {
    ++v8->m_MS_data.ms_data[2].nClass[15];
  }
  else if ( !strcmp_0(Str1, "BFS2") )
  {
    ++v8->m_MS_data.ms_data[2].nClass[16];
  }
  else if ( !strcmp_0(Str1, "BFS3") )
  {
    ++v8->m_MS_data.ms_data[2].nClass[17];
  }
  else if ( !strcmp_0(Str1, "BSB0") )
  {
    ++v8->m_MS_data.ms_data[2].nClass[18];
  }
  else if ( !strcmp_0(Str1, "BSF1") )
  {
    ++v8->m_MS_data.ms_data[2].nClass[19];
  }
  else if ( !strcmp_0(Str1, "BSF2") )
  {
    ++v8->m_MS_data.ms_data[2].nClass[20];
  }
  else if ( !strcmp_0(Str1, "BSS1") )
  {
    ++v8->m_MS_data.ms_data[2].nClass[21];
  }
  else if ( !strcmp_0(Str1, "BSS2") )
  {
    ++v8->m_MS_data.ms_data[2].nClass[22];
  }
  else if ( !strcmp_0(Str1, "CWB0") )
  {
    ++v8->m_MS_data.ms_data[2].nClass[23];
  }
  else if ( !strcmp_0(Str1, "CWF1") )
  {
    ++v8->m_MS_data.ms_data[2].nClass[24];
  }
  else if ( !strcmp_0(Str1, "CWF2") )
  {
    ++v8->m_MS_data.ms_data[2].nClass[25];
  }
  else if ( !strcmp_0(Str1, "CWS1") )
  {
    ++v8->m_MS_data.ms_data[2].nClass[26];
  }
  else if ( !strcmp_0(Str1, "CWS2") )
  {
    ++v8->m_MS_data.ms_data[2].nClass[27];
  }
  else if ( !strcmp_0(Str1, "CWS3") )
  {
    ++v8->m_MS_data.ms_data[2].nClass[28];
  }
  else if ( !strcmp_0(Str1, "CRB0") )
  {
    ++v8->m_MS_data.ms_data[2].nClass[29];
  }
  else if ( !strcmp_0(Str1, "CRF1") )
  {
    ++v8->m_MS_data.ms_data[2].nClass[30];
  }
  else if ( !strcmp_0(Str1, "CRF2") )
  {
    ++v8->m_MS_data.ms_data[2].nClass[31];
  }
  else if ( !strcmp_0(Str1, "CRS1") )
  {
    ++v8->m_MS_data.ms_data[2].nClass[32];
  }
  else if ( !strcmp_0(Str1, "CRS2") )
  {
    ++v8->m_MS_data.ms_data[2].nClass[33];
  }
  else if ( !strcmp_0(Str1, "CRS3") )
  {
    ++v8->m_MS_data.ms_data[2].nClass[34];
  }
  else if ( !strcmp_0(Str1, "CFB0") )
  {
    ++v8->m_MS_data.ms_data[2].nClass[35];
  }
  else if ( !strcmp_0(Str1, "CFF1") )
  {
    ++v8->m_MS_data.ms_data[2].nClass[36];
  }
  else if ( !strcmp_0(Str1, "CFF2") )
  {
    ++v8->m_MS_data.ms_data[2].nClass[37];
  }
  else if ( !strcmp_0(Str1, "CFS1") )
  {
    ++v8->m_MS_data.ms_data[2].nClass[38];
  }
  else if ( !strcmp_0(Str1, "CFS2") )
  {
    ++v8->m_MS_data.ms_data[2].nClass[39];
  }
  else if ( !strcmp_0(Str1, "CFS3") )
  {
    ++v8->m_MS_data.ms_data[2].nClass[40];
  }
  else if ( !strcmp_0(Str1, "CSB0") )
  {
    ++v8->m_MS_data.ms_data[2].nClass[41];
  }
  else if ( !strcmp_0(Str1, "CSF1") )
  {
    ++v8->m_MS_data.ms_data[2].nClass[42];
  }
  else if ( !strcmp_0(Str1, "CSS1") )
  {
    ++v8->m_MS_data.ms_data[2].nClass[43];
  }
  else if ( !strcmp_0(Str1, "AWB0") )
  {
    ++v8->m_MS_data.ms_data[2].nClass[44];
  }
  else if ( !strcmp_0(Str1, "AWF1") )
  {
    ++v8->m_MS_data.ms_data[2].nClass[45];
  }
  else if ( !strcmp_0(Str1, "AWF2") )
  {
    ++v8->m_MS_data.ms_data[2].nClass[46];
  }
  else if ( !strcmp_0(Str1, "AWS1") )
  {
    ++v8->m_MS_data.ms_data[2].nClass[47];
  }
  else if ( !strcmp_0(Str1, "AWS2") )
  {
    ++v8->m_MS_data.ms_data[2].nClass[48];
  }
  else if ( !strcmp_0(Str1, "AWS3") )
  {
    ++v8->m_MS_data.ms_data[2].nClass[49];
  }
  else if ( !strcmp_0(Str1, "ARB0") )
  {
    ++v8->m_MS_data.ms_data[2].nClass[50];
  }
  else if ( !strcmp_0(Str1, "ARF1") )
  {
    ++v8->m_MS_data.ms_data[2].nClass[51];
  }
  else if ( !strcmp_0(Str1, "ARF2") )
  {
    ++v8->m_MS_data.ms_data[2].nClass[52];
  }
  else if ( !strcmp_0(Str1, "ARS1") )
  {
    ++v8->m_MS_data.ms_data[2].nClass[53];
  }
  else if ( !strcmp_0(Str1, "ARS2") )
  {
    ++v8->m_MS_data.ms_data[2].nClass[54];
  }
  else if ( !strcmp_0(Str1, "ARS3") )
  {
    ++v8->m_MS_data.ms_data[2].nClass[55];
  }
  else if ( !strcmp_0(Str1, "ASB0") )
  {
    ++v8->m_MS_data.ms_data[2].nClass[56];
  }
  else if ( !strcmp_0(Str1, "ASF1") )
  {
    ++v8->m_MS_data.ms_data[2].nClass[57];
  }
  else if ( !strcmp_0(Str1, "ASS1") )
  {
    ++v8->m_MS_data.ms_data[2].nClass[58];
  }
  else if ( !strcmp_0(Str1, "ASS2") )
  {
    ++v8->m_MS_data.ms_data[2].nClass[59];
  }
}

