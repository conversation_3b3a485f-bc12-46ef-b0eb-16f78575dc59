﻿/*
 * Function: ?_FromImpl@cancellation_token@Concurrency@@SA?AV12@PEAV_CancellationTokenState@details@2@@Z
 * Address: 0x14057B7F0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Required includes for Concurrency namespace
#include <ppl.h>
#include <ppltasks.h>
#include <concurrent_vector.h>



CryptoPP::Integer *__fastcallConcurrency::cancellation_token::_FromImpl(CryptoPP::Integer *a1, const struct CryptoPP::Integer *a2)
{
  CryptoPP::Integer *v3; // [sp+40h] [bp+8h]@1

  v3 = a1;
  CryptoPP::Integer::Integer(a1, a2);
  return v3;
}

