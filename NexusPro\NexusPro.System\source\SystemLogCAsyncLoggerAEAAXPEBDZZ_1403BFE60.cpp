﻿/*
 * Function: ?SystemLog@CAsyncLogger@@AEAAXPEBDZZ
 * Address: 0x1403BFE60
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// External symbol declarations for decompiled code
extern "C" ULONG_PTR _security_cookie;



void __usercall CAsyncLogger::SystemLog(CAsyncLogger *this@<rcx>, const char *fmt@<rdx>, signed __int64 a3@<rax>, ...)
{
  void *v3; // rsp@1
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  int v6; // edi@5
  unsigned int v7; // eax@5
  const char *v8; // rax@9
  __int64 v9; // [sp-20h] [bp-2CF8h]@1
  int v10; // [sp+0h] [bp-2CD8h]@5
  int v11; // [sp+8h] [bp-2CD0h]@5
  int v12; // [sp+10h] [bp-2CC8h]@5
  int v13; // [sp+18h] [bp-2CC0h]@5
  int v14; // [sp+20h] [bp-2CB8h]@5
  int v15; // [sp+28h] [bp-2CB0h]@5
  int v16; // [sp+30h] [bp-2CA8h]@5
  char DstBuf; // [sp+50h] [bp-2C88h]@5
  char v18; // [sp+51h] [bp-2C87h]@5
  _SYSTEMTIME SystemTime; // [sp+2C68h] [bp-70h]@5
  int v20; // [sp+2C84h] [bp-54h]@5
  va_list ArgList; // [sp+2C98h] [bp-40h]@8
  int nLen; // [sp+2CA4h] [bp-34h]@8
  int v23; // [sp+2CB0h] [bp-28h]@5
  int v24; // [sp+2CB4h] [bp-24h]@5
  int v25; // [sp+2CB8h] [bp-20h]@5
  int v26; // [sp+2CBCh] [bp-1Ch]@5
  int v27; // [sp+2CC0h] [bp-18h]@5
  int v28; // [sp+2CC4h] [bp-14h]@5
  unsigned __int64 v29; // [sp+2CC8h] [bp-10h]@4
  CAsyncLogger *v30; // [sp+2CE0h] [bp+8h]@1
  char *Format; // [sp+2CE8h] [bp+10h]@0
  va_list va; // [sp+2CF0h] [bp+18h]@1

  va_start(va, fmt);
  v30 = this;
  v3 = alloca(a3);
  v4 = &v9;
  for (signed __int64 i = 2876; i > 0; --i)
  {
    *(DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v29 = (unsigned __int64)&v9 ^ _security_cookie;
  if ( v30->m_pSystemLogInfo )
  {
    DstBuf = 0;
    memset(&v18, 0, 0x2BFFui64);
    SystemTime.wYear = 0;
    memset(&SystemTime.wMonth, 0, 0xEui64);
    GetLocalTime(&SystemTime);
    v23 = SystemTime.wMilliseconds;
    v24 = SystemTime.wSecond;
    v25 = SystemTime.wMinute;
    v6 = SystemTime.wHour;
    v26 = SystemTime.wDay;
    v27 = SystemTime.wMonth;
    v28 = SystemTime.wYear;
    v7 = CAsyncLogInfo::GetCount(v30->m_pSystemLogInfo);
    v16 = v23;
    v15 = v24;
    v14 = v25;
    v13 = v6;
    v12 = v26;
    v11 = v27;
    v10 = v28;
    v20 = sprintf_s(&DstBuf, 0x2C00ui64, "%u\t%04d-%02d-%02d %02d:%02d:%02d.%03d : ", v7);
    if ( v20 > 0 && v20 < 11264 )
    {
      ArgList = (va_list)va;
      nLen = vsprintf_s(&DstBuf + v20, 11264i64 - v20, Format, (va_list)va);
      ArgList = 0;
      if ( nLen > 0 )
      {
        nLen += v20;
        v8 = CAsyncLogInfo::GetFileName(v30->m_pSystemLogInfo);
        CAsyncLogBufferList::WriteFile(v8, nLen, &DstBuf);
      }
    }
  }
}


