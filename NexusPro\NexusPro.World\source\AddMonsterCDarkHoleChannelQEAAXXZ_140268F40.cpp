﻿/*
 * Function: ?Add<PERSON><PERSON><PERSON>@CDarkHoleChannel@@QEAAXXZ
 * Address: 0x140268F40
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CDarkHoleChannel::AddMonster(CDarkHoleChannel *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  _dh_quest_setup *v3; // rcx@18
  _dh_quest_setup *v4; // rcx@20
  __int64 v5; // [sp+0h] [bp-C8h]@1
  CMonster *pParent; // [sp+20h] [bp-A8h]@18
  bool bRobExp; // [sp+28h] [bp-A0h]@18
  bool bRewardExp; // [sp+30h] [bp-98h]@18
  bool bDungeon; // [sp+38h] [bp-90h]@18
  bool bWithoutFail; // [sp+40h] [bp-88h]@18
  bool bApplyRopExpField; // [sp+48h] [bp-80h]@18
  _dh_mission_setup *v12; // [sp+50h] [bp-78h]@4
  int j; // [sp+58h] [bp-70h]@5
  __add_monster *v14; // [sp+60h] [bp-68h]@8
  _dummy_position *pPos; // [sp+68h] [bp-60h]@8
  _dummy_position *v16; // [sp+70h] [bp-58h]@11
  int k; // [sp+78h] [bp-50h]@14
  float pNewPos; // [sp+88h] [bp-40h]@16
  _monster_fld *v19; // [sp+A8h] [bp-20h]@18
  _monster_fld *v20; // [sp+B0h] [bp-18h]@20
  __int64 v21; // [sp+B8h] [bp-10h]@20
  CDarkHoleChannel *v22; // [sp+D0h] [bp+8h]@1

  v22 = this;
  v1 = &v5;
  for (signed __int64 i = 48; i > 0; --i)
  {
    *(DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v12 = v22->m_MissionMgr.pCurMssionPtr;
  if ( v12 )
  {
    for ( j = 0; ; ++j )
    {
      if ( j >= v12->nAddMonsterNum )
        return;
      v14 = v12->pAddMonster[j];
      pPos = 0;
      if ( v14->ReactArea.AreaDefType == 1 )
      {
        pPos = v14->ReactArea.obj.dummy.pPos;
      }
      else
      {
        if ( v14->ReactArea.AreaDefType != 2 )
          continue;
        v16 = v14->ReactArea.obj.dummy.pPos;
        pPos = *(_dummy_position **)&v16->m_szCode[8 * (rand() % *(DWORD *)&v16->m_szCode[8]) + 16];
      }
      if ( pPos )
      {
        for ( k = 0; k < v14->ReactObj.wNum; ++k )
        {
          if ( CMapData::GetRandPosInDummy(v22->m_pQuestSetup->pUseMap, pPos, &pNewPos, 1) )
          {
            if ( v14->ReactObj.ObjDefType == 2 )
            {
              v19 = v14->ReactObj.obj.monster.pMonsterFld;
              v3 = v22->m_pQuestSetup;
              bApplyRopExpField = 0;
              bWithoutFail = 1;
              bDungeon = 1;
              bRewardExp = 1;
              bRobExp = 0;
              pParent = 0;
              CreateRepMonster(v3->pUseMap, v22->m_wLayerIndex, &pNewPos, v19->m_strCode, 0i64, 0, 1, 1, 1, 0);
            }
            else if ( v14->ReactObj.ObjDefType == 4 )
            {
              v20 = v14->ReactObj.obj.monster.pMonsterFld;
              v21 = *(QWORD *)&v20->m_strCode[8 * (rand() % *(DWORD *)&v20->m_strCode[4]) + 12];
              v4 = v22->m_pQuestSetup;
              bApplyRopExpField = 0;
              bWithoutFail = 1;
              bDungeon = 1;
              bRewardExp = 1;
              bRobExp = 0;
              pParent = 0;
              CreateRepMonster(v4->pUseMap, v22->m_wLayerIndex, &pNewPos, (char *)(v21 + 4), 0i64, 0, 1, 1, 1, 0);
            }
          }
        }
      }
    }
  }
}


