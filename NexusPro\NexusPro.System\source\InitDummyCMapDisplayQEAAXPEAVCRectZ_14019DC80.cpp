﻿/*
 * Function: ?InitDummy@CMapDisplay@@QEAAXPEAVCRect@@@Z
 * Address: 0x14019DC80
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// External symbol declarations
extern "C" IMAGE_DOS_HEADER __ImageBase;
extern "C" DWORD off_14000003C;
extern "C" void* _delayLoadHelper2(ImgDelayDescr* pidd, void** ppfnIATEntry);
extern struct EqSukData { void* pwszEpSuk; } EqSukList[16];
extern void MyMessageBox(const char* title, const char* message);


void __fastcall CMapDisplay::InitDummy(CMapDisplay *this, CRect *prcWnd)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  _map_fld *v4; // rcx@8
  signed __int64 v5; // rax@10
  unsigned __int8 v6; // cf@12
  unsigned __int64 v7; // rax@12
  __int64 v8; // [sp+0h] [bp-138h]@1
  void (__cdecl *pDtor)(void *); // [sp+20h] [bp-118h]@8
  int v10; // [sp+40h] [bp-F8h]@4
  int j; // [sp+44h] [bp-F4h]@4
  CMapData *pMap; // [sp+48h] [bp-F0h]@7
  int v13; // [sp+50h] [bp-E8h]@17
  int k; // [sp+54h] [bp-E4h]@17
  _mon_block *v15; // [sp+58h] [bp-E0h]@20
  unsigned int l; // [sp+60h] [bp-D8h]@20
  _dummy_position *v17; // [sp+68h] [bp-D0h]@22
  int m; // [sp+70h] [bp-C8h]@27
  _dummy_position *v19; // [sp+78h] [bp-C0h]@29
  int n; // [sp+80h] [bp-B8h]@33
  _dummy_position *v21; // [sp+88h] [bp-B0h]@35
  _dummy_position *v22; // [sp+90h] [bp-A8h]@41
  _dummy_position *v23; // [sp+98h] [bp-A0h]@47
  _dummy_position *v24; // [sp+A0h] [bp-98h]@53
  int count[2]; // [sp+A8h] [bp-90h]@10
  CDummyDraw *v26; // [sp+B0h] [bp-88h]@17
  void *v27; // [sp+B8h] [bp-80h]@14
  __int64 v28; // [sp+C0h] [bp-78h]@4
  CDummyDraw *v29; // [sp+C8h] [bp-70h]@15
  CDummyDraw *v30; // [sp+D0h] [bp-68h]@23
  CDummyDraw *v31; // [sp+D8h] [bp-60h]@24
  CDummyDraw *v32; // [sp+E0h] [bp-58h]@30
  CDummyDraw *v33; // [sp+E8h] [bp-50h]@31
  CDummyDraw *v34; // [sp+F0h] [bp-48h]@36
  CDummyDraw *v35; // [sp+F8h] [bp-40h]@37
  CDummyDraw *v36; // [sp+100h] [bp-38h]@42
  CDummyDraw *v37; // [sp+108h] [bp-30h]@43
  CDummyDraw *v38; // [sp+110h] [bp-28h]@48
  CDummyDraw *v39; // [sp+118h] [bp-20h]@49
  CDummyDraw *v40; // [sp+120h] [bp-18h]@54
  CDummyDraw *v41; // [sp+128h] [bp-10h]@55
  CMapDisplay *v42; // [sp+140h] [bp+8h]@1
  CRect *v43; // [sp+148h] [bp+10h]@1

  v43 = prcWnd;
  v42 = this;
  v2 = &v8;
  for (signed __int64 i = 76; i > 0; --i)
  {
    *(DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v28 = -2i64;
  v10 = dword_141470B98;
  for ( j = 0; j < v10; ++j )
  {
    pMap = (CMapData *)(1504i64 * j + unk_141470BC8);
    v42->m_nDummyDrawNum[j] = pMap->m_nQuestDumNum
                            + pMap->m_nResDumNum
                            + pMap->m_nBindDumNum
                            + pMap->m_nStartDumNum
                            + pMap->m_nItemStoreDumNum
                            + pMap->m_nPortalNum
                            + pMap->m_nMonDumNum;
    if ( v42->m_nDummyDrawNum[j] <= 3000 )
    {
      if ( !v42->m_DummyDraw[j] )
      {
        *(QWORD *)count = v42->m_nDummyDrawNum[j];
        v5 = 152i64 * *(QWORD *)count;
        if ( !is_mul_ok(0x98ui64, *(unsigned __int64 *)count) )
          v5 = -1i64;
        v6 = __CFADD__(v5, 8i64);
        v7 = v5 + 8;
        if ( v6 )
          v7 = -1i64;
        v27 = operator new[](v7);
        if ( v27 )
        {
          *(DWORD *)v27 = count[0];
          `eh vector constructor iterator'(
            (char *)v27 + 8,
            0x98ui64,
            count[0],
            (void (__cdecl *)(void *))CDummyDraw::CDummyDraw,
            (void (__cdecl *)(void *))CDummyDraw::~CDummyDraw);
          v29 = (CDummyDraw *)((char *)v27 + 8);
        }
        else
        {
          v29 = 0;
        }
        v26 = v29;
        v42->m_DummyDraw[j] = v29;
        v13 = 0;
        for ( k = 0; k < pMap->m_nMonBlockNum; ++k )
        {
          v15 = &pMap->m_pMonBlock[k];
          for ( l = 0; l < v15->m_pBlkRec->m_dwDummyNum; ++l )
          {
            v17 = v15->m_pDumPos[l];
            if ( v17->m_bPosAble )
            {
              v31 = &v42->m_DummyDraw[j][v13];
              CDummyDraw::SetDummyRange(v31, pMap, v17->m_fMin, v17->m_fMax, v17->m_fRT, v17->m_fLB, 0, v43);
            }
            else
            {
              v30 = &v42->m_DummyDraw[j][v13];
              CDummyDraw::SetDummyRange(v30, pMap, v17->m_fMin, v17->m_fMax, v17->m_fRT, v17->m_fLB, 9, v43);
            }
            ++v13;
          }
        }
        for ( m = 0; m < pMap->m_nPortalNum; ++m )
        {
          v19 = pMap->m_pPortal[m].m_pDumPos;
          if ( v19->m_bPosAble )
          {
            v33 = &v42->m_DummyDraw[j][v13];
            CDummyDraw::SetDummyRange(v33, pMap, v19->m_fMin, v19->m_fMax, v19->m_fRT, v19->m_fLB, 1, v43);
          }
          else
          {
            v32 = &v42->m_DummyDraw[j][v13];
            CDummyDraw::SetDummyRange(v32, pMap, v19->m_fMin, v19->m_fMax, v19->m_fRT, v19->m_fLB, 9, v43);
          }
          ++v13;
        }
        for ( n = 0; n < pMap->m_nItemStoreDumNum; ++n )
        {
          v21 = pMap->m_pItemStoreDummy[n].m_pDumPos;
          if ( v21->m_bPosAble )
          {
            v35 = &v42->m_DummyDraw[j][v13];
            CDummyDraw::SetDummyRange(v35, pMap, v21->m_fMin, v21->m_fMax, v21->m_fRT, v21->m_fLB, 2, v43);
          }
          else
          {
            v34 = &v42->m_DummyDraw[j][v13];
            CDummyDraw::SetDummyRange(v34, pMap, v21->m_fMin, v21->m_fMax, v21->m_fRT, v21->m_fLB, 9, v43);
          }
          ++v13;
        }
        for ( n = 0; n < pMap->m_nStartDumNum; ++n )
        {
          v22 = pMap->m_pStartDummy[n].m_pDumPos;
          if ( v22->m_bPosAble )
          {
            v37 = &v42->m_DummyDraw[j][v13];
            CDummyDraw::SetDummyRange(v37, pMap, v22->m_fMin, v22->m_fMax, v22->m_fRT, v22->m_fLB, 3, v43);
          }
          else
          {
            v36 = &v42->m_DummyDraw[j][v13];
            CDummyDraw::SetDummyRange(v36, pMap, v22->m_fMin, v22->m_fMax, v22->m_fRT, v22->m_fLB, 9, v43);
          }
          ++v13;
        }
        for ( n = 0; n < pMap->m_nBindDumNum; ++n )
        {
          v23 = pMap->m_pBindDummy[n].m_pDumPos;
          if ( v23->m_bPosAble )
          {
            v39 = &v42->m_DummyDraw[j][v13];
            CDummyDraw::SetDummyRange(v39, pMap, v23->m_fMin, v23->m_fMax, v23->m_fRT, v23->m_fLB, 4, v43);
          }
          else
          {
            v38 = &v42->m_DummyDraw[j][v13];
            CDummyDraw::SetDummyRange(v38, pMap, v23->m_fMin, v23->m_fMax, v23->m_fRT, v23->m_fLB, 9, v43);
          }
          ++v13;
        }
        for ( n = 0; n < pMap->m_nQuestDumNum; ++n )
        {
          v24 = pMap->m_pQuestDummy[n].m_pDumPos;
          if ( v24->m_bPosAble )
          {
            v41 = &v42->m_DummyDraw[j][v13];
            CDummyDraw::SetDummyRange(v41, pMap, v24->m_fMin, v24->m_fMax, v24->m_fRT, v24->m_fLB, 8, v43);
          }
          else
          {
            v40 = &v42->m_DummyDraw[j][v13];
            CDummyDraw::SetDummyRange(v40, pMap, v24->m_fMin, v24->m_fMax, v24->m_fRT, v24->m_fLB, 9, v43);
          }
          ++v13;
        }
      }
    }
    else
    {
      v4 = pMap->m_pMapSet;
      ((DWORD)(pDtor) = 3000;
      MyMessageBox("CMapDisplay::InitDummy", "%sMap ´õ¹Ì¼ö[%d]°¡ %dº¸´Ù ¸¹´Ù. ", v4->m_strCode, v42->m_nDummyDrawNum[j]);
    }
  }
}


