﻿/*
 * Function: ?Init@CRaceBossMsgController@@QEAA_NXZ
 * Address: 0x1402A0470
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


bool __fastcall CRaceBossMsgController::Init(CRaceBossMsgController *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // rax@5
  bool result; // al@8
  __int64 v5; // [sp+0h] [bp-68h]@1
  unsigned int dwCurTime; // [sp+24h] [bp-44h]@11
  CMyTimer *v7; // [sp+38h] [bp-30h]@7
  CMyTimer *v8; // [sp+40h] [bp-28h]@4
  __int64 v9; // [sp+48h] [bp-20h]@4
  CMyTimer *v10; // [sp+50h] [bp-18h]@5
  CRaceBossMsgController *v11; // [sp+70h] [bp+8h]@1

  v11 = this;
  v1 = &v5;
  for (signed __int64 i = 24; i > 0; --i)
  {
    *(DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v9 = -2i64;
  v8 = (CMyTimer *)operator new(0x18ui64);
  if ( v8 )
  {
    CMyTimer::CMyTimer(v8);
    v10 = (CMyTimer *)v3;
  }
  else
  {
    v10 = 0;
  }
  v7 = v10;
  v11->m_pkTimer = v10;
  if ( v11->m_pkTimer )
  {
    v11->m_iOldDay = CRaceBossMsgController::GetCurDay(v11);
    if ( v11->m_iOldDay >= 0 )
    {
      CMyTimer::BeginTimer(v11->m_pkTimer, 0x7530u);
      dwCurTime = 0;
      if ( RACE_BOSS_MSG::CMsgListManager::IsHaveBeenSave(&v11->m_kManager)
        && CRaceBossMsgController::LoadCurTime(v11, &dwCurTime) )
      {
        result = RACE_BOSS_MSG::CMsgListManager::Load(&v11->m_kManager, dwCurTime);
      }
      else
      {
        RACE_BOSS_MSG::CMsgListManager::Save(&v11->m_kManager);
        result = 1;
      }
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}


