#!/usr/bin/env python3
"""
Apply Successful Automation to All NexusPro Modules
Applies the proven Python automation approach to all remaining modules
"""

import os
import sys
import time
import subprocess
from pathlib import Path

class ModuleAutomationApplier:
    def __init__(self):
        self.modules = [
            'NexusPro.Authentication',
            'NexusPro.Combat', 
            'NexusPro.Database',
            'NexusPro.Items',
            'NexusPro.Network',
            'NexusPro.Player',
            'NexusPro.Security',
            'NexusPro.System',
            'NexusPro.World'
        ]
        self.automation_scripts = [
            'mass_fix_syntax.py',
            'advanced_signature_parser.py',
            'critical_backtick_fixer.py',
            'final_function_signature_fixer.py',
            'advanced_this_parameter_fixer.py'
        ]
        
    def run_script_on_module(self, script_name: str, module_name: str) -> dict:
        """Run a specific automation script on a module"""
        try:
            print(f"  Running {script_name} on {module_name}...")
            
            # Modify scripts to target specific module
            if script_name == 'mass_fix_syntax.py':
                cmd = ['py', script_name, module_name.split('.')[1].lower()]
            elif script_name == 'advanced_signature_parser.py':
                cmd = ['py', script_name, module_name.split('.')[1].lower()]
            else:
                # For module-specific scripts, we need to modify them
                cmd = ['py', script_name]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
            
            return {
                'success': result.returncode == 0,
                'stdout': result.stdout,
                'stderr': result.stderr,
                'returncode': result.returncode
            }
            
        except subprocess.TimeoutExpired:
            return {'success': False, 'error': 'Timeout'}
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def apply_automation_to_module(self, module_name: str) -> dict:
        """Apply all automation scripts to a specific module"""
        print(f"\n🔧 Processing {module_name}...")
        
        results = {}
        total_files_processed = 0
        total_files_fixed = 0
        
        for script in self.automation_scripts:
            if not Path(script).exists():
                print(f"  ⚠️  Script {script} not found, skipping...")
                continue
                
            result = self.run_script_on_module(script, module_name)
            results[script] = result
            
            if result['success']:
                print(f"  ✅ {script} completed successfully")
                # Try to extract statistics from output
                if 'Files Processed:' in result['stdout']:
                    lines = result['stdout'].split('\n')
                    for line in lines:
                        if 'Files Processed:' in line:
                            try:
                                processed = int(line.split(':')[1].strip().replace(',', ''))
                                total_files_processed += processed
                            except:
                                pass
                        elif 'Files Fixed:' in line:
                            try:
                                fixed = int(line.split(':')[1].strip().replace(',', ''))
                                total_files_fixed += fixed
                            except:
                                pass
            else:
                print(f"  ❌ {script} failed: {result.get('error', 'Unknown error')}")
        
        return {
            'module': module_name,
            'results': results,
            'total_files_processed': total_files_processed,
            'total_files_fixed': total_files_fixed
        }
    
    def test_module_build(self, module_name: str) -> dict:
        """Test build for a specific module"""
        print(f"\n🔨 Testing build for {module_name}...")
        
        try:
            vcxproj_path = f"NexusPro\\{module_name}\\{module_name}.vcxproj"
            
            cmd = [
                "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\MSBuild\\Current\\Bin\\MSBuild.exe",
                vcxproj_path,
                "/p:Configuration=Debug",
                "/p:Platform=x64",
                "/v:minimal",
                "/nologo"
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=600)
            
            # Count errors in output
            error_count = result.stderr.count('error C') + result.stdout.count('error C')
            warning_count = result.stderr.count('warning C') + result.stdout.count('warning C')
            
            return {
                'success': result.returncode == 0,
                'error_count': error_count,
                'warning_count': warning_count,
                'returncode': result.returncode,
                'output_length': len(result.stdout) + len(result.stderr)
            }
            
        except subprocess.TimeoutExpired:
            return {'success': False, 'error': 'Build timeout'}
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def process_all_modules(self):
        """Process all modules with automation"""
        print("🚀 Applying Proven Python Automation to All NexusPro Modules")
        print("=" * 70)
        
        start_time = time.time()
        
        overall_stats = {
            'modules_processed': 0,
            'total_files_processed': 0,
            'total_files_fixed': 0,
            'successful_builds': 0,
            'failed_builds': 0,
            'module_results': []
        }
        
        for module in self.modules:
            module_path = Path(f'NexusPro/{module}')
            if not module_path.exists():
                print(f"⚠️  Module {module} not found, skipping...")
                continue
            
            # Apply automation
            automation_result = self.apply_automation_to_module(module)
            
            # Test build
            build_result = self.test_module_build(module)
            
            # Update overall stats
            overall_stats['modules_processed'] += 1
            overall_stats['total_files_processed'] += automation_result['total_files_processed']
            overall_stats['total_files_fixed'] += automation_result['total_files_fixed']
            
            if build_result['success']:
                overall_stats['successful_builds'] += 1
                print(f"  ✅ Build successful!")
            else:
                overall_stats['failed_builds'] += 1
                error_count = build_result.get('error_count', 'Unknown')
                print(f"  ❌ Build failed with {error_count} errors")
            
            # Store detailed results
            overall_stats['module_results'].append({
                'module': module,
                'automation': automation_result,
                'build': build_result
            })
            
            print(f"  📊 Module Stats: {automation_result['total_files_processed']} processed, {automation_result['total_files_fixed']} fixed")
        
        elapsed_time = time.time() - start_time
        
        # Print final summary
        print(f"\n🎉 Automation Complete!")
        print(f"📊 Overall Statistics:")
        print(f"   • Modules Processed: {overall_stats['modules_processed']}")
        print(f"   • Total Files Processed: {overall_stats['total_files_processed']:,}")
        print(f"   • Total Files Fixed: {overall_stats['total_files_fixed']:,}")
        print(f"   • Successful Builds: {overall_stats['successful_builds']}")
        print(f"   • Failed Builds: {overall_stats['failed_builds']}")
        print(f"   • Processing Time: {elapsed_time:.2f} seconds")
        
        if overall_stats['total_files_processed'] > 0:
            success_rate = (overall_stats['total_files_fixed'] / overall_stats['total_files_processed']) * 100
            print(f"   • Fix Success Rate: {success_rate:.1f}%")
        
        if overall_stats['modules_processed'] > 0:
            build_success_rate = (overall_stats['successful_builds'] / overall_stats['modules_processed']) * 100
            print(f"   • Build Success Rate: {build_success_rate:.1f}%")
        
        return overall_stats

def main():
    """Main function"""
    applier = ModuleAutomationApplier()
    
    print("🎯 NexusPro Module Automation Applier")
    print("Applying the proven Economy module automation approach to all modules")
    print()
    
    # Check if we're in the right directory
    if not Path('NexusPro').exists():
        print("❌ Error: NexusPro directory not found. Please run from project root.")
        return 1
    
    # Check if automation scripts exist
    missing_scripts = []
    for script in applier.automation_scripts:
        if not Path(script).exists():
            missing_scripts.append(script)
    
    if missing_scripts:
        print(f"⚠️  Warning: Missing automation scripts: {', '.join(missing_scripts)}")
        print("Will skip missing scripts and continue with available ones.")
        print()
    
    # Process all modules
    results = applier.process_all_modules()
    
    print(f"\n✅ Module automation application complete!")
    print(f"Ready for solution-wide build testing!")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
