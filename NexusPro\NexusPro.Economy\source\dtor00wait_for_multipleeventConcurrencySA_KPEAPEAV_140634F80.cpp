﻿/*
 * Function: ?dtor_0@?0??wait_for_multiple@event@Concurrency@@SA_KPEAPEAV12@_K_NI@Z@4HA
 * Address: 0x140634F80
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Required includes for Concurrency namespace
#include <ppl.h>
#include <ppltasks.h>
#include <concurrent_vector.h>



void __fastcallConcurrency::event::wait_for_multiple'::1'::dtor_0(__int64 a1, __int64 a2)
{
  CryptoPP::Integer::~Integer((CryptoPP::Integer *)(a2 + 64));
}

