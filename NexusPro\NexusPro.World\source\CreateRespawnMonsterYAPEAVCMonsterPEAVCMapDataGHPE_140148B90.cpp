﻿/*
 * Function: ?CreateRespawnMonster@@YAPEAVCMonster@@PEAVCMapData@@GHPEAU_mon_active@@PEAU_dummy_position@@_N3333@Z
 * Address: 0x140148B90
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


CMonster *__fastcall CreateRespawnMonster(CMapData *pMap, unsigned __int16 wLayer, int nMonsterIndex, _mon_active *pActiveRec, _dummy_position *pDumPosition, bool bRobExp, bool bRewardExp, bool bDungeon, bool bWithoutFail, bool bApplyRopExpField)
{
  __int64 *v10; // rdi@1
  signed __int64 i; // rcx@1
  CMonster *result; // rax@5
  __int64 v13; // [sp+0h] [bp-A8h]@1
  CMonster *v14; // [sp+20h] [bp-88h]@4
  _monster_create_setdata Dst; // [sp+40h] [bp-68h]@6
  _base_fld *v16; // [sp+88h] [bp-20h]@16
  bool v17; // [sp+90h] [bp-18h]@16
  CMapData *v18; // [sp+B0h] [bp+8h]@1
  unsigned __int16 v19; // [sp+B8h] [bp+10h]@1
  int n; // [sp+C0h] [bp+18h]@1
  _mon_active *v21; // [sp+C8h] [bp+20h]@1

  v21 = pActiveRec;
  n = nMonsterIndex;
  v19 = wLayer;
  v18 = pMap;
  v10 = &v13;
  for (signed __int64 i = 40; i > 0; --i)
  {
    *(DWORD *)v10 = -858993460;
    v10 = (__int64 *)((char *)v10 + 4);
  }
  v14 = SearchEmptyMonster(bWithoutFail);
  if ( !v14 )
    return 0i64;
  _monster_create_setdata::_monster_create_setdata(&Dst);
  Dst.m_pMap = v18;
  Dst.m_nLayerIndex = v19;
  Dst.m_pRecordSet = CRecordData::GetRecord(&stru_1799C6210, n);
  if ( !Dst.m_pRecordSet )
    return 0i64;
  if ( v21 && v21->m_pBlk && v21->m_pBlk->m_bRotate && pDumPosition )
  {
    memcpy_0(Dst.m_fStartPos, pDumPosition->m_fCenterPos, 0xCui64);
  }
  else if ( !CMapData::GetRandPosInDummy(v18, pDumPosition, Dst.m_fStartPos, 0) )
  {
    return 0i64;
  }
  Dst.pActiveRec = v21;
  Dst.pDumPosition = pDumPosition;
  Dst.bDungeon = bDungeon;
  if ( bApplyRopExpField )
  {
    v16 = Dst.m_pRecordSet;
    v17 = *(DWORD *)&Dst.m_pRecordSet[4].m_strCode[4] != 0;
    Dst.bRobExp = v17;
  }
  else
  {
    Dst.bRobExp = bRobExp;
  }
  Dst.bRewardExp = bRewardExp;
  if ( CMonster::Create(v14, &Dst) )
    result = v14;
  else
    result = 0;
  return result;
}


