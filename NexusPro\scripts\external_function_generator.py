#!/usr/bin/env python3
"""
External Function Declaration Generator for RF Online Decompiled Code
Automatically generates missing external function declarations and stubs
"""

import os
import re
import sys
from pathlib import Path
from typing import List, Dict, Set, Tuple, Optional
from collections import defaultdict

class ExternalFunctionGenerator:
    def __init__(self):
        self.missing_functions = set()
        self.function_signatures = {}
        self.external_variables = set()
        
        # Common RF Online function patterns and their likely signatures
        self.known_patterns = {
            # Graphics/Rendering functions
            r'CN_InvalidateNature': 'void CN_InvalidateNature();',
            r'ReleaseVertexShaderList': 'void ReleaseVertexShaderList();',
            r'ReleaseBlurVBuffer': 'void ReleaseBlurVBuffer();',
            r'ReleaseFullScreenEffect': 'void ReleaseFullScreenEffect();',
            
            # Math functions
            r'sqrt_0': 'double sqrt_0(double x);',
            r'ffloor': 'double ffloor(double x);',
            r'_CalcPayExgRatePerRace': 'double _CalcPayExgRatePerRace(int race, double rate);',
            
            # String/utility functions
            r'S': 'const char* S(const char* str);',
            r'MyMessageBox': 'void MyMessageBox(const char* title, const char* message);',
            
            # Memory management
            r'_delayLoadHelper2': 'void* _delayLoadHelper2(void* pidd, void** ppfnIATEntry);',
            
            # Game-specific functions
            r'GetPrivateProfileString': 'DWORD GetPrivateProfileString(LPCSTR lpAppName, LPCSTR lpKeyName, LPCSTR lpDefault, LPSTR lpReturnedString, DWORD nSize, LPCSTR lpFileName);',
            r'GetPrivateProfileInt': 'UINT GetPrivateProfileInt(LPCSTR lpAppName, LPCSTR lpKeyName, INT nDefault, LPCSTR lpFileName);',
            
            # DirectX/Graphics variables
            r'stOldRenderTarget': 'extern void* stOldRenderTarget;',
            r'stOldStencilZ': 'extern void* stOldStencilZ;',
            
            # Image base
            r'__ImageBase': 'extern "C" IMAGE_DOS_HEADER __ImageBase;',
            r'off_14000003C': 'extern "C" DWORD off_14000003C;',
        }
        
        # Variable patterns
        self.variable_patterns = {
            r'st\w+': 'extern void* {};',
            r'off_[0-9A-Fa-f]+': 'extern "C" DWORD {};',
            r'g_\w+': 'extern void* {};',
            r'm_\w+': 'extern void* {};',
        }

    def extract_missing_functions(self, content: str) -> Set[str]:
        """Extract function names from error messages"""
        missing = set()
        
        # Pattern for "identifier not found" errors
        pattern = r"error C3861: '([^']+)': identifier not found"
        matches = re.findall(pattern, content)
        missing.update(matches)
        
        # Pattern for "undeclared identifier" errors  
        pattern = r"error C2065: '([^']+)': undeclared identifier"
        matches = re.findall(pattern, content)
        missing.update(matches)
        
        return missing

    def extract_function_usage(self, content: str) -> Dict[str, List[str]]:
        """Extract function usage patterns to infer signatures"""
        usage_patterns = defaultdict(list)
        
        # Find function calls with parameters
        pattern = r'(\w+)\s*\([^)]*\)'
        matches = re.findall(pattern, content)
        
        for match in matches:
            if match not in ['if', 'for', 'while', 'switch', 'sizeof']:
                usage_patterns[match].append(match)
        
        return usage_patterns

    def generate_function_declaration(self, func_name: str, usage_context: List[str] = None) -> str:
        """Generate appropriate function declaration based on name and usage"""
        
        # Check known patterns first
        for pattern, declaration in self.known_patterns.items():
            if re.match(pattern, func_name):
                return declaration
        
        # Infer from naming conventions
        if func_name.startswith('Get'):
            if 'String' in func_name:
                return f'const char* {func_name}();'
            elif 'Int' in func_name or 'Count' in func_name:
                return f'int {func_name}();'
            else:
                return f'void* {func_name}();'
        
        elif func_name.startswith('Set') or func_name.startswith('Update'):
            return f'void {func_name}();'
        
        elif func_name.startswith('Create') or func_name.startswith('Init'):
            return f'bool {func_name}();'
        
        elif func_name.startswith('Release') or func_name.startswith('Destroy'):
            return f'void {func_name}();'
        
        elif func_name.startswith('Is') or func_name.startswith('Has') or func_name.startswith('Can'):
            return f'bool {func_name}();'
        
        elif 'Calc' in func_name or 'Compute' in func_name:
            return f'double {func_name}();'
        
        else:
            # Default to void function
            return f'void {func_name}();'

    def generate_variable_declaration(self, var_name: str) -> str:
        """Generate appropriate variable declaration"""
        
        # Check known patterns
        for pattern, declaration in self.known_patterns.items():
            if re.match(pattern, var_name):
                return declaration
        
        # Check variable patterns
        for pattern, template in self.variable_patterns.items():
            if re.match(pattern, var_name):
                return template.format(var_name)
        
        # Default to void pointer
        return f'extern void* {var_name};'

    def create_external_header(self, missing_symbols: Set[str], output_path: Path):
        """Create a header file with all missing external declarations"""
        
        functions = []
        variables = []
        
        for symbol in sorted(missing_symbols):
            # Determine if it's likely a function or variable
            if any(keyword in symbol.lower() for keyword in ['get', 'set', 'create', 'init', 'release', 'destroy', 'update', 'calc', 'compute', 'invalidate']):
                functions.append(self.generate_function_declaration(symbol))
            elif symbol.startswith(('st', 'g_', 'm_', 'off_')) or symbol == '__ImageBase':
                variables.append(self.generate_variable_declaration(symbol))
            else:
                # Default to function
                functions.append(self.generate_function_declaration(symbol))
        
        header_content = f'''#ifndef EXTERNAL_DECLARATIONS_H
#define EXTERNAL_DECLARATIONS_H

// Auto-generated external declarations for RF Online decompiled code
// Generated by ExternalFunctionGenerator

#include <windows.h>

// External function declarations
{chr(10).join(functions)}

// External variable declarations  
{chr(10).join(variables)}

#endif // EXTERNAL_DECLARATIONS_H
'''
        
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(header_content)
        
        print(f"✅ Generated external declarations: {output_path}")
        print(f"   Functions: {len(functions)}")
        print(f"   Variables: {len(variables)}")

    def process_build_output(self, build_output: str) -> Set[str]:
        """Process MSBuild output to extract missing symbols"""
        missing_symbols = set()
        
        # Extract from error messages
        missing_symbols.update(self.extract_missing_functions(build_output))
        
        return missing_symbols

    def scan_source_files(self, directory: Path) -> Set[str]:
        """Scan source files for potential missing symbols"""
        missing_symbols = set()
        
        for file_path in directory.rglob('*.cpp'):
            try:
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                
                # Look for function calls that might be missing
                # This is a heuristic approach
                pattern = r'(\w+)\s*\([^)]*\);'
                matches = re.findall(pattern, content)
                
                for match in matches:
                    # Filter out obvious language keywords and common functions
                    if (match not in ['if', 'for', 'while', 'switch', 'return', 'sizeof', 'printf', 'malloc', 'free'] 
                        and not match.startswith('std::')
                        and len(match) > 2):
                        missing_symbols.add(match)
                        
            except Exception as e:
                print(f"Warning: Could not scan {file_path}: {e}")
        
        return missing_symbols

def main():
    if len(sys.argv) < 2:
        print("Usage: python external_function_generator.py <project_directory> [build_output_file]")
        sys.exit(1)
    
    project_dir = Path(sys.argv[1])
    if not project_dir.exists():
        print(f"Directory not found: {project_dir}")
        sys.exit(1)
    
    generator = ExternalFunctionGenerator()
    missing_symbols = set()
    
    # If build output file provided, process it
    if len(sys.argv) > 2:
        build_output_file = Path(sys.argv[2])
        if build_output_file.exists():
            with open(build_output_file, 'r', encoding='utf-8') as f:
                build_output = f.read()
            missing_symbols.update(generator.process_build_output(build_output))
            print(f"📄 Processed build output: {len(missing_symbols)} symbols found")
    
    # Scan source files for additional symbols
    print(f"🔍 Scanning source files in: {project_dir}")
    scanned_symbols = generator.scan_source_files(project_dir)
    missing_symbols.update(scanned_symbols)
    print(f"📁 Scanned source files: {len(scanned_symbols)} additional symbols found")
    
    if missing_symbols:
        output_path = project_dir / "NexusPro.Core" / "headers" / "ExternalDeclarations.h"
        generator.create_external_header(missing_symbols, output_path)
    else:
        print("ℹ️  No missing symbols found")

if __name__ == "__main__":
    main()
