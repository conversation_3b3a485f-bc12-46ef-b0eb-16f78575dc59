﻿/*
 * Function: ?PostRegistryLoad@CPostSystemManager@@QEAA_NXZ
 * Address: 0x140324F30
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// External symbol declarations for decompiled code
extern "C" ULONG_PTR _security_cookie;



char __usercall CPostSystemManager::PostRegistryLoad@<al>(CPostSystemManager *this@<rcx>, signed __int64 a2@<rax>)
{
  void *v2; // rsp@1
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@11
  __int64 v6; // [sp-20h] [bp-261D8h]@1
  char ptr; // [sp+20h] [bp-26198h]@4
  char v8; // [sp+26184h] [bp-34h]@4
  unsigned int dwIndex; // [sp+26188h] [bp-30h]@5
  char v10; // [sp+26198h] [bp-20h]@11
  char v11; // [sp+26199h] [bp-1Fh]@13
  bool v12; // [sp+2619Ah] [bp-1Eh]@14
  __int64 v13; // [sp+261A0h] [bp-18h]@4
  unsigned __int64 v14; // [sp+261A8h] [bp-10h]@4
  CPostSystemManager *v15; // [sp+261C0h] [bp+8h]@1

  v15 = this;
  v2 = alloca(a2);
  v3 = &v6;
  for (signed __int64 i = 39028; i > 0; --i)
  {
    *(DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v13 = -2i64;
  v14 = (unsigned __int64)&v6 ^ _security_cookie;
  `eh vector constructor iterator'(
    &ptr,
    0x138ui64,
    500,
    (void (__cdecl *)(void *))CPostData::CPostData,
    (void (__cdecl *)(void *))CPostData::~CPostData);
  v8 = CRFWorldDatabase::Select_PostRegistryData(pkDB, 0x1F4u, (CPostData *)&ptr);
  if ( v8 )
  {
    if ( v8 == 2 )
    {
      v11 = 1;
      `eh vector destructor iterator'(&ptr, 0x138ui64, 500, (void (__cdecl *)(void *))CPostData::~CPostData);
      result = v11;
    }
    else
    {
      v12 = 0;
      `eh vector destructor iterator'(&ptr, 0x138ui64, 500, (void (__cdecl *)(void *))CPostData::~CPostData);
      result = v12;
    }
  }
  else
  {
    memcpy_s(v15->m_PostData, 0x26160ui64, &ptr, 0x26160ui64);
    for ( dwIndex = 0; (signed int)dwIndex < 500; ++dwIndex )
    {
      if ( !v15->m_PostData[dwIndex].m_byState )
      {
        if ( CNetIndexList::FindNode(&v15->m_listEmpty, dwIndex) )
          CNetIndexList::PushNode_Back(&v15->m_listRegist, dwIndex);
      }
    }
    v10 = 1;
    `eh vector destructor iterator'(&ptr, 0x138ui64, 500, (void (__cdecl *)(void *))CPostData::~CPostData);
    result = v10;
  }
  return result;
}


