﻿/*
 * Function: ?CheckMonArea_N_ChangeState@DfAIMgr@@SAHPEAVCMonsterAI@@PEAVCMonster@@H@Z
 * Address: 0x140153350
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


signed __int64 __usercall DfAIMgr::CheckMonArea_N_ChangeState@<rax>(CMonsterAI *pAI@<rcx>, CMonster *pMon@<rdx>, int bAttackState@<r8d>, float a4@<xmm0>)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 result; // rax@8
  CMonster *v7; // rax@19
  CMonster *v8; // rax@21
  int v9; // xmm0_4@21
  int v10; // xmm0_4@21
  float v11; // xmm0_4@21
  CMonster *v12; // rax@22
  CMonster *v13; // rax@26
  __int64 v14; // [sp+0h] [bp-158h]@1
  int v15; // [sp+20h] [bp-138h]@6
  float v16[3]; // [sp+38h] [bp-120h]@7
  int v17; // [sp+54h] [bp-104h]@11
  int v18; // [sp+58h] [bp-100h]@11
  int v19; // [sp+5Ch] [bp-FCh]@19
  int v20; // [sp+60h] [bp-F8h]@19
  float v21; // [sp+64h] [bp-F4h]@21
  double v22; // [sp+68h] [bp-F0h]@21
  float v23; // [sp+78h] [bp-E0h]@21
  float v24; // [sp+7Ch] [bp-DCh]@21
  float v25; // [sp+80h] [bp-D8h]@21
  float v26[3]; // [sp+A8h] [bp-B0h]@21
  int v27; // [sp+C4h] [bp-94h]@29
  float v28[3]; // [sp+D8h] [bp-80h]@30
  float v29[3]; // [sp+108h] [bp-50h]@37
  CMapData *v30; // [sp+128h] [bp-30h]@7
  float *Tar; // [sp+130h] [bp-28h]@19
  int v32; // [sp+138h] [bp-20h]@21
  int v33; // [sp+13Ch] [bp-1Ch]@21
  CMapData *v34; // [sp+140h] [bp-18h]@30
  CMapData *v35; // [sp+148h] [bp-10h]@37
  CMonsterAI *pHFS; // [sp+160h] [bp+8h]@1
  CMonster *pMona; // [sp+168h] [bp+10h]@1
  int v38; // [sp+170h] [bp+18h]@1

  v38 = bAttackState;
  pMona = pMon;
  pHFS = pAI;
  v4 = &v14;
  for (signed __int64 i = 84; i > 0; --i)
  {
    *(DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  if ( CMonster::IsRoateMonster(pMon) && !v38 )
  {
    Get3DSqrt(pMona->m_fCreatePos, pMona->m_fCurPos);
    v15 = (signed int)ffloor(a4);
    if ( v15 > 5 )
    {
      v30 = pMona->m_pCurMap;
      if ( (unsigned int)CBsp::CanYouGoThere(
                           v30->m_Level.mBsp,
                           pMona->m_fCurPos,
                           pMona->m_fCreatePos,
                           (float (*)[3])v16) )
      {
        Us_HFSM::SendMsg((Us_HFSM *)&pHFS->vfptr, 3u, 0x24u, 0i64);
        DfAIMgr::ChangeTargetPos(pMona, pMona->m_fCreatePos);
        result = 1;
      }
      else
      {
        CMonsterHelper::TransPort(pMona, pMona->m_fCreatePos);
        result = 1;
      }
      return result;
    }
  }
  if ( pMona->m_pMonRec->m_bMonsterCondition != 1 )
  {
    if ( CMonsterHierarchy::GetParent(&pMona->m_MonHierarcy) )
    {
      v19 = pMona->m_pMonRec->m_nGuardingArea;
      Tar = pMona->m_fCurPos;
      v7 = CMonsterHierarchy::GetParent(&pMona->m_MonHierarcy);
      Get3DSqrt(v7->m_fCurPos, Tar);
      v20 = (signed int)ffloor(a4);
      if ( v20 <= v19 || v38 )
      {
        if ( v20 > 300 && v38 )
        {
          v13 = CMonsterHierarchy::GetParent(&pMona->m_MonHierarcy);
          CMonsterHierarchy::PopChildMon(&v13->m_MonHierarcy, pMona);
          CMonster::Command_ChildMonDestroy(pMona, 0x3A98u);
          return 1i64;
        }
      }
      else
      {
        v8 = CMonsterHierarchy::GetParent(&pMona->m_MonHierarcy);
        GetYAngle(pMona->m_fCurPos, v8->m_fCurPos);
        v21 = a4;
        v22 = 6.283185307 * a4 / 65535.0;
        *(float *)&v9 = sin_0(v22);
        v32 = v9;
        v23 = pMona->m_fCurPos[0] - (float)(*(float *)&v9 * (float)(50 - rand() % 100 + v20));
        *(float *)&v10 = cos_0(v22);
        v33 = v10;
        v25 = pMona->m_fCurPos[2] - (float)(*(float *)&v10 * (float)(50 - rand() % 100 + v20));
        v11 = pMona->m_fCurPos[1];
        v24 = pMona->m_fCurPos[1];
        if ( (unsigned int)CBsp::CanYouGoThere(
                             pMona->m_pCurMap->m_Level.mBsp,
                             pMona->m_fCurPos,
                             &v23,
                             (float (*)[3])v26) )
        {
          Us_HFSM::SendMsg((Us_HFSM *)&pHFS->vfptr, 3u, 0x23u, 0i64);
          CMonster::GeEmotionImpStdTime(pMona);
          Us_HFSM::SetLoopTime((Us_HFSM *)&pHFS->vfptr, 3, (signed int)ffloor(v11));
          v12 = CMonsterHierarchy::GetParent(&pMona->m_MonHierarcy);
          DfAIMgr::ChangeTargetPos(pMona, v12->m_fCurPos);
          return 1i64;
        }
      }
    }
    else if ( !v38 )
    {
      Get3DSqrt(pMona->m_fCreatePos, pMona->m_fCurPos);
      v27 = (signed int)ffloor(a4);
      if ( v27 <= 200 )
      {
        if ( v27 < 200 && v27 >= 150 )
        {
          v35 = pMona->m_pCurMap;
          if ( (unsigned int)CBsp::CanYouGoThere(
                               v35->m_Level.mBsp,
                               pMona->m_fCurPos,
                               pMona->m_fCreatePos,
                               (float (*)[3])v29) )
          {
            Us_HFSM::SendMsg((Us_HFSM *)&pHFS->vfptr, 3u, 0x24u, 0i64);
            DfAIMgr::ChangeTargetPos(pMona, pMona->m_fCreatePos);
            return 1i64;
          }
        }
      }
      else
      {
        v34 = pMona->m_pCurMap;
        if ( (unsigned int)CBsp::CanYouGoThere(
                             v34->m_Level.mBsp,
                             pMona->m_fCurPos,
                             pMona->m_fCreatePos,
                             (float (*)[3])v28) )
        {
          Us_HFSM::SendMsg((Us_HFSM *)&pHFS->vfptr, 3u, 0x23u, 0i64);
          CMonster::GeEmotionImpStdTime(pMona);
          Us_HFSM::SetLoopTime((Us_HFSM *)&pHFS->vfptr, 3, (signed int)ffloor(a4));
          DfAIMgr::ChangeTargetPos(pMona, pMona->m_fCreatePos);
          return 1i64;
        }
        if ( v27 > 400 )
        {
          CMonsterHelper::TransPort(pMona, pMona->m_fCreatePos);
          return 1i64;
        }
      }
    }
    return 0i64;
  }
  v17 = pMona->m_pMonRec->m_nMinMoveArea;
  Get3DSqrt(pMona->m_fCreatePos, pMona->m_fCurPos);
  v18 = (signed int)ffloor(a4);
  if ( v18 > 2 * v17 )
  {
    CMonsterHelper::TransPort(pMona, pMona->m_fCreatePos);
    return 1i64;
  }
  if ( v18 <= v17 )
    return 0i64;
  if ( v38 )
  {
    Us_HFSM::SendMsg((Us_HFSM *)&pHFS->vfptr, 5u, 0x2Fu, 0i64);
    CMonsterHelper::TransPort(pMona, pMona->m_fCreatePos);
    CMonster::SetAttackTarget(pMona, 0i64);
    Us_HFSM::SendMsg((Us_HFSM *)&pHFS->vfptr, 6u, 0x26u, 0i64);
    result = 1;
  }
  else
  {
    Us_HFSM::SendMsg((Us_HFSM *)&pHFS->vfptr, 3u, 0x23u, 0i64);
    CMonster::GeEmotionImpStdTime(pMona);
    Us_HFSM::SetLoopTime((Us_HFSM *)&pHFS->vfptr, 3, (signed int)ffloor(a4));
    DfAIMgr::ChangeTargetPos(pMona, pMona->m_fCreatePos);
    result = 1;
  }
  return result;
}


