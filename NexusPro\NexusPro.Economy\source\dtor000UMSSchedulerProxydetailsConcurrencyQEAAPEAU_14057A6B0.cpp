﻿/*
 * Function: ?dtor_0@?0???0UMSSchedulerProxy@details@Concurrency@@QEAA@PEAUIScheduler@2@PEAVResourceManager@12@AEBVSchedulerPolicy@2@@Z@4HA
 * Address: 0x14057A6B0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Required includes for Concurrency namespace
#include <ppl.h>
#include <ppltasks.h>
#include <concurrent_vector.h>



int __fastcallConcurrency::details::UMSSchedulerProxy::UMSSchedulerProxy'::1'::dtor_0(__int64 a1, __int64 a2)
{
  return CryptoPP::clonable_ptr<CryptoPP::ModularArithmetic> ::~clonable_ptr<CryptoPP::ModularArithmetic>(*(QWORD *)(a2 + 160) + 8i64);
}


