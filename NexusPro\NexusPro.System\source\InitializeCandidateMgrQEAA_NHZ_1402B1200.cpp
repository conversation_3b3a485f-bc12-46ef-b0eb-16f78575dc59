﻿/*
 * Function: ?Initialize@CandidateMgr@@QEAA_NH@Z
 * Address: 0x1402B1200
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// External symbol declarations for decompiled code
extern "C" ULONG_PTR _security_cookie;



char __fastcall CandidateMgr::Initialize(CandidateMgr *this, int nMaxNum)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  unsigned int v4; // eax@4
  unsigned int v5; // eax@4
  __int64 v7; // [sp+0h] [bp-1C8h]@1
  char _Dest[256]; // [sp+40h] [bp-188h]@4
  int j; // [sp+144h] [bp-84h]@4
  int __n[2]; // [sp+150h] [bp-78h]@6
  void *v11; // [sp+158h] [bp-70h]@9
  void *__t; // [sp+160h] [bp-68h]@6
  int v13[2]; // [sp+168h] [bp-60h]@11
  void *v14; // [sp+170h] [bp-58h]@14
  void *v15; // [sp+178h] [bp-50h]@11
  void *v16; // [sp+180h] [bp-48h]@19
  void *v17; // [sp+188h] [bp-40h]@16
  __int64 v18; // [sp+190h] [bp-38h]@4
  void *v19; // [sp+198h] [bp-30h]@7
  void *v20; // [sp+1A0h] [bp-28h]@12
  void *v21; // [sp+1A8h] [bp-20h]@17
  unsigned __int64 v22; // [sp+1B0h] [bp-18h]@4
  CandidateMgr *v23; // [sp+1D0h] [bp+8h]@1
  int v24; // [sp+1D8h] [bp+10h]@1

  v24 = nMaxNum;
  v23 = this;
  v2 = &v7;
  for (signed __int64 i = 112; i > 0; --i)
  {
    *(DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v18 = -2i64;
  v22 = (unsigned __int64)&v7 ^ _security_cookie;
  _Dest[0] = 0;
  memset(&_Dest[1], 0, 0xFFui64);
  v4 = GetKorLocalTime();
  sprintf_s<256>((char (*)[256])_Dest, "..\\ZoneServerLog\\SystemLog\\Patriarch\\CandidateMgr_%d.log", v4);
  CLogFile::SetWriteLogFile(&v23->_kSysLog, _Dest, 1, 0, 1, 1);
  v5 = GetKorLocalTime();
  sprintf_s<256>((char (*)[256])_Dest, "..\\ZoneServerLog\\ServiceLog\\Patriarch\\VoteResult_%d.log", v5);
  CLogFile::SetWriteLogFile(&v23->_kVoteResultLog, _Dest, 1, 0, 1, 1);
  v23->_nMaxNum = v24;
  for ( j = 0; j < 3; ++j )
  {
    *(QWORD *)__n = v23->_nMaxNum;
    __t = operator new[](saturated_mul(0x58ui64, *(unsigned __int64 *)__n));
    if ( __t )
    {
      `vector constructor iterator'(__t, 0x58ui64, __n[0], (void *(__cdecl *)(void *))_candidate_info::_candidate_info);
      v19 = __t;
    }
    else
    {
      v19 = 0;
    }
    v11 = v19;
    v23->_kCandidate[j] = (_candidate_info *)v19;
    if ( !v23->_kCandidate[j] )
      return 0;
    *(QWORD *)v13 = v23->_nMaxNum;
    v15 = operator new[](saturated_mul(0x58ui64, *(unsigned __int64 *)v13));
    if ( v15 )
    {
      `vector constructor iterator'(v15, 0x58ui64, v13[0], (void *(__cdecl *)(void *))_candidate_info::_candidate_info);
      v20 = v15;
    }
    else
    {
      v20 = 0;
    }
    v14 = v20;
    v23->_kCandidate_old[j] = (_candidate_info *)v20;
    if ( !v23->_kCandidate_old[j] )
      return 0;
    v17 = operator new[](0x318ui64);
    if ( v17 )
    {
      `vector constructor iterator'(v17, 0x58ui64, 9, (void *(__cdecl *)(void *))_candidate_info::_candidate_info);
      v21 = v17;
    }
    else
    {
      v21 = 0;
    }
    v16 = v21;
    v23->_kPatriarchGroup[j] = (_candidate_info *)v21;
    if ( !v23->_kPatriarchGroup[j] )
      return 0;
  }
  return 1;
}


