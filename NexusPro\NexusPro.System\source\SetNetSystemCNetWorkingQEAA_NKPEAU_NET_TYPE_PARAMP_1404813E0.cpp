﻿/*
 * Function: ?SetNetSystem@CNetWorking@@QEAA_NKPEAU_NET_TYPE_PARAM@@PEAD1@Z
 * Address: 0x1404813E0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// External symbol declarations for decompiled code
extern "C" ULONG_PTR _security_cookie;



char __fastcall CNetWorking::SetNetSystem(CNetWorking *this, unsigned int dwUseProcessNum, _NET_TYPE_PARAM *pType, char *szSystemName, char *pszLogPath)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@11
  int v8; // eax@17
  unsigned __int32 v9; // eax@20
  __int64 v10; // [sp+0h] [bp-258h]@1
  bool bDate[4]; // [sp+20h] [bp-238h]@7
  char szFileName; // [sp+40h] [bp-218h]@7
  char ReturnedString; // [sp+D8h] [bp-180h]@7
  char v14; // [sp+D9h] [bp-17Fh]@7
  int nIndex; // [sp+F4h] [bp-164h]@12
  char Filename; // [sp+110h] [bp-148h]@17
  char v17; // [sp+111h] [bp-147h]@17
  char *v18; // [sp+228h] [bp-30h]@17
  unsigned int v19; // [sp+230h] [bp-28h]@20
  unsigned __int64 v20; // [sp+240h] [bp-18h]@4
  CNetWorking *pNetwork; // [sp+260h] [bp+8h]@1
  _NET_TYPE_PARAM *v22; // [sp+270h] [bp+18h]@1

  v22 = pType;
  pNetwork = this;
  v5 = &v10;
  for (signed __int64 i = 148; i > 0; --i)
  {
    *(DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v20 = (unsigned __int64)&v10 ^ _security_cookie;
  pNetwork->m_dwUseProcessNum = dwUseProcessNum;
  wsprintfA(pNetwork->m_szSystemName, "%s", szSystemName);
  if ( pszLogPath )
    strcpy_s(pNetwork->m_szLogPath, 0x80ui64, pszLogPath);
  else
    strcpy_s(pNetwork->m_szLogPath, 0x80ui64, ".\\NetLog");
  CreateDirectoryA(pNetwork->m_szLogPath, 0i64);
  *(DWORD *)bDate = GetKorLocalTime();
  wsprintfA(&szFileName, "%s\\%s_Sys%d.log", pNetwork->m_szLogPath, pNetwork->m_szSystemName);
  CLogFile::SetWriteLogFile(&pNetwork->m_LogFile, &szFileName, 1, 1, 1, 1);
  *(DWORD *)bDate = GetKorLocalTime();
  wsprintfA(&szFileName, "%s\\%s_CcrFgSys%d.log", pNetwork->m_szLogPath, pNetwork->m_szSystemName);
  CLogFile::SetWriteLogFile(&g_FgLogFile, &szFileName, 1, 1, 1, 1);
  ReturnedString = 0;
  memset(&v14, 0, 0xFui64);
  GetPrivateProfileStringA("FireGuard Use", "Use", "TRUE", &ReturnedString, 0x10u, ".\\fireguard\\fgrs.ini");
  pNetwork->m_bUseFG = strcmp_0(&ReturnedString, "FALSE") != 0;
  GetPrivateProfileStringA("System", "WorldName", "X", pNetwork->m_szServerName, 0x21u, "..\\WorldInfo\\WorldInfo.ini");
  if ( pNetwork->m_szServerName[0] == 88 )
  {
    CLogFile::Write(&g_FgLogFile, "FG Error : WorldName Read Failed In \"WorldInfo.ini\" File");
    result = 0;
  }
  else
  {
    for ( nIndex = 0; nIndex < pNetwork->m_dwUseProcessNum; ++nIndex )
    {
      bDate[0] = pNetwork->m_bUseFG;
      if ( !CNetProcess::SetProcess(&pNetwork->m_Process[nIndex], nIndex, &v22[nIndex], pNetwork, bDate[0]) )
        return 0;
      if ( !nIndex )
      {
        Filename = 0;
        memset(&v17, 0, 0x103ui64);
        GetModuleFileNameA(0i64, &Filename, 0x104u);
        v18 = _tcsrchr(&Filename, 0x5Cu) + 1;
        *v18 = 0;
        strcat_s(&Filename, 0x104ui64, "fireguard\\");
        v8 = strlen_0(&Filename);
        if ( AddEnvVariable("path", &Filename, v8) <= 0 )
        {
          CLogFile::Write(
            &pNetwork->m_LogFile,
            "SetNetSystem(%d) CCRFG SERVER : AddEnvVariable() Fail",
            (unsigned int)nIndex);
          return 0;
        }
        if ( pNetwork->m_bUseFG )
        {
          g_pfnCallBack.pfunc = (int (__cdecl *)(int, void *, void *, int, void *))CcrFgCallback;
          v9 = strlen_0(pNetwork->m_szServerName);
          v19 = _CcrFG_rs_Initialize(
                  (int (__stdcall *)(__int32, void *, void *, int, void *))g_pfnCallBack.pfunc,
                  (unsigned __int8 *)pNetwork->m_szServerName,
                  v9);
          if ( (signed int)v19 < 1 )
          {
            *(DWORD *)bDate = _CcrFG_rs_GetLastError();
            CLogFile::Write(
              &pNetwork->m_LogFile,
              "SetNetSystem(%d) CCRFG SERVER : _CcrFG_rs_Initialize() Fail, nRet(%#x), _CcrFG_rs_GetLastError(%#x)",
              (unsigned int)nIndex,
              v19);
            return 0;
          }
        }
        qword_184A69528 = pNetwork;
        g_FGSendData.pTargetProc = &pNetwork->m_Process[nIndex];
      }
    }
    result = 1;
  }
  return result;
}


