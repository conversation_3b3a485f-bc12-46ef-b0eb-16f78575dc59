﻿/*
 * Function: ??$_Uninit_move@PEAVCMoveMapLimitRightInfo@@PEAV1@V?$allocator@VCMoveMapLimitRightInfo@@@std@@U_Undefined_move_tag@3@@std@@YAPEAVCMoveMapLimitRightInfo@@PEAV1@00AEAV?$allocator@VCMoveMapLimitRightInfo@@@0@U_Undefined_move_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x1403B36B0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


CMoveMapLimitRightInfo *__fastcall std::_Uninit_move<CMoveMapLimitRightInfo *,CMoveMapLimitRightInfo *,std::allocator<CMoveMapLimitRightInfo>,std::_Undefined_move_tag>(CMoveMapLimitRightInfo *_First, CMoveMapLimitRightInfo *_Last, CMoveMapLimitRightInfo *_Dest, std::allocator<CMoveMapLimitRightInfo> *_Al, std::_Undefined_move_tag __formal, std::_Range_checked_iterator_tag a6)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v9; // [sp+0h] [bp-28h]@1
  CMoveMapLimitRightInfo *_Firsta; // [sp+30h] [bp+8h]@1

  _Firsta = _First;
  v6 = &v9;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  return stdext::unchecked_uninitialized_copy<CMoveMapLimitRightInfo *,CMoveMapLimitRightInfo *,std::allocator<CMoveMapLimitRightInfo>>(
           _Firsta,
           _Last,
           _Dest,
           _Al);
}

