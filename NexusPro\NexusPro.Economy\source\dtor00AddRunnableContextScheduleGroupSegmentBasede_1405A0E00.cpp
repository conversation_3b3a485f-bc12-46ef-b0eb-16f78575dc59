﻿/*
 * Function: ?dtor_0@?0??AddRunnableContext@ScheduleGroupSegmentBase@details@Concurrency@@IEAAXPEAVInternalContextBase@23@Vlocation@3@@Z@4HA
 * Address: 0x1405A0E00
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Required includes for Concurrency namespace
#include <ppl.h>
#include <ppltasks.h>
#include <concurrent_vector.h>



int __fastcallConcurrency::details::ScheduleGroupSegmentBase::AddRunnableContext'::1'::dtor_0(__int64 a1, __int64 a2)
{
  __int64 v2; // rcx@1

  v2 = *(QWORD *)(a2 + 192);
  return std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer> ,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>>::~_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>>();
}


