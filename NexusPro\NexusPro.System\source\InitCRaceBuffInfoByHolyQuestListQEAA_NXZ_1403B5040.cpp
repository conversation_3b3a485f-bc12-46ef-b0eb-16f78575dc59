﻿/*
 * Function: ?Init@CRaceBuffInfoByHolyQuestList@@QEAA_NXZ
 * Address: 0x1403B5040
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


char __fastcall CRaceBuffInfoByHolyQuestList::Init(CRaceBuffInfoByHolyQuestList *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  unsigned __int64 v4; // rax@6
  unsigned __int64 v5; // rax@7
  __int64 v6; // rax@11
  CRaceBuffInfoByHolyQuestfGroup **v7; // rax@13
  CRaceBuffInfoByHolyQuestfGroup **v8; // rax@15
  __int64 v9; // [sp+0h] [bp-68h]@1
  unsigned __int64 _Count; // [sp+20h] [bp-48h]@4
  CRaceBuffInfoByHolyQuestfGroup *_Val; // [sp+28h] [bp-40h]@6
  CRaceBuffInfoByHolyQuestfGroup *v12; // [sp+30h] [bp-38h]@13
  CRaceBuffInfoByHolyQuestfGroup *v13; // [sp+38h] [bp-30h]@10
  __int64 v14; // [sp+40h] [bp-28h]@4
  __int64 v15; // [sp+48h] [bp-20h]@6
  CRaceBuffInfoByHolyQuestfGroup *v16; // [sp+50h] [bp-18h]@11
  CRaceBuffInfoByHolyQuestList *v17; // [sp+70h] [bp+8h]@1

  v17 = this;
  v1 = &v9;
  for (signed __int64 i = 24; i > 0; --i)
  {
    *(DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v14 = -2i64;
  ((DWORD)(_Count) = GetPrivateProfileIntA("RaceBuff", "thCnt", 0, ".\\Initialize\\NewHolySystem.ini");
  if ( (DWORD)_Count )
  {
    _Val = 0;
    std::vector<CRaceBuffInfoByHolyQuestfGroup *,std::allocator<CRaceBuffInfoByHolyQuestfGroup *>>::assign(
      &v17->m_vecInfo,
      (unsigned int)_Count,
      &_Val);
    v15 = (unsigned int)_Count;
    v4 = std::vector<CRaceBuffInfoByHolyQuestfGroup *,std::allocator<CRaceBuffInfoByHolyQuestfGroup *>>::size(&v17->m_vecInfo);
    if ( v15 == v4 )
    {
      for ( HIDWORD(_Count) = 0; HIDWORD(_Count) < (unsigned int)_Count; ++HIDWORD(_Count) )
      {
        v13 = (CRaceBuffInfoByHolyQuestfGroup *)operator new(0x30ui64);
        if ( v13 )
        {
          CRaceBuffInfoByHolyQuestfGroup::CRaceBuffInfoByHolyQuestfGroup(v13, HIDWORD(_Count));
          v16 = (CRaceBuffInfoByHolyQuestfGroup *)v6;
        }
        else
        {
          v16 = 0;
        }
        v12 = v16;
        v7 = std::vector<CRaceBuffInfoByHolyQuestfGroup *,std::allocator<CRaceBuffInfoByHolyQuestfGroup *>>::operator[](
               &v17->m_vecInfo,
               HIDWORD(_Count));
        *v7 = v12;
        if ( !*std::vector<CRaceBuffInfoByHolyQuestfGroup *,std::allocator<CRaceBuffInfoByHolyQuestfGroup *>>::operator[](
                 &v17->m_vecInfo,
                 HIDWORD(_Count)) )
        {
          CLogFile::Write(
            &stru_1799C8F30,
            "CRaceBuffInfoByHolyQuestList::Init() : new CRaceBuffInfoByHolyQuestfGroup(%u) NULL!",
            HIDWORD(_Count));
          return 0;
        }
        v8 = std::vector<CRaceBuffInfoByHolyQuestfGroup *,std::allocator<CRaceBuffInfoByHolyQuestfGroup *>>::operator[](
               &v17->m_vecInfo,
               HIDWORD(_Count));
        if ( !CRaceBuffInfoByHolyQuestfGroup::Init(*v8) )
        {
          CLogFile::Write(
            &stru_1799C8F30,
            "CRaceBuffInfoByHolyQuestList::Init() : m_vecInfo[i(%u)]->Init() Fail!",
            HIDWORD(_Count));
          return 0;
        }
      }
      result = 1;
    }
    else
    {
      v5 = std::vector<CRaceBuffInfoByHolyQuestfGroup *,std::allocator<CRaceBuffInfoByHolyQuestfGroup *>>::size(&v17->m_vecInfo);
      CLogFile::Write(
        &stru_1799C8F30,
        "CRaceBuffInfoByHolyQuestList::Init() : thCnt(%u) != m_vecInfo.size()(%d)",
        (unsigned int)_Count,
        v5);
      result = 0;
    }
  }
  else
  {
    result = 1;
  }
  return result;
}


