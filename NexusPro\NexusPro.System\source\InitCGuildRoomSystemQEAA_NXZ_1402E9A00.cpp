﻿/*
 * Function: ?Init@CGuildRoomSystem@@QEAA_NXZ
 * Address: 0x1402E9A00
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


char __fastcall CGuildRoomSystem::Init(CGuildRoomSystem *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  CGuildRoomInfo *v3; // rax@4
  char result; // al@5
  CGuildRoomInfo *v5; // rax@39
  CGuildRoomInfo *v6; // rax@40
  CGuildRoomInfo *v7; // rax@45
  CGuildRoomInfo *v8; // rax@46
  CGuildRoomInfo *v9; // rax@51
  CGuildRoomInfo *v10; // rax@52
  __int64 v11; // [sp+0h] [bp-D8h]@1
  char byRace; // [sp+20h] [bp-B8h]@39
  CMapData *v13; // [sp+30h] [bp-A8h]@6
  _portal_dummy *v14; // [sp+38h] [bp-A0h]@20
  int j; // [sp+40h] [bp-98h]@36
  CGuildRoomInfo v16; // [sp+48h] [bp-90h]@4
  __int64 v17; // [sp+A8h] [bp-30h]@4
  CGuildRoomInfo *v18; // [sp+B0h] [bp-28h]@4
  CGuildRoomInfo *_Val; // [sp+B8h] [bp-20h]@4
  int v20; // [sp+C0h] [bp-18h]@40
  int v21; // [sp+C4h] [bp-14h]@46
  int v22; // [sp+C8h] [bp-10h]@52
  CGuildRoomSystem *v23; // [sp+E0h] [bp+8h]@1

  v23 = this;
  v1 = &v11;
  for (signed __int64 i = 52; i > 0; --i)
  {
    *(DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v17 = -2i64;
  CGuildRoomInfo::CGuildRoomInfo(&v16);
  v18 = v3;
  _Val = v3;
  std::vector<CGuildRoomInfo,std::allocator<CGuildRoomInfo>>::assign(&v23->m_vecGuildRoom, 0x5Aui64, v3);
  CGuildRoomInfo::~CGuildRoomInfo(&v16);
  if ( std::vector<CGuildRoomInfo,std::allocator<CGuildRoomInfo>>::size(&v23->m_vecGuildRoom) == 90 )
  {
    v13 = 0;
    v13 = CMapOperation::GetMap(&g_MapOper, "BellaGSD");
    if ( v13 )
    {
      v23->m_pRoomMap[0][0] = v13;
      v13 = CMapOperation::GetMap(&g_MapOper, "CoraGSD");
      if ( v13 )
      {
        v23->m_pRoomMap[1][0] = v13;
        v13 = CMapOperation::GetMap(&g_MapOper, "AccGSD");
        if ( v13 )
        {
          v23->m_pRoomMap[2][0] = v13;
          v13 = CMapOperation::GetMap(&g_MapOper, "BellaGSP");
          if ( v13 )
          {
            v23->m_pRoomMap[0][1] = v13;
            v13 = CMapOperation::GetMap(&g_MapOper, "CoraGSP");
            if ( v13 )
            {
              v23->m_pRoomMap[1][1] = v13;
              v13 = CMapOperation::GetMap(&g_MapOper, "AccGSP");
              if ( v13 )
              {
                v23->m_pRoomMap[2][1] = v13;
                v13 = CMapOperation::GetMap(&g_MapOper, "NeutralB");
                if ( v13 )
                {
                  CGuildRoomInfo::sm_neutal_map = (struct CMapData * near *)v13;
                  v14 = CMapData::GetPortal(v13, "dpfrom_bellato_camp");
                  if ( v14 )
                  {
                    if ( v14->m_pDumPos )
                    {
                      CGuildRoomInfo::sm_neutral_hq_dummy = (struct _dummy_position * near *)v14->m_pDumPos;
                      v13 = CMapOperation::GetMap(&g_MapOper, "NeutralC");
                      if ( v13 )
                      {
                        qword_184A6F640 = (__int64)v13;
                        v14 = CMapData::GetPortal(v13, "dpfrom_cora_camp");
                        if ( v14 )
                        {
                          if ( v14->m_pDumPos )
                          {
                            qword_184A6F628 = (__int64)v14->m_pDumPos;
                            v13 = CMapOperation::GetMap(&g_MapOper, "NeutralA");
                            if ( v13 )
                            {
                              qword_184A6F648 = (__int64)v13;
                              v14 = CMapData::GetPortal(v13, "dpfrom_accretia_camp");
                              if ( v14 )
                              {
                                if ( v14->m_pDumPos )
                                {
                                  qword_184A6F630 = (__int64)v14->m_pDumPos;
                                  for ( j = 0; j < 30; ++j )
                                  {
                                    if ( j >= 20 )
                                    {
                                      v20 = j - 20;
                                      v6 = std::vector<CGuildRoomInfo,std::allocator<CGuildRoomInfo>>::operator[](
                                             &v23->m_vecGuildRoom,
                                             j);
                                      byRace = 0;
                                      CGuildRoomInfo::SetRoomMapInfo(v6, v23->m_pRoomMap[0][1], v20, 1, 0);
                                    }
                                    else
                                    {
                                      v5 = std::vector<CGuildRoomInfo,std::allocator<CGuildRoomInfo>>::operator[](
                                             &v23->m_vecGuildRoom,
                                             j);
                                      byRace = 0;
                                      CGuildRoomInfo::SetRoomMapInfo(v5, v23->m_pRoomMap[0][0], j, 0, 0);
                                    }
                                  }
                                  for ( j = 0; j < 30; ++j )
                                  {
                                    if ( j >= 20 )
                                    {
                                      v21 = j - 20;
                                      v8 = std::vector<CGuildRoomInfo,std::allocator<CGuildRoomInfo>>::operator[](
                                             &v23->m_vecGuildRoom,
                                             j + 30);
                                      byRace = 1;
                                      CGuildRoomInfo::SetRoomMapInfo(v8, v23->m_pRoomMap[1][1], v21, 1, 1);
                                    }
                                    else
                                    {
                                      v7 = std::vector<CGuildRoomInfo,std::allocator<CGuildRoomInfo>>::operator[](
                                             &v23->m_vecGuildRoom,
                                             j + 30);
                                      byRace = 1;
                                      CGuildRoomInfo::SetRoomMapInfo(v7, v23->m_pRoomMap[1][0], j, 0, 1);
                                    }
                                  }
                                  for ( j = 0; j < 30; ++j )
                                  {
                                    if ( j >= 20 )
                                    {
                                      v22 = j - 20;
                                      v10 = std::vector<CGuildRoomInfo,std::allocator<CGuildRoomInfo>>::operator[](
                                              &v23->m_vecGuildRoom,
                                              j + 60);
                                      byRace = 2;
                                      CGuildRoomInfo::SetRoomMapInfo(v10, v23->m_pRoomMap[2][1], v22, 1, 2);
                                    }
                                    else
                                    {
                                      v9 = std::vector<CGuildRoomInfo,std::allocator<CGuildRoomInfo>>::operator[](
                                             &v23->m_vecGuildRoom,
                                             j + 60);
                                      byRace = 2;
                                      CGuildRoomInfo::SetRoomMapInfo(v9, v23->m_pRoomMap[2][0], j, 0, 2);
                                    }
                                  }
                                  ((DWORD)(CGuildRoomInfo::sm_RoomInfo) = 5000000;
                                  dword_184A6F654 = 604800;
                                  byte_184A6F658 = 2;
                                  byte_184A6F659 = 30;
                                  dword_184A6F65C = 20000000;
                                  dword_184A6F660 = 2592000;
                                  byte_184A6F664 = 3;
                                  byte_184A6F665 = 30;
                                  result = 1;
                                }
                                else
                                {
                                  CLogFile::Write(
                                    &stru_1799C8F30,
                                    "CGuildRoomSystem::Init() : Portal(%s)->m_pDumPos is NULL!",
                                    "dpfrom_accretia_camp");
                                  result = 0;
                                }
                              }
                              else
                              {
                                CLogFile::Write(
                                  &stru_1799C8F30,
                                  "CGuildRoomSystem::Init() : pMap->GetPortal(acc_hq_dummy(%s)) is NULL!",
                                  "dpfrom_accretia_camp");
                                result = 0;
                              }
                            }
                            else
                            {
                              CLogFile::Write(
                                &stru_1799C8F30,
                                "CGuildRoomSystem::Init() : g_MapOper.GetMap(acc_neutral_map(%s)) is NULL!",
                                "NeutralA");
                              result = 0;
                            }
                          }
                          else
                          {
                            CLogFile::Write(
                              &stru_1799C8F30,
                              "CGuildRoomSystem::Init() : Portal(%s)->m_pDumPos is NULL!",
                              "dpfrom_cora_camp");
                            result = 0;
                          }
                        }
                        else
                        {
                          CLogFile::Write(
                            &stru_1799C8F30,
                            "CGuildRoomSystem::Init() : pMap->GetPortal(cora_hq_dummy(%s)) is NULL!",
                            "dpfrom_cora_camp");
                          result = 0;
                        }
                      }
                      else
                      {
                        CLogFile::Write(
                          &stru_1799C8F30,
                          "CGuildRoomSystem::Init() : g_MapOper.GetMap(cora_neutral_map(%s)) is NULL!",
                          "NeutralC");
                        result = 0;
                      }
                    }
                    else
                    {
                      CLogFile::Write(
                        &stru_1799C8F30,
                        "CGuildRoomSystem::Init() : Portal(%s)->m_pDumPos is NULL!",
                        "dpfrom_bellato_camp");
                      result = 0;
                    }
                  }
                  else
                  {
                    CLogFile::Write(
                      &stru_1799C8F30,
                      "CGuildRoomSystem::Init() : pMap->GetPortal(bella_hq_dummy(%s)) is NULL!",
                      "dpfrom_bellato_camp");
                    result = 0;
                  }
                }
                else
                {
                  CLogFile::Write(
                    &stru_1799C8F30,
                    "CGuildRoomSystem::Init() : g_MapOper.GetMap(bella_neutral_map(%s)) is NULL!",
                    "NeutralB");
                  result = 0;
                }
              }
              else
              {
                CLogFile::Write(
                  &stru_1799C8F30,
                  "CGuildRoomSystem::Init() : acc_special_map(%s) Not Exist Map Data!",
                  "AccGSP");
                result = 0;
              }
            }
            else
            {
              CLogFile::Write(
                &stru_1799C8F30,
                "CGuildRoomSystem::Init() : cora_special_map(%s) Not Exist Map Data!",
                "CoraGSP");
              result = 0;
            }
          }
          else
          {
            CLogFile::Write(
              &stru_1799C8F30,
              "CGuildRoomSystem::Init() : bella_special_map(%s) Not Exist Map Data!",
              "BellaGSP");
            result = 0;
          }
        }
        else
        {
          CLogFile::Write(
            &stru_1799C8F30,
            "CGuildRoomSystem::Init() : cora_standard_map(%s) Not Exist Map Data!",
            "CoraGSD");
          result = 0;
        }
      }
      else
      {
        CLogFile::Write(
          &stru_1799C8F30,
          "CGuildRoomSystem::Init() : cora_standard_map(%s) Not Exist Map Data!",
          "CoraGSD");
        result = 0;
      }
    }
    else
    {
      CLogFile::Write(
        &stru_1799C8F30,
        "CGuildRoomSystem::Init() : bella_standard_map(%s) Not Exist Map Data!",
        "BellaGSD");
      result = 0;
    }
  }
  else
  {
    CLogFile::Write(
      &stru_1799C8F30,
      "CGuildRoomSystem::Init() : RACE_NUM*MAX_GUILD_ROOM != m_vecGuildRoom.size() Fail!");
    result = 0;
  }
  return result;
}


