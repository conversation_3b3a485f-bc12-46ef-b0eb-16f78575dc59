﻿/*
 * Function: ?SetConfig@TimeLimitMgr@@QEAA_NGGGGG@Z
 * Address: 0x14040EDB0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// External symbol declarations for decompiled code
extern "C" ULONG_PTR _security_cookie;



char __fastcall TimeLimitMgr::SetConfig(TimeLimitMgr *this, unsigned __int16 time1, unsigned __int16 time2, unsigned __int16 time3, unsigned __int16 time4, unsigned __int16 time5)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v9; // [sp+0h] [bp-68h]@1
  unsigned __int16 v10; // [sp+28h] [bp-40h]@4
  unsigned __int16 v11; // [sp+2Ah] [bp-3Eh]@4
  unsigned __int16 v12; // [sp+2Ch] [bp-3Ch]@4
  unsigned __int16 v13; // [sp+2Eh] [bp-3Ah]@4
  unsigned __int16 v14; // [sp+30h] [bp-38h]@4
  int j; // [sp+44h] [bp-24h]@4
  unsigned __int64 v16; // [sp+50h] [bp-18h]@4
  TimeLimitMgr *v17; // [sp+70h] [bp+8h]@1

  v17 = this;
  v6 = &v9;
  for (signed __int64 i = 24; i > 0; --i)
  {
    *(DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  v16 = (unsigned __int64)&v9 ^ _security_cookie;
  v10 = time1;
  v11 = time2;
  v12 = time3;
  v13 = time4;
  v14 = time5;
  for ( j = 0; j < v17->m_wPeriodCnt; ++j )
    v17->m_pwTime[j] = *(&v10 + j);
  return 1;
}


