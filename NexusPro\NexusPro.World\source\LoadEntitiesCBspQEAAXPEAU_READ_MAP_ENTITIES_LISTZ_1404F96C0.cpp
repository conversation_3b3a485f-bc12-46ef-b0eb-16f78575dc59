﻿/*
 * Function: ?LoadEntities@CBsp@@QEAAXPEAU_READ_MAP_ENTITIES_LIST@@@Z
 * Address: 0x1404F96C0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// External symbol declarations for decompiled code
extern "C" ULONG_PTR _security_cookie;



void __fastcall CBsp::LoadEntities(CBsp *this, struct _READ_MAP_ENTITIES_LIST *a2)
{
  struct _READ_MAP_ENTITIES_LIST *v2; // r14@1
  CBsp *v3; // rbx@1
  unsigned int v4; // ecx@1
  CEntity *v5; // rax@2
  CParticle *v6; // rax@2
  unsigned int v7; // er12@2
  __int64 v8; // rbp@3
  __int64 v9; // r13@3
  __int64 v10; // rcx@4
  char v11; // al@5
  _ENTITY_LIST *v12; // r8@6
  signed __int64 v13; // rsi@7
  __int64 v14; // rcx@7
  char *v15; // rdi@7
  char v16; // al@8
  __int64 v17; // rcx@10
  char *v18; // rdi@10
  char v19; // al@11
  _ENTITY_LIST *v20; // rax@14
  signed __int64 v21; // rsi@15
  signed int v22; // er8@18
  signed __int64 v23; // rsi@20
  unsigned int v24; // ebp@26
  __int64 v25; // rsi@27
  signed __int64 v26; // r12@27
  signed __int64 v27; // rax@29
  __int64 v28; // kr20_8@30
  struct _READ_MAP_ENTITIES_LIST *v29; // rax@31
  CParticle *v30; // rax@32
  CParticle *v31; // rdi@32
  __int64 v32; // [sp+0h] [bp-178h]@1
  struct _READ_MAP_ENTITIES_LIST *v33; // [sp+20h] [bp-158h]@1
  __int64 v34; // [sp+28h] [bp-150h]@1
  char v35[256]; // [sp+30h] [bp-148h]@5
  unsigned __int64 v36; // [sp+130h] [bp-48h]@1

  v34 = -2i64;
  v36 = (unsigned __int64)&v32 ^ _security_cookie;
  v2 = a2;
  v33 = a2;
  v3 = this;
  v4 = this->mEntityListNum;
  if ( v4 )
  {
    v5 = (CEntity *)Dmalloc(244 * v4);
    v3->mEntity = v5;
    memset_0(v5, 0, 244i64 * v3->mEntityListNum);
    v6 = (CParticle *)Dmalloc(1168 * v3->mEntityListNum);
    v3->mParticle = v6;
    memset_0(v6, 0, 1168i64 * v3->mEntityListNum);
    v3->mTotalAllocSize += 1412 * v3->mEntityListNum;
    SetMergeFileManager(&v3->mMapEntityMFM);
    v7 = 0;
    if ( v3->mEntityListNum )
    {
      v8 = 0;
      v9 = 0;
      do
      {
        v10 = 0;
        do
        {
          v11 = byte_184A790F0[v10];
          v35[v10++] = v11;
        }
        while ( v11 );
        v12 = v3->mEntityList;
        if ( v12[v8].Name[0] == 92 )
        {
          v13 = v7;
          v15 = &v35[strlen(v35) + 1];
          v14 = 0;
          do
          {
            v16 = v12[v7].Name[v14 + 1];
            v15[v14++ - 1] = v16;
          }
          while ( v16 );
        }
        else
        {
          v13 = v7;
          v18 = &v35[strlen(v35) + 1];
          v17 = 0;
          do
          {
            v19 = v12[v7].Name[v17];
            v18[v17++ - 1] = v19;
          }
          while ( v19 );
        }
        v12[v8].IsFileExist = 0;
        if ( !dword_184A797AC )
          v3->mEntityList[v8].ShaderID = 0;
        v20 = v3->mEntityList;
        if ( v20[v8].IsParticle )
        {
          v21 = v13;
          if ( (unsigned int)CParticle::LoadParticleSPT(&v3->mParticle[v21], v35, 0) )
          {
            v3->mEntityList[v8].IsFileExist = 1;
            CParticle::InitParticle(&v3->mParticle[v21]);
            CParticle::SetParticleState(&v3->mParticle[v21], 1u);
          }
          else
          {
            Warning(v35, " <- ÆÄÀÏÀÌ ¾ø°Å³ª, ÆÄÆ¼Å¬ spt°¡ ¾Æ´Ï´Ù.");
          }
        }
        else
        {
          v22 = 0;
          if ( v20[v8].ShaderID )
            v22 = 2;
          v23 = v13;
          if ( (unsigned int)CEntity::LoadEntity(&v3->mEntity[v23], v35, v22 | 0x20u) )
          {
            if ( v3->mEntityList[v8].Flag & 0x40 )
              v3->mEntity[v9].mFlag |= 0x40u;
            CEntity::RestoreTexMem(&v3->mEntity[v23]);
            v3->mEntityList[v8].IsFileExist = 1;
          }
        }
        ++v7;
        ++v9;
        ++v8;
      }
      while ( v7 < v3->mEntityListNum );
      v2 = v33;
    }
    v24 = 0;
    if ( v3->mMapEntitiesListNum )
    {
      v25 = 0;
      v26 = (signed __int64)&v2->Pos[1];
      do
      {
        if ( v3->mEntityList[*(WORD *)(v26 - 10)].IsFileExist )
        {
          v3->mMapEntitiesList[v25].ID = *(WORD *)(v26 - 10);
          v3->mMapEntitiesList[v25].Pos[0] = *(float *)(v26 - 4);
          v3->mMapEntitiesList[v25].Pos[1] = *(float *)v26;
          v3->mMapEntitiesList[v25].Pos[2] = *(float *)(v26 + 4);
          v3->mMapEntitiesList[v25].RotX = *(float *)(v26 + 8);
          v3->mMapEntitiesList[v25].RotY = *(float *)(v26 + 12);
          v3->mMapEntitiesList[v25].Scale = *(float *)(v26 - 8);
          v3->mMapEntitiesList[v25].BBMin[0] = *(WORD *)(v26 + 16);
          v3->mMapEntitiesList[v25].BBMin[1] = *(WORD *)(v26 + 18);
          v3->mMapEntitiesList[v25].BBMin[2] = *(WORD *)(v26 + 20);
          v3->mMapEntitiesList[v25].BBMax[0] = *(WORD *)(v26 + 22);
          v3->mMapEntitiesList[v25].BBMax[1] = *(WORD *)(v26 + 24);
          v3->mMapEntitiesList[v25].BBMax[2] = *(WORD *)(v26 + 26);
          v28 = rand();
          v3->mMapEntitiesList[v25].AddFrame = (float)((unsigned __int8)(BYTE4(v28) + v28) - BYTE4(v28)) * 0.25;
          v3->mMapEntitiesList[v25].Particle = 0;
          if ( v3->mEntityList[v3->mMapEntitiesList[v25].ID].IsParticle )
          {
            v29 = (struct _READ_MAP_ENTITIES_LIST *)operator new(0x490ui64);
            v33 = v29;
            if ( v29 )
            {
              ((DWORD)(v30) = CParticle::CParticle(v29);
              v31 = v30;
            }
            else
            {
              v31 = 0;
            }
            v3->mMapEntitiesList[v25].Particle = v31;
            memcpy_0(v31, &v3->mParticle[v3->mMapEntitiesList[v25].ID], 0x490ui64);
            CParticle::InitParticle(v31);
          }
        }
        else
        {
          v27 = (signed __int64)&v3->mMapEntitiesList[v24];
          *(QWORD *)v27 = 0;
          *(QWORD *)(v27 + 8) = 0;
          *(QWORD *)(v27 + 16) = 0;
          *(QWORD *)(v27 + 24) = 0;
          *(QWORD *)(v27 + 32) = 0;
          *(QWORD *)(v27 + 40) = 0;
          *(QWORD *)(v27 + 48) = 0;
          *(WORD *)(v27 + 56) = 0;
        }
        ++v24;
        v26 += 38i64;
        ++v25;
      }
      while ( v24 < v3->mMapEntitiesListNum );
    }
  }
}


