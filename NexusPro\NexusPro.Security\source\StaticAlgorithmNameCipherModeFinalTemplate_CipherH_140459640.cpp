﻿/*
 * Function: ?StaticAlgorithmName@?$CipherModeFinalTemplate_CipherHolder@V?$BlockCipherFinal@$00VDec@Rijndael@CryptoPP@@@CryptoPP@@VCBC_Decryption@2@@CryptoPP@@SA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ
 * Address: 0x140459640
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


std::basic_string<char,std::char_traits<char>,std::allocator<char> > *__fastcall CryptoPP::CipherModeFinalTemplate_CipherHolder<CryptoPP::BlockCipherFinal<1,CryptoPP::Rijndael::Dec>,CryptoPP::CBC_Decryption>::StaticAlgorithmName(std::basic_string<char,std::char_traits<char>,std::allocator<char> > *result)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  std::basic_string<char,std::char_traits<char>,std::allocator<char> > *v3; // rax@4
  __int64 v4; // rax@4
  __int64 v6; // [sp+0h] [bp-C8h]@1
  std::basic_string<char,std::char_traits<char>,std::allocator<char> > resulta; // [sp+20h] [bp-A8h]@4
  char v8; // [sp+50h] [bp-78h]@4
  int v9; // [sp+80h] [bp-48h]@4
  __int64 v10; // [sp+88h] [bp-40h]@4
  const char *v11; // [sp+90h] [bp-38h]@4
  std::basic_string<char,std::char_traits<char>,std::allocator<char> > *v12; // [sp+98h] [bp-30h]@4
  std::basic_string<char,std::char_traits<char>,std::allocator<char> > *v13; // [sp+A0h] [bp-28h]@4
  __int64 v14; // [sp+A8h] [bp-20h]@4
  __int64 v15; // [sp+B0h] [bp-18h]@4
  std::basic_string<char,std::char_traits<char>,std::allocator<char> > *v16; // [sp+D0h] [bp+8h]@1

  v16 = result;
  v1 = &v6;
  for (signed __int64 i = 48; i > 0; --i)
  {
    *(DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v10 = -2i64;
  v9 = 0;
  v11 = CryptoPP::CBC_ModeBase::StaticAlgorithmName();
  v3 = CryptoPP::AlgorithmImpl<CryptoPP::SimpleKeyingInterfaceImpl<CryptoPP::TwoBases<CryptoPP::SimpleKeyedTransformation<CryptoPP::BlockTransformation>,CryptoPP::Rijndael_Info>,CryptoPP::TwoBases<CryptoPP::SimpleKeyedTransformation<CryptoPP::BlockTransformation>,CryptoPP::Rijndael_Info>>,CryptoPP::SimpleKeyingInterfaceImpl<CryptoPP::TwoBases<CryptoPP::SimpleKeyedTransformation<CryptoPP::BlockTransformation>,CryptoPP::Rijndael_Info>,CryptoPP::TwoBases<CryptoPP::SimpleKeyedTransformation<CryptoPP::BlockTransformation>,CryptoPP::Rijndael_Info>>>::StaticAlgorithmName(&resulta);
  v12 = v3;
  v13 = v3;
  ((DWORD)(v4) = std::operator+<char,std::char_traits<char>,std::allocator<char>>(&v8, v3, "/");
  v14 = v4;
  v15 = v4;
  std::operator+<char,std::char_traits<char>,std::allocator<char>>(v16, v4, v11);
  v9 |= 1u;
  std::basic_string<char,std::char_traits<char>,std::allocator<char>>::~basic_string<char,std::char_traits<char>,std::allocator<char>>(&v8);
  std::basic_string<char,std::char_traits<char>,std::allocator<char>>::~basic_string<char,std::char_traits<char>,std::allocator<char>>(&resulta);
  return v16;
}


