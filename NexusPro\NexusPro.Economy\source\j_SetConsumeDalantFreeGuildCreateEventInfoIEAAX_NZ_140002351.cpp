﻿/*
 * Function: j_?SetConsumeDalantFree@GuildCreateEventInfo@@IEAAX_N@Z
 * Address: 0x140002351
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall GuildCreateEventInfo::SetConsumeDalantFree(GuildCreateEventInfo *this, bool bEnable)
{
  // GuildCreateEventInfo::SetConsumeDalantFree(); // MALFORMED CONSTRUCTOR - COMMENTED OUT
}

