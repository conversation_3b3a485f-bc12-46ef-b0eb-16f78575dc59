﻿/*
 * Function: ?dtor_0@?0??MoveCompletionListToRunnables@UMSThreadScheduler@details@Concurrency@@QEAA_NVlocation@3@@Z@4HA_0
 * Address: 0x14060D510
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Required includes for Concurrency namespace
#include <ppl.h>
#include <ppltasks.h>
#include <concurrent_vector.h>



void __fastcallConcurrency::details::UMSThreadScheduler::MoveCompletionListToRunnables'::1'::dtor_0(__int64 a1, __int64 a2)
{
  operator delete(*(void **)(a2 + 152));
}

