﻿/*
 * Function: ?Initialzie@CPcBangFavor@@QEAAHXZ
 * Address: 0x14040BCA0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// External symbol declarations for decompiled code
extern "C" ULONG_PTR _security_cookie;



signed __int64 __fastcall CPcBangFavor::Initialzie(CPcBangFavor *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-78h]@1
  char ReturnedString; // [sp+38h] [bp-40h]@4
  char v6; // [sp+39h] [bp-3Fh]@4
  unsigned __int64 v7; // [sp+60h] [bp-18h]@4
  CPcBangFavor *v8; // [sp+80h] [bp+8h]@1

  v8 = this;
  v1 = &v4;
  for (signed __int64 i = 28; i > 0; --i)
  {
    *(DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v7 = (unsigned __int64)&v4 ^ _security_cookie;
  ReturnedString = 0;
  memset(&v6, 0, 9ui64);
  GetPrivateProfileStringA("PcBang Favor", "USE", "FALSE", &ReturnedString, 0xAu, ".\\Initialize\\WorldSystem.ini");
  v8->m_bEnable = strcmp_0(&ReturnedString, "FALSE") != 0;
  return 1i64;
}


