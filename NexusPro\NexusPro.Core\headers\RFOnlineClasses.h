#pragma once
#ifndef RF_ONLINE_CLASSES_H
#define RF_ONLINE_CLASSES_H

#include "NexusProCommon.h"

/*
 * RF Online Basic Class Definitions
 * This file contains basic class definitions for RF Online decompiled source code
 * These are minimal definitions to allow compilation - full implementations will be added later
 */

namespace NexusPro {

// Forward declarations
class CAsyncLogInfo;
class CUserDB;
class CMainThread;
class CNetwork;
class CQuestMgr;
class CGuild;
class CMoneySupplyMgr;
class CCheckSumGuildData;

// Economy system structures
struct _economy_calc_data;

// Constants used in decompiled code
#define FLOAT_1_0 1.0f

// External function declarations
extern double sqrt_0(double x);
extern float ffloor(float x);
extern void _CalcPayExgRatePerRace(int race);
extern int S(int value);

// Basic CAsyncLogInfo class definition
class CAsyncLogInfo {
public:
    CAsyncLogInfo();
    virtual ~CAsyncLogInfo();
    
    // Basic member variables (based on decompiled code patterns)
    DWORD m_dwLogType;
    DWORD m_dwLogID;
    std::string m_strLogMessage;
    SYSTEMTIME m_LogTime;
    
    // Basic member functions
    virtual void Initialize();
    virtual void Cleanup();
    virtual DWORD GetLogType() const { return m_dwLogType; }
    virtual void SetLogType(DWORD dwType) { m_dwLogType = dwType; }
    virtual const char* GetLogMessage() const { return m_strLogMessage.c_str(); }
    virtual void SetLogMessage(const char* pszMessage) { m_strLogMessage = pszMessage ? pszMessage : ""; }
};

// Basic CUserDB class definition
class CUserDB {
public:
    CUserDB();
    virtual ~CUserDB();
    
    // Basic member variables
    DWORD m_dwUserID;
    std::string m_strUserName;
    DWORD m_dwConnectionID;
    
    // Basic member functions
    virtual BOOL Initialize();
    virtual void Cleanup();
    virtual DWORD GetUserID() const { return m_dwUserID; }
    virtual void SetUserID(DWORD dwID) { m_dwUserID = dwID; }
    virtual const char* GetUserName() const { return m_strUserName.c_str(); }
    virtual void SetUserName(const char* pszName) { m_strUserName = pszName ? pszName : ""; }
};

// Basic CMainThread class definition
class CMainThread {
public:
    CMainThread();
    virtual ~CMainThread();
    
    // Basic member variables
    HANDLE m_hThread;
    DWORD m_dwThreadID;
    BOOL m_bRunning;
    
    // Basic member functions
    virtual BOOL Start();
    virtual void Stop();
    virtual BOOL IsRunning() const { return m_bRunning; }
    virtual DWORD GetThreadID() const { return m_dwThreadID; }
    
protected:
    virtual DWORD ThreadProc();
    static DWORD WINAPI StaticThreadProc(LPVOID lpParam);
};

// Basic CNetwork class definition
class CNetwork {
public:
    CNetwork();
    virtual ~CNetwork();
    
    // Basic member variables
    SOCKET m_Socket;
    DWORD m_dwPort;
    std::string m_strIPAddress;
    BOOL m_bConnected;
    
    // Basic member functions
    virtual BOOL Initialize();
    virtual void Cleanup();
    virtual BOOL Connect(const char* pszIP, DWORD dwPort);
    virtual void Disconnect();
    virtual BOOL IsConnected() const { return m_bConnected; }
    virtual int SendData(const void* pData, int nSize);
    virtual int ReceiveData(void* pBuffer, int nBufferSize);
};

// Basic CGameObject class definition
class CGameObject {
public:
    CGameObject();
    virtual ~CGameObject();
    
    // Basic member variables
    DWORD m_dwObjectID;
    DWORD m_dwObjectType;
    FLOAT m_fPosX, m_fPosY, m_fPosZ;
    
    // Basic member functions
    virtual void Initialize();
    virtual void Update(FLOAT fDeltaTime);
    virtual void Render();
    virtual DWORD GetObjectID() const { return m_dwObjectID; }
    virtual void SetObjectID(DWORD dwID) { m_dwObjectID = dwID; }
    virtual void SetPosition(FLOAT x, FLOAT y, FLOAT z) { m_fPosX = x; m_fPosY = y; m_fPosZ = z; }
};

// Basic CPlayer class definition
class CPlayer : public CGameObject {
public:
    CPlayer();
    virtual ~CPlayer();
    
    // Basic member variables
    std::string m_strPlayerName;
    DWORD m_dwLevel;
    DWORD m_dwExperience;
    DWORD m_dwHP, m_dwMaxHP;
    DWORD m_dwMP, m_dwMaxMP;
    
    // Basic member functions
    virtual void Initialize() override;
    virtual void Update(FLOAT fDeltaTime) override;
    virtual const char* GetPlayerName() const { return m_strPlayerName.c_str(); }
    virtual void SetPlayerName(const char* pszName) { m_strPlayerName = pszName ? pszName : ""; }
    virtual DWORD GetLevel() const { return m_dwLevel; }
    virtual void SetLevel(DWORD dwLevel) { m_dwLevel = dwLevel; }
};

// Basic CMonster class definition
class CMonster : public CGameObject {
public:
    CMonster();
    virtual ~CMonster();
    
    // Basic member variables
    DWORD m_dwMonsterType;
    DWORD m_dwHP, m_dwMaxHP;
    DWORD m_dwAggression;
    
    // Basic member functions
    virtual void Initialize() override;
    virtual void Update(FLOAT fDeltaTime) override;
    virtual DWORD GetMonsterType() const { return m_dwMonsterType; }
    virtual void SetMonsterType(DWORD dwType) { m_dwMonsterType = dwType; }
};

// Basic CItem class definition
class CItem {
public:
    CItem();
    virtual ~CItem();
    
    // Basic member variables
    DWORD m_dwItemID;
    DWORD m_dwItemType;
    DWORD m_dwQuantity;
    std::string m_strItemName;
    
    // Basic member functions
    virtual void Initialize();
    virtual DWORD GetItemID() const { return m_dwItemID; }
    virtual void SetItemID(DWORD dwID) { m_dwItemID = dwID; }
    virtual const char* GetItemName() const { return m_strItemName.c_str(); }
    virtual void SetItemName(const char* pszName) { m_strItemName = pszName ? pszName : ""; }
};

// Basic CSkill class definition
class CSkill {
public:
    CSkill();
    virtual ~CSkill();
    
    // Basic member variables
    DWORD m_dwSkillID;
    DWORD m_dwSkillLevel;
    DWORD m_dwCooldown;
    std::string m_strSkillName;
    
    // Basic member functions
    virtual void Initialize();
    virtual DWORD GetSkillID() const { return m_dwSkillID; }
    virtual void SetSkillID(DWORD dwID) { m_dwSkillID = dwID; }
    virtual DWORD GetSkillLevel() const { return m_dwSkillLevel; }
    virtual void SetSkillLevel(DWORD dwLevel) { m_dwSkillLevel = dwLevel; }
};

// Basic CGuild class definition
class CGuild {
public:
    CGuild();
    virtual ~CGuild();
    
    // Basic member variables
    DWORD m_dwGuildID;
    std::string m_strGuildName;
    DWORD m_dwMemberCount;
    
    // Basic member functions
    virtual void Initialize();
    virtual DWORD GetGuildID() const { return m_dwGuildID; }
    virtual void SetGuildID(DWORD dwID) { m_dwGuildID = dwID; }
    virtual const char* GetGuildName() const { return m_strGuildName.c_str(); }
    virtual void SetGuildName(const char* pszName) { m_strGuildName = pszName ? pszName : ""; }
};

// Basic CMap class definition
class CMap {
public:
    CMap();
    virtual ~CMap();
    
    // Basic member variables
    DWORD m_dwMapID;
    std::string m_strMapName;
    DWORD m_dwWidth, m_dwHeight;
    
    // Basic member functions
    virtual void Initialize();
    virtual void LoadMap(const char* pszMapFile);
    virtual DWORD GetMapID() const { return m_dwMapID; }
    virtual void SetMapID(DWORD dwID) { m_dwMapID = dwID; }
};

// Basic CDatabase class definition
class CDatabase {
public:
    CDatabase();
    virtual ~CDatabase();
    
    // Basic member variables
    std::string m_strConnectionString;
    BOOL m_bConnected;
    
    // Basic member functions
    virtual BOOL Initialize();
    virtual void Cleanup();
    virtual BOOL Connect(const char* pszConnectionString);
    virtual void Disconnect();
    virtual BOOL IsConnected() const { return m_bConnected; }
    virtual BOOL ExecuteQuery(const char* pszQuery);
};

} // namespace NexusPro

// RF Online classes (outside namespace for decompiled code compatibility)
class CQuestMgr {
public:
    CQuestMgr();
    virtual ~CQuestMgr();

    // Basic member functions
    virtual BOOL __CheckCond_Dalant(char nRaceCode);
};

class CMoneySupplyMgr {
public:
    CMoneySupplyMgr();
    virtual ~CMoneySupplyMgr();

    // Money supply data structure
    struct MONEY_SUPPLY_DATA {
        DWORD dwAmount[16];
    } m_MS_data;

    // Static instance member
    static CMoneySupplyMgr* pInstance;

    // Member functions
    virtual void UpdateFeeMoneyData(char nRaceCode, int nAmount, DWORD dwFee);
    virtual void UpdateGateRewardMoneyData(char nRaceCode, int nAmount, void* pData);
    virtual void UpdateQuestRewardMoneyData(char nRaceCode, int nAmount, void* pData);
    virtual void UpdateHonorGuildMoneyData(char nRaceCode, char nType, DWORD dwAmount);
    virtual void UpdateSellData(char nRaceCode, int nAmount, void* pData, DWORD dwPrice);
    virtual void UpdateUnitRepairingChargesData(int nLv, unsigned int nAmount);
    virtual void UpdateBuyData(char nRaceCode, int nAmount, void* pData, DWORD dwPrice);
    virtual void UpdateBuyUnitData(int nAmount, DWORD dwPrice);
    virtual void LoopMoneySupply();

    // Static instance
    static CMoneySupplyMgr* Instance();
};

class CGuild {
public:
    CGuild();
    virtual ~CGuild();

    // Guild money management
    virtual unsigned char ManagePopGuildMoney(DWORD dwGuildID, DWORD dwAmount, DWORD dwType);
    virtual void SetGuild(DWORD dwGuildID, unsigned char nType, char* pData, DWORD dwParam1, DWORD dwParam2, DWORD dwParam3, void* pMemberData);
    virtual void PushHistory_IOMoney(bool bType, char* pData, DWORD dwParam1, char nParam2, char nParam3, char nParam4, char nParam5, void* pExtra);
    virtual void IOMoney(char* pData, DWORD dwParam1, char nParam2, char nParam3, char nParam4, char nParam5, void* pExtra, bool bParam);
    virtual short GetTotalDalant();
};

class CMgrGuildHistory {
public:
    CMgrGuildHistory();
    virtual ~CMgrGuildHistory();

    virtual void pop_money(char* pData, DWORD dwParam1, char nParam2, char nParam3, char nParam4, char nParam5);
    virtual void push_money(char* pData, DWORD dwParam1, char nParam2, char nParam3, char nParam4, char nParam5);
};

class CHonorGuild {
public:
    CHonorGuild();
    virtual ~CHonorGuild();

    virtual void SetGuildMaintainMoney(unsigned char nType, DWORD dwParam1, DWORD dwParam2);
};

class CGuildRanking {
public:
    CGuildRanking();
    virtual ~CGuildRanking();

    virtual bool LoadGuildMoneyIOInfo(DWORD dwParam, void* pIOData);
};

class GuildCreateEventInfo {
public:
    GuildCreateEventInfo();
    virtual ~GuildCreateEventInfo();

    virtual void SetConsumeDalantFree(bool bFree);
    virtual DWORD GetEstConsumeDalant();
    virtual DWORD GetEmblemDalant();
};

class CNetworkEX {
public:
    CNetworkEX();
    virtual ~CNetworkEX();

    virtual void GuildPushMoneyRequest(int nParam, char* pData);
    virtual void ExchangeDalantForGoldRequest(int nParam, char* pData);
    virtual void ExchangeGoldForDalantRequest(int nParam, char* pData);
    virtual void TrunkCreateCostIsFreeRequest(int nParam, char* pData);
    virtual void TrunkIoMoneyRequest(int nParam, char* pData);
};

class CMainThread {
public:
    CMainThread();
    virtual ~CMainThread();

    virtual unsigned char check_min_max_guild_money(DWORD dwParam, short* pParam1, short* pParam2);
};

// Economy system structures
struct _economy_calc_data {
    float fValue1;
    float fValue2;
    float fValue3;
    double dValue1;
    double dValue2;
    double dValue3;
};

struct _economy_history_data {
    DWORD dwSize;
    char data[1024];

    // Constructor for decompiled code compatibility
    _economy_history_data();
    virtual ~_economy_history_data();
};

struct _guild_money_io_download_zocl {
    DWORD dwSize;
    char data[256];
};

struct _log_sheet_economy {
    DWORD dwSize;
    char data[512];
};

struct _MONEY_SUPPLY_DATA {
    DWORD dwAmount[16];
};

// Global economy system instance
extern struct {
    double m_dCurTradeDalant[4];
    double m_dBufTradeDalant[4];
    struct {
        double dOldTradeDalant;
    } m_CurRate[4];
} e_EconomySystem;

// Additional external types for decompiled code
struct strFILE;  // Forward declaration instead of typedef
class CDarkHoleDungeonQuestSetup;

#endif // RF_ONLINE_CLASSES_H
