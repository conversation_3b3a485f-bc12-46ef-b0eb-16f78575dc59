﻿/*
 * Function: ?InitCore@@YAXXZ
 * Address: 0x1404ED0D0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// External symbol declarations for decompiled code
extern "C" ULONG_PTR _security_cookie;



void InitCore(void)
{
  int v0; // ebx@1
  void *v1; // rax@1
  DWORD *v2; // rdi@1
  signed __int64 i; // rcx@1
  int v4; // eax@4
  int v5; // eax@4
  int v6; // eax@4
  unsigned int v7; // edx@4
  signed int v8; // edi@4
  signed int v9; // ecx@4
  int v10; // eax@8
  int v11; // eax@10
  unsigned int v12; // ecx@10
  signed int v13; // eax@12
  int v14; // eax@21
  unsigned int v15; // ecx@21
  int v16; // eax@32
  unsigned int v17; // ecx@32
  signed __int64 v18; // rax@34
  float v19; // xmm0_4@36
  float v20; // xmm0_4@36
  float v21; // xmm0_4@36
  __int64 v22; // [sp+0h] [bp-178h]@1
  DWORD nSize; // [sp+20h] [bp-158h]@6
  LPCSTR lpFileName; // [sp+28h] [bp-150h]@6
  float v25; // [sp+30h] [bp-148h]@32
  float v26; // [sp+34h] [bp-144h]@32
  float v27; // [sp+38h] [bp-140h]@32
  int v28; // [sp+3Ch] [bp-13Ch]@32
  char ReturnedString; // [sp+40h] [bp-138h]@4
  unsigned __int64 v30; // [sp+140h] [bp-38h]@1

  v30 = (unsigned __int64)&v22 ^ _security_cookie;
  memset_0(stState, 0, 0xC38ui64);
  v0 = 0;
  stIsInit = 1;
  dword_184A7981C = 0;
  dword_184A79808 = 0;
  dword_184A79828 = 0;
  dword_184A79788 = ((DWORD)(FLOAT_1_3333334);
  dword_184A7983C = 0;
  dword_184A79840 = 0;
  dword_184A79810 = 0;
  dword_184A79778 = ((DWORD)(FLOAT_6000_0);
  dword_184A79774 = ((DWORD)(FLOAT_2_0);
  dword_184A79800 = 0x40000;
  dword_184A7977C = ((DWORD)(FLOAT_N0_5);
  dword_184A79784 = ((DWORD)(FLOAT_0_60000002);
  dword_184A79780 = ((DWORD)(FLOAT_0_60000002);
  *(QWORD *)&stru_184A79A6C.m[3][1] = 0;
  *(QWORD *)&stru_184A79A6C.m[2][3] = 0;
  *(QWORD *)stru_184A79A6C.m[2] = 0;
  *(QWORD *)&stru_184A79A6C.m[1][2] = 0;
  *(QWORD *)&stru_184A79A6C.m[0][3] = 0;
  *(QWORD *)&stru_184A79A6C.m[0][1] = 0;
  stru_184A79A6C._44 = FLOAT_1_0;
  stru_184A79A6C._33 = FLOAT_1_0;
  stru_184A79A6C._22 = FLOAT_1_0;
  stru_184A79A6C._11 = FLOAT_1_0;
  dword_184A79A64 = 0;
  dword_184A79A60 = 0;
  dword_184A79A5C = 0;
  dword_184A79A58 = 0;
  dword_184A79A50 = 0;
  dword_184A79A4C = 0;
  dword_184A79A48 = 0;
  dword_184A79A44 = 0;
  dword_184A79A3C = 0;
  dword_184A79A38 = 0;
  dword_184A79A34 = 0;
  dword_184A79A30 = 0;
  dword_184A79A68 = ((DWORD)(FLOAT_1_0);
  dword_184A79A54 = ((DWORD)(FLOAT_1_0);
  dword_184A79A40 = ((DWORD)(FLOAT_1_0);
  dword_184A79A2C = ((DWORD)(FLOAT_1_0);
  dword_184A79B64 = 0;
  dword_184A79B60 = 0;
  dword_184A79B5C = 0;
  dword_184A79B58 = 0;
  dword_184A79B50 = 0;
  dword_184A79B4C = 0;
  dword_184A79B48 = 0;
  dword_184A79B44 = 0;
  dword_184A79B3C = 0;
  dword_184A79B38 = 0;
  dword_184A79B34 = 0;
  dword_184A79B30 = 0;
  dword_184A79B68 = ((DWORD)(FLOAT_1_0);
  dword_184A79B54 = ((DWORD)(FLOAT_1_0);
  dword_184A79B40 = ((DWORD)(FLOAT_1_0);
  dword_184A79B2C[0] = FLOAT_1_0;
  dword_184A79814 = 0;
  dword_184A79818 = 0;
  dword_184A7982C = 0;
  dword_184A79834 = 0;
  dword_184A79830 = 0;
  dword_184A79838 = 0;
  dword_184A797C4 = ((DWORD)(FLOAT_1_0);
  dword_184A797B8 = 0;
  dword_184A797BC = 0;
  dword_184A79920 = 1;
  qword_184A79C20 = 0;
  dword_184A79794 = ((DWORD)(FLOAT_30_0);
  v1 = Dmalloc(0x40000);
  dword_184A798D0 = -1;
  v2 = &unk_184A798D4;
  stTemp = v1;
  for ( i = 16; i > 0; --i )
  {
    *v2 = 1065353216;
    ++v2;
  }
  GetPrivateProfileStringA("Path", "MapPath", ".\\Map\\", stState, 0x80u, ".\\R3Engine.ini");
  GetPrivateProfileStringA("Path", "SystemPath", ".\\System\\", ::ReturnedString, 0x80u, ".\\R3Engine.ini");
  GetPrivateProfileStringA("Path", "EntityPath", ".\\Map\\Entity\\", byte_184A790F0, 0x80u, ".\\R3Engine.ini");
  GetPrivateProfileStringA("Path", "EffectPath", ".\\Effect\\", byte_184A79170, 0x80u, ".\\R3Engine.ini");
  GetPrivateProfileStringA("Path", "ScreenShotsPath", ".\\ScreenShots\\", byte_184A791F0, 0x80u, ".\\R3Engine.ini");
  GetPrivateProfileStringA("Path", "SoundPath", ".\\Snd\\", byte_184A79270, 0x80u, ".\\R3Engine.ini");
  GetPrivateProfileStringA("RenderState", "ScreenXSize", "1024", &ReturnedString, 0x80u, ".\\R3Engine.ini");
  v4 = atoi(&ReturnedString);
  dword_184A79BA8 = 0;
  dword_184A79BB0 = (signed int)ffloor((float)v4);
  *(float *)&dword_184A7978C = (float)v4;
  GetPrivateProfileStringA("RenderState", "ScreenYSize", "768", &ReturnedString, 0x80u, ".\\R3Engine.ini");
  v5 = atoi(&ReturnedString);
  *(float *)&dword_184A79790 = (float)v5;
  dword_184A79BB4 = (signed int)ffloor((float)v5);
  dword_184A79BAC = 0;
  GetPrivateProfileStringA("RenderState", "RenderBits", "32", &ReturnedString, 0x80u, ".\\R3Engine.ini");
  dword_184A79798 = atoi(&ReturnedString);
  GetPrivateProfileStringA("RenderState", "ZbufferBits", "24", &ReturnedString, 0x80u, ".\\R3Engine.ini");
  dword_184A7979C = atoi(&ReturnedString);
  GetPrivateProfileStringA("RenderState", "bFullScreen", "FALSE", &ReturnedString, 0x80u, ".\\R3Engine.ini");
  _strupr(&ReturnedString);
  dword_184A797A0 = memcmp(&ReturnedString, "TRUE", 5ui64) == 0;
  GetPrivateProfileStringA("RenderState", "BBoShasi", "2", &ReturnedString, 0x80u, ".\\R3Engine.ini");
  dword_184A797F8 = atoi(&ReturnedString);
  GetPrivateProfileStringA("RenderState", "TextureDetail", "2", &ReturnedString, 0x80u, ".\\R3Engine.ini");
  v6 = atoi(&ReturnedString);
  v7 = 0;
  v8 = 3;
  v9 = 3;
  if ( v6 )
    v7 = v6;
  lpFileName = ".\\R3Engine.ini";
  nSize = 128;
  if ( v7 < 3 )
    v9 = v7;
  dword_184A797C8 = 3 - v9;
  v10 = 3 - v9 - 1;
  if ( v10 < 0 )
    v10 = 0;
  dword_184A797D0 = v10;
  GetPrivateProfileStringA("RenderState", "DynamicLight", "1", &ReturnedString, nSize, lpFileName);
  v11 = atoi(&ReturnedString);
  v12 = 0;
  if ( v11 )
    v12 = v11;
  v13 = 3;
  if ( v12 < 3 )
    v13 = v12;
  if ( v13 == 1 )
  {
    dword_184A797D8 = 3;
  }
  else if ( v13 == 2 )
  {
    dword_184A797D8 = 6;
  }
  else
  {
    if ( v13 == 3 )
      v13 = 10;
    dword_184A797D8 = v13;
  }
  GetPrivateProfileStringA("RenderState", "ShadowDetail", "1", &ReturnedString, 0x80u, ".\\R3Engine.ini");
  v14 = atoi(&ReturnedString);
  v15 = 0;
  if ( v14 )
    v15 = v14;
  if ( v15 < 3 )
    v8 = v15;
  if ( v8 == 1 )
  {
    dword_184A797D4 = 1;
  }
  else if ( v8 == 2 )
  {
    dword_184A797D4 = 10;
  }
  else
  {
    if ( v8 == 3 )
      v8 = -1;
    dword_184A797D4 = v8;
  }
  GetPrivateProfileStringA("RenderState", "bDetailTexture", "TRUE", &ReturnedString, 0x80u, ".\\R3Engine.ini");
  dword_184A797CC = memcmp(&ReturnedString, "TRUE", 5ui64) == 0;
  GetPrivateProfileStringA("RenderState", "bMouseAccelation", "TRUE", &ReturnedString, 0x80u, ".\\R3Engine.ini");
  _strupr(&ReturnedString);
  v25 = FLOAT_0_60000002;
  v27 = FLOAT_1_0;
  v26 = FLOAT_0_80000001;
  dword_184A797F0 = memcmp(&ReturnedString, "TRUE", 5ui64) == 0;
  v28 = 0;
  GetPrivateProfileStringA("RenderState", "SeeDistance", "2", &ReturnedString, 0x80u, ".\\R3Engine.ini");
  v16 = atoi(&ReturnedString);
  v17 = 0;
  if ( v16 )
    v17 = v16;
  v18 = 2;
  lpFileName = ".\\R3Engine.ini";
  nSize = 128;
  if ( v17 < 2 )
    v18 = v17;
  dword_184A797DC = *((DWORD *)&v25 + v18);
  GetPrivateProfileStringA("Sound", "Sound", "TRUE", &ReturnedString, nSize, lpFileName);
  _strupr(&ReturnedString);
  dword_184A797E0 = memcmp(&ReturnedString, "TRUE", 5ui64) == 0;
  GetPrivateProfileStringA("Sound", "Music", "TRUE", &ReturnedString, 0x80u, ".\\R3Engine.ini");
  _strupr(&ReturnedString);
  dword_184A797E4 = memcmp(&ReturnedString, "TRUE", 5ui64) == 0;
  GetPrivateProfileStringA("Sound", "MusicVol", "1.0f", &ReturnedString, 0x80u, ".\\R3Engine.ini");
  v19 = atof(&ReturnedString);
  dword_184A797EC = ((DWORD)(v19);
  SetGlobalMusicVolume(v19);
  GetPrivateProfileStringA("Sound", "SoundVol", "1.0f", &ReturnedString, 0x80u, ".\\R3Engine.ini");
  v20 = atof(&ReturnedString);
  dword_184A797E8 = ((DWORD)(v20);
  SetGlobalWavVolume(v20);
  GetPrivateProfileStringA("RenderState", "Gamma", "1.0f", &ReturnedString, 0x80u, ".\\R3Engine.ini");
  v21 = atof(&ReturnedString);
  dword_184A797C4 = ((DWORD)(v21);
  SetGamma(v21, 0);
  GetPrivateProfileStringA("RenderState", "bStartCamera", "FALSE", &ReturnedString, 0x80u, ".\\R3Engine.ini");
  _strupr(&ReturnedString);
  LOBYTE(v0) = memcmp(&ReturnedString, "TRUE", 5ui64) == 0;
  dword_184A79770 = v0;
  GetPrivateProfileStringA("SampleCharacter", "CharPath", ".\\Character\\", byte_184A792F0, 0x80u, ".\\R3Engine.ini");
  GetPrivateProfileStringA(
    "SampleCharacter",
    "CharTexturePath",
    ".\\Character\\tex\\",
    byte_184A79370,
    0x80u,
    ".\\R3Engine.ini");
  GetPrivateProfileStringA("SampleCharacter", "MeshName", "SNWG_001.msh", byte_184A793F0, 0x80u, ".\\R3Engine.ini");
  GetPrivateProfileStringA("SampleCharacter", "BoneName", "bone_b.bn", byte_184A79470, 0x80u, ".\\R3Engine.ini");
  GetPrivateProfileStringA("SampleCharacter", "StopAni", "Stop.ani", byte_184A794F0, 0x80u, ".\\R3Engine.ini");
  GetPrivateProfileStringA("SampleCharacter", "RunAni", "Run_Loop.ani", byte_184A79570, 0x80u, ".\\R3Engine.ini");
  GetPrivateProfileStringA("SampleCharacter", "SwordAni", byte_140883769, byte_184A795F0, 0x80u, ".\\R3Engine.ini");
  GetPrivateProfileStringA("SampleCharacter", "SwordMesh", byte_140883769, byte_184A79670, 0x80u, ".\\R3Engine.ini");
  GetPrivateProfileStringA(
    "SampleCharacter",
    "SwordTexturePath",
    byte_140883769,
    byte_184A796F0,
    0x80u,
    ".\\R3Engine.ini");
  v25 = FLOAT_1_0;
  v26 = FLOAT_1_0;
  v27 = FLOAT_1_0;
  SetMainLight(&v25);
}


