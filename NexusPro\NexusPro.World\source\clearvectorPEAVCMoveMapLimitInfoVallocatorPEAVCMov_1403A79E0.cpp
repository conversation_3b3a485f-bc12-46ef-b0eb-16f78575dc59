﻿/*
 * Function: ?clear@?$vector@PEAVCMoveMapLimitInfo@@V?$allocator@PEAVCMoveMapLimitInfo@@@std@@@std@@QEAAXXZ
 * Address: 0x1403A79E0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall std::vector<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>::clear(std::vector<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *> > *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-A8h]@1
  char v4; // [sp+20h] [bp-88h]@4
  std::_Vector_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *> > *result; // [sp+38h] [bp-70h]@4
  char v6; // [sp+40h] [bp-68h]@4
  std::_Vector_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *> > *v7; // [sp+58h] [bp-50h]@4
  std::_Vector_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *> > v8; // [sp+60h] [bp-48h]@4
  __int64 v9; // [sp+78h] [bp-30h]@4
  std::_Vector_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *> > *v10; // [sp+80h] [bp-28h]@4
  std::_Vector_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *> > *v11; // [sp+88h] [bp-20h]@4
  std::_Vector_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *> > *v12; // [sp+90h] [bp-18h]@4
  std::vector<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *> > *v13; // [sp+B0h] [bp+8h]@1

  v13 = this;
  v1 = &v3;
  for (signed __int64 i = 40; i > 0; --i)
  {
    *(DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v9 = -2i64;
  result = (std::_Vector_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *> > *)&v4;
  v7 = (std::_Vector_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *> > *)&v6;
  v10 = std::vector<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>::end(
          v13,
          (std::_Vector_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *> > *)&v4);
  v11 = v10;
  v12 = std::vector<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>::begin(v13, v7);
  std::vector<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>::erase(v13, &v8, v12, v11);
  std::_Vector_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>::~_Vector_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>(&v8);
}


