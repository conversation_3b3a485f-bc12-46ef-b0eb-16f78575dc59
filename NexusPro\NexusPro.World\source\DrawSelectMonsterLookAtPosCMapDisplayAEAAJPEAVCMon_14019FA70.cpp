﻿/*
 * Function: ?DrawSelectMonsterLookAtPos@CMapDisplay@@AEAAJPEAVCMonster@@@Z
 * Address: 0x14019FA70
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


__int64 __fastcall CMapDisplay::DrawSelectMonsterLookAtPos(CMapDisplay *this, CMonster *pMon)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 result; // rax@6
  IDirectDrawSurface7 *v5; // rax@16
  __int64 v6; // [sp+0h] [bp-E8h]@1
  __int64 v7; // [sp+20h] [bp-C8h]@16
  int v8; // [sp+28h] [bp-C0h]@16
  unsigned int v9; // [sp+30h] [bp-B8h]@4
  float v10; // [sp+48h] [bp-A0h]@11
  float v11; // [sp+4Ch] [bp-9Ch]@11
  float v12; // [sp+78h] [bp-70h]@11
  float v13; // [sp+7Ch] [bp-6Ch]@11
  float v14; // [sp+80h] [bp-68h]@11
  _bsp_info *v15; // [sp+98h] [bp-50h]@11
  int v16; // [sp+A0h] [bp-48h]@11
  int v17; // [sp+A4h] [bp-44h]@11
  float v18; // [sp+B8h] [bp-30h]@11
  float v19; // [sp+BCh] [bp-2Ch]@11
  IDirectDrawSurface7 *v20; // [sp+C8h] [bp-20h]@16
  IUnknownVtbl *v21; // [sp+D0h] [bp-18h]@16
  CMapDisplay *v22; // [sp+F0h] [bp+8h]@1
  CMonster *v23; // [sp+F8h] [bp+10h]@1

  v23 = pMon;
  v22 = this;
  v2 = &v6;
  for (signed __int64 i = 56; i > 0; --i)
  {
    *(DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v9 = 0;
  if ( v22->m_pSFCircle && pMon )
  {
    if ( CGameObject::GetCurSecNum((CGameObject *)&pMon->vfptr) == -1 )
    {
      result = v9;
    }
    else if ( v23->m_pCurMap )
    {
      v15 = CMapData::GetBspInfo(v23->m_pCurMap);
      v12 = (float)-v15->m_nMapMinSize[0] + v23->m_fLookAtPos[0];
      v13 = (float)v15->m_nMapMaxSize[1] - v23->m_fLookAtPos[1];
      v14 = (float)v15->m_nMapMaxSize[2] - v23->m_fLookAtPos[2];
      v16 = v22->m_MapExtend.m_rcExtend.right;
      v17 = v22->m_MapExtend.m_rcExtend.bottom;
      v18 = v12 - (float)v22->m_MapExtend.m_rcExtend.left;
      v19 = v14 - (float)v22->m_MapExtend.m_rcExtend.top;
      v10 = (float)(signed int)ffloor((float)(v18 * (float)v22->m_rcWnd.right) / (float)(v22->m_MapExtend.m_rcExtend.right
                                                                                       - v22->m_MapExtend.m_rcExtend.left));
      v11 = (float)(signed int)ffloor((float)(v19 * (float)v22->m_rcWnd.bottom) / (float)(v22->m_MapExtend.m_rcExtend.bottom
                                                                                        - v22->m_MapExtend.m_rcExtend.top));
      if ( v22->m_MapExtend.m_bExtendMode
        && v12 > (float)v22->m_MapExtend.m_ptStartMap.x
        && (float)v22->m_MapExtend.m_ptEndMap.x > v12
        && v14 > (float)v22->m_MapExtend.m_ptStartMap.y
        && (float)v22->m_MapExtend.m_ptEndMap.y > v14 )
      {
        v20 = CSurface::GetDDrawSurface(v22->m_pSFMap);
        v5 = CSurface::GetDDrawSurface(v22->m_pSFCircle);
        v21 = v20->vfptr;
        v8 = 1;
        v7 = 0;
        v9 = ((int (__fastcall *)(IDirectDrawSurface7 *, QWORD, QWORD, IDirectDrawSurface7 *))v21[2].AddRef)(
               v20,
               (unsigned int)(signed int)ffloor(v10),
               (unsigned int)(signed int)ffloor(v11),
               v5);
      }
      result = v9;
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}


