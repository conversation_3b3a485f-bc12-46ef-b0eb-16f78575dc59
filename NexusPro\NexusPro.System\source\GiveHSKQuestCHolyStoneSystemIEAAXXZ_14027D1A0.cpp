﻿/*
 * Function: ?GiveHSKQuest@CHolyStoneSystem@@IEAAXXZ
 * Address: 0x14027D1A0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CHolyStoneSystem::GiveHSKQuest(CHolyStoneSystem *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  int v3; // eax@22
  __int64 v4; // [sp+0h] [bp-98h]@1
  int j; // [sp+40h] [bp-58h]@4
  CPlayer *v6; // [sp+48h] [bp-50h]@7
  int v7; // [sp+50h] [bp-48h]@9
  CPlayer *v8; // [sp+58h] [bp-40h]@12
  CPartyPlayer **v9; // [sp+60h] [bp-38h]@17
  int k; // [sp+68h] [bp-30h]@18
  CPlayer *v11; // [sp+70h] [bp-28h]@22
  char v12; // [sp+78h] [bp-20h]@16
  int v13; // [sp+7Ch] [bp-1Ch]@22
  char v14; // [sp+80h] [bp-18h]@24
  CHolyStoneSystem *v15; // [sp+A0h] [bp+8h]@1

  v15 = this;
  v1 = &v4;
  for (signed __int64 i = 36; i > 0; --i)
  {
    *(DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  CHolyStoneSystem::InitQuestCash(v15);
  for ( j = 0; j < 2532; ++j )
  {
    v6 = &g_Player + j;
    if ( v6->m_bLive )
      CPlayer::RecvHSKQuest(v6, 100, 3, 0, 0, 0, -1);
  }
  v7 = rand();
  for ( j = 0; j < 2532; ++j )
  {
    ++v7;
    v8 = &g_Player + j;
    if ( v8->m_bLive && v8->m_byHSKQuestCode == 100 )
    {
      if ( ((int (__fastcall *)(CPlayer *))v8->vfptr->GetLevel)(v8) < 25 )
      {
        CPlayer::RecvHSKQuest(v8, 100, 3, 0, 0, 0, -1);
      }
      else if ( CPartyPlayer::IsPartyMode(v8->m_pPartyMgr) )
      {
        v9 = CPartyPlayer::GetPtrPartyMember(v8->m_pPartyMgr);
        if ( v9 )
        {
          for ( k = 0; k < 8 && v9[k]; ++k )
          {
            v11 = &g_Player + v9[k]->m_wZoneIndex;
            v13 = CPlayerDB::GetRaceCode(&v11->m_Param);
            v3 = CPlayerDB::GetRaceCode(&v8->m_Param);
            if ( v13 == v3 && v11->m_byHSKQuestCode == 100 )
            {
              v14 = CHolyStoneSystem::GetNumOfTime(v15);
              CPlayer::RecvHSKQuest(v11, v7 % 2, 0, 0, 0, 0, v14);
            }
          }
        }
      }
      else
      {
        v12 = CHolyStoneSystem::GetNumOfTime(v15);
        CPlayer::RecvHSKQuest(v8, v7 % 2, 0, 0, 0, 0, v12);
      }
    }
  }
}


