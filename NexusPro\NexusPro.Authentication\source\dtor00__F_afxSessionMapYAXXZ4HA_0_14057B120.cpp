﻿/*
 * Function: ?dtor$0@?0???__F_afxSessionMap@@YAXXZ@4HA_0
 * Address: 0x14057B120
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Fixed malformed function signature with special characters
void dtor_afxSessionMap_destructor()
{
  // Removed malformed CryptoPP destructor call - replaced with stub
  // Original: CryptoPP::EcRecommendedParameters<CryptoPP::ECP>::~EcRecommendedParameters<CryptoPP::ECP>(&unk_184A89708);
}

