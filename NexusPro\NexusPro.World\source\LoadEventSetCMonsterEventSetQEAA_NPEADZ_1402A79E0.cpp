﻿/*
 * Function: ?LoadEventSet@CMonsterEventSet@@QEAA_NPEAD@Z
 * Address: 0x1402A79E0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// External symbol declarations for decompiled code
extern "C" ULONG_PTR _security_cookie;



char __fastcall CMonsterEventSet::LoadEventSet(CMonsterEventSet *this, char *pwszErrCode)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  char *v5; // rax@19
  int v6; // eax@23
  int v7; // eax@23
  float v8; // xmm0_4@23
  signed int v9; // ecx@24
  signed int v10; // edx@24
  unsigned __int16 v11; // ax@28
  __int32 v12; // eax@31
  char v13; // al@33
  bool v14; // zf@36
  __int64 v15; // [sp+0h] [bp-838h]@1
  int v16; // [sp+20h] [bp-818h]@24
  int v17; // [sp+28h] [bp-810h]@24
  FILE *File; // [sp+38h] [bp-800h]@6
  char Buf; // [sp+60h] [bp-7D8h]@8
  _FILETIME ftWrite; // [sp+478h] [bp-3C0h]@4
  int v21; // [sp+484h] [bp-3B4h]@13
  char Dst; // [sp+4A0h] [bp-398h]@7
  char v23; // [sp+4E0h] [bp-358h]@7
  char v24; // [sp+520h] [bp-318h]@7
  char v25; // [sp+560h] [bp-2D8h]@7
  char v26; // [sp+5A0h] [bp-298h]@7
  char v27; // [sp+5E0h] [bp-258h]@7
  char v28; // [sp+620h] [bp-218h]@7
  char v29; // [sp+660h] [bp-1D8h]@7
  char v30; // [sp+6A0h] [bp-198h]@7
  char v31; // [sp+6E0h] [bp-158h]@7
  char *ppszDst; // [sp+740h] [bp-F8h]@7
  char *Source; // [sp+748h] [bp-F0h]@7
  char *Str; // [sp+750h] [bp-E8h]@7
  char *v35; // [sp+758h] [bp-E0h]@7
  char *v36; // [sp+760h] [bp-D8h]@7
  char *szRecordCode; // [sp+768h] [bp-D0h]@7
  char *v38; // [sp+770h] [bp-C8h]@7
  char *v39; // [sp+778h] [bp-C0h]@7
  char *v40; // [sp+780h] [bp-B8h]@7
  char *v41; // [sp+788h] [bp-B0h]@7
  int v42; // [sp+794h] [bp-A4h]@7
  char *Str1; // [sp+798h] [bp-A0h]@15
  _event_set::_monster_set *v44; // [sp+7A0h] [bp-98h]@19
  char String; // [sp+7C0h] [bp-78h]@19
  CMapData *v46; // [sp+808h] [bp-30h]@21
  _base_fld *v47; // [sp+810h] [bp-28h]@26
  unsigned __int64 v48; // [sp+820h] [bp-18h]@4
  CMonsterEventSet *v49; // [sp+840h] [bp+8h]@1
  char *Dest; // [sp+848h] [bp+10h]@1

  Dest = pwszErrCode;
  v49 = this;
  v2 = &v15;
  for (signed __int64 i = 524; i > 0; --i)
  {
    *(DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v48 = (unsigned __int64)&v15 ^ _security_cookie;
  if ( GetLastWriteFileTime(".\\Initialize\\EventSet.ini", &ftWrite) )
  {
    v49->m_ftWrite = ftWrite;
    if ( fopen_s(&File, ".\\Initialize\\EventSet.ini", "r+t") )
    {
      result = 0;
    }
    else
    {
      ppszDst = &Dst;
      Source = &v23;
      Str = &v24;
      v35 = &v25;
      v36 = &v26;
      szRecordCode = &v27;
      v38 = &v28;
      v39 = &v29;
      v40 = &v30;
      v41 = &v31;
      v42 = 0;
      while ( fgets(&Buf, 1024, File) )
      {
        if ( Buf != 59 && Buf != 10 && Buf != 13 )
        {
          memset_0(&Dst, 0, 0x280ui64);
          v21 = ParsingCommandA(&Buf, 10, &ppszDst, 64);
          if ( v21 != 1 )
          {
            if ( v21 != 10 )
            {
              sprintf(Dest, "Event Set Load Error >> event parameter count error : %d", (unsigned int)v21);
              CLogFile::Write(&stru_1799C8F30, Dest);
              fclose(File);
              return 0;
            }
            Str1 = (char *)CMonsterEventSet::GetEmptyEventSet(v49);
            if ( !Str1 )
            {
              sprintf(Dest, "Event Set Load Error >> over max event set error : %d", 10i64);
              CLogFile::Write(&stru_1799C8F30, Dest);
              fclose(File);
              return 0;
            }
            if ( !strcmp_0(Str1, &byte_1407B554F) )
              strcpy_0(Str1, ppszDst);
            v44 = CMonsterEventSet::GetMonsterSet(v49, (_event_set *)Str1);
            strcpy_0(&String, Source);
            v5 = _strupr(&String);
            if ( !strcmp_0(v5, "UNKNOWN") )
            {
              v44->pMap = 0;
              v44->fPos[0] = FLOAT_N1_0;
              v44->fPos[1] = FLOAT_N1_0;
              v44->fPos[2] = FLOAT_N1_0;
              v44->bUnknownMap = 1;
            }
            else
            {
              v46 = CMapOperation::GetMap(&g_MapOper, Source);
              if ( !v46 )
              {
                sprintf(Dest, "Event Set Load Error : %s >> map code error : %s", ppszDst, Source);
                CLogFile::Write(&stru_1799C8F30, Dest);
                fclose(File);
                return 0;
              }
              v44->pMap = v46;
              v6 = atoi(Str);
              v44->fPos[0] = (float)v6;
              v7 = atoi(v35);
              v44->fPos[1] = (float)v7;
              v8 = (float)atoi(v36);
              v44->fPos[2] = v8;
              if ( !CMapData::IsMapIn(v46, v44->fPos) )
              {
                v9 = (signed int)ffloor(v44->fPos[1]);
                v10 = (signed int)ffloor(v44->fPos[0]);
                v17 = (signed int)ffloor(v44->fPos[2]);
                v16 = v9;
                sprintf(Dest, "Event Set Load Error : %s >> xyz range error : %d %d %d", ppszDst, (unsigned int)v10);
                CLogFile::Write(&stru_1799C8F30, Dest);
                fclose(File);
                return 0;
              }
              v44->bUnknownMap = 0;
            }
            v47 = CRecordData::GetRecord(&stru_1799C6210, szRecordCode);
            if ( !v47 )
            {
              sprintf(Dest, "Event Set Load Error : %s >> mon code error : %s", ppszDst, szRecordCode);
              CLogFile::Write(&stru_1799C8F30, Dest);
              fclose(File);
              return 0;
            }
            v44->pMonsterFld = v47;
            v11 = atoi(v38);
            v44->wNum = v11;
            if ( (signed int)v44->wNum <= 0 || (signed int)v44->wNum > 100 )
            {
              sprintf(Dest, "Event Set Load Error : %s >> mon num error : %d", ppszDst, v44->wNum);
              CLogFile::Write(&stru_1799C8F30, Dest);
              fclose(File);
              return 0;
            }
            v12 = atol(v39);
            v44->dwRegenTerm = 1000 * v12;
            if ( !v44->dwRegenTerm )
            {
              sprintf(Dest, "Event Set Load Error : %s >> mon regen term error : %d", ppszDst, v44->dwRegenTerm);
              CLogFile::Write(&stru_1799C8F30, Dest);
              fclose(File);
              return 0;
            }
            v13 = atoi(v40);
            v44->byRegenProb = v13;
            if ( v44->byRegenProb <= 0 || v44->byRegenProb > 100 )
            {
              sprintf(Dest, "Event Set Load Error : %s >> mon regen prob error : %d", ppszDst, v44->byRegenProb);
              CLogFile::Write(&stru_1799C8F30, Dest);
              fclose(File);
              return 0;
            }
            v44->dwDuring = atoi(v41);
            v14 = v44->dwDuring == 0;
            if ( v44->dwDuring > 7 )
            {
              sprintf(Dest, "Event Set Load Error : %s >> event during error : %d", ppszDst, v44->dwDuring);
              CLogFile::Write(&stru_1799C8F30, Dest);
              fclose(File);
              return 0;
            }
            v44->dwDuring *= 86400000;
            v44->bIsSet = 1;
          }
        }
      }
      fclose(File);
      result = 1;
    }
  }
  else
  {
    sprintf(Dest, "Event Set Load Error >> can't find .ini file : %s", ".\\Initialize\\EventSet.ini");
    CLogFile::Write(&stru_1799C8F30, Dest);
    result = 0;
  }
  return result;
}


