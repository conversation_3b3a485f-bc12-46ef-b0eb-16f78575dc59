﻿/*
 * Function: ?Initialzie@AutoMineMachineMng@@QEAA_NXZ
 * Address: 0x1402D5DC0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


char __fastcall AutoMineMachineMng::Initialzie(AutoMineMachineMng *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  CWeeklyGuildRankManager *v4; // rax@16
  __int64 v5; // [sp+0h] [bp-438h]@1
  _DB_LOAD_AUTOMINE_MACHINE Dst; // [sp+30h] [bp-408h]@6
  int j; // [sp+414h] [bp-24h]@6
  int k; // [sp+418h] [bp-20h]@8
  CGuild *pGuild; // [sp+420h] [bp-18h]@16
  AutoMineMachineMng *v10; // [sp+440h] [bp+8h]@1

  v10 = this;
  v1 = &v5;
  for (signed __int64 i = 268; i > 0; --i)
  {
    *(DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( CRFWorldDatabase::create_automine_table(pkDB) )
  {
    _DB_LOAD_AUTOMINE_MACHINE::_DB_LOAD_AUTOMINE_MACHINE(&Dst);
    for ( j = 0; j < 3; ++j )
    {
      for ( k = 0; k < 2; ++k )
      {
        if ( !AutoMineMachine::Initialize((AutoMineMachine *)v10 + 2 * j + k, j, k) )
          return 0;
        memset_0(&Dst, 0, 0x3D1ui64);
        Dst.byRace = j;
        Dst.byCollisionType = k;
        if ( CRFWorldDatabase::select_automine(pkDB, &Dst) == 1 )
          return 0;
        if ( !AutoMineMachine::LoadDatabase((AutoMineMachine *)v10 + 2 * j + k, &Dst) )
          return 0;
        v4 = CWeeklyGuildRankManager::Instance();
        pGuild = CWeeklyGuildRankManager::GetPrevOwnerGuild(v4, j, k);
        if ( !AutoMineMachine::SetOwner((AutoMineMachine *)v10 + 2 * j + k, j, k, pGuild) )
          return 0;
      }
    }
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}


