﻿/*
 * Function: ?MakeMap@CBossMonsterScheduleSystem@@IEAAPEAUBossSchedule_Map@@HPEAVCMapData@@@Z
 * Address: 0x140419160
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// External symbol declarations for decompiled code
extern "C" ULONG_PTR _security_cookie;



BossSchedule_Map *__fastcall CBossMonsterScheduleSystem::MakeMap(CBossMonsterScheduleSystem *this, int nIndex, CMapData *pMap)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  BossSchedule_Map *result; // rax@5
  BossSchedule_Map *v6; // rax@26
  __int64 v7; // [sp+0h] [bp-398h]@1
  char _Dest[260]; // [sp+40h] [bp-358h]@12
  int j; // [sp+154h] [bp-244h]@12
  int v10; // [sp+158h] [bp-240h]@12
  __int64 *v11; // [sp+160h] [bp-238h]@14
  __int64 v12; // [sp+168h] [bp-230h]@14
  int *v13; // [sp+170h] [bp-228h]@14
  int v14; // [sp+178h] [bp-220h]@14
  int k; // [sp+17Ch] [bp-21Ch]@14
  _mon_active *v16; // [sp+180h] [bp-218h]@17
  _mon_active_fld *v17; // [sp+188h] [bp-210h]@18
  _base_fld *v18; // [sp+190h] [bp-208h]@18
  CIniFile rhs; // [sp+1B0h] [bp-1E8h]@25
  BossSchedule_Map *pMapSchedule; // [sp+2F8h] [bp-A0h]@28
  int v21; // [sp+300h] [bp-98h]@28
  _mon_block *pBlock; // [sp+308h] [bp-90h]@30
  _mon_block_fld *v23; // [sp+310h] [bp-88h]@30
  int *v24; // [sp+318h] [bp-80h]@30
  int v25; // [sp+320h] [bp-78h]@30
  int l; // [sp+324h] [bp-74h]@30
  _mon_active *pMonAct; // [sp+328h] [bp-70h]@33
  _mon_active_fld *v28; // [sp+330h] [bp-68h]@34
  _base_fld *v29; // [sp+338h] [bp-60h]@34
  BossSchedule_Map *v30; // [sp+348h] [bp-50h]@28
  BossSchedule_Map *v31; // [sp+350h] [bp-48h]@25
  BossSchedule **v32; // [sp+358h] [bp-40h]@28
  BossSchedule_Map *v33; // [sp+360h] [bp-38h]@39
  __int64 v34; // [sp+368h] [bp-30h]@4
  BossSchedule_Map *v35; // [sp+370h] [bp-28h]@26
  unsigned __int64 v36; // [sp+378h] [bp-20h]@28
  unsigned __int64 v37; // [sp+380h] [bp-18h]@4
  CBossMonsterScheduleSystem *v38; // [sp+3A0h] [bp+8h]@1
  int v39; // [sp+3A8h] [bp+10h]@1
  CMapData *v40; // [sp+3B0h] [bp+18h]@1

  v40 = pMap;
  v39 = nIndex;
  v38 = this;
  v3 = &v7;
  for (signed __int64 i = 228; i > 0; --i)
  {
    *(DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v34 = -2i64;
  v37 = (unsigned __int64)&v7 ^ _security_cookie;
  if ( pMap->m_bUse )
  {
    if ( pMap->m_pMapSet->m_nMapType )
    {
      result = 0;
    }
    else if ( _LAYER_SET::IsActiveLayer(pMap->m_ls) )
    {
      if ( v40->m_nMonBlockNum >= 0 )
      {
        _Dest[0] = 0;
        memset(&_Dest[1], 0, 0x103ui64);
        sprintf_s<260>((char (*)[260])_Dest, "..\\SystemSave\\%s_Boss.ini", v40->m_pMapSet->m_strFileName);
        v10 = 0;
        for ( j = 0; j < v40->m_nMonBlockNum; ++j )
        {
          v11 = (__int64 *)&v40->m_pMonBlock[j];
          v12 = *v11;
          v13 = &v40->m_ls->m_pMB->m_nBlockNum;
          v14 = CRecordData::GetRecordNum((CRecordData *)(*((QWORD *)v13 + 1) + 176i64 * j));
          for ( k = 0; k < v14; ++k )
          {
            v16 = &v40->m_ls->m_MonAct[j][k];
            if ( v16->m_nLimRegenNum > 0 )
            {
              v17 = v16->m_pActRec;
              v18 = CRecordData::GetRecord(&stru_1799C6210, v17->m_strCode);
              if ( v18 )
              {
                if ( v18[4].m_dwIndex )
                  ++v10;
              }
            }
          }
        }
        if ( v10 )
        {
          CIniFile::CIniFile(&rhs);
          v31 = (BossSchedule_Map *)operator new(0x198ui64);
          if ( v31 )
          {
            BossSchedule_Map::BossSchedule_Map(v31);
            v35 = v6;
          }
          else
          {
            v35 = 0;
          }
          v30 = v35;
          pMapSchedule = v35;
          sprintf_s<64>((char (*)[64])v35->m_strMap, "%s", v40->m_pMapSet->m_strFileName);
          pMapSchedule->m_pSystem = v38;
          pMapSchedule->m_nCount = v10;
          pMapSchedule->m_nIndex = v39;
          CIniFile::SetIniFilename(&pMapSchedule->m_INIFile, _Dest);
          CIniFile::SetIniFilename(&rhs, _Dest);
          CIniFile::Load(&rhs);
          v36 = pMapSchedule->m_nCount;
          v32 = (BossSchedule **)operator new[](saturated_mul(8ui64, v36));
          pMapSchedule->m_ScheduleList = v32;
          memset_0(pMapSchedule->m_ScheduleList, 0, 8i64 * pMapSchedule->m_nCount);
          v21 = 0;
          for ( j = 0; j < v40->m_nMonBlockNum; ++j )
          {
            pBlock = &v40->m_pMonBlock[j];
            v23 = pBlock->m_pBlkRec;
            v24 = &v40->m_ls->m_pMB->m_nBlockNum;
            v25 = CRecordData::GetRecordNum((CRecordData *)(*((QWORD *)v24 + 1) + 176i64 * j));
            for ( l = 0; l < v25; ++l )
            {
              pMonAct = &v40->m_ls->m_MonAct[j][l];
              if ( pMonAct->m_nLimRegenNum > 0 )
              {
                v28 = pMonAct->m_pActRec;
                v29 = CRecordData::GetRecord(&stru_1799C6210, v28->m_strCode);
                if ( v29 )
                {
                  if ( v29[4].m_dwIndex )
                    pMapSchedule->m_ScheduleList[v21++] = CBossMonsterScheduleSystem::MakeSchedule(
                                                            v38,
                                                            pMapSchedule,
                                                            pMonAct,
                                                            pBlock,
                                                            l,
                                                            j);
                }
              }
            }
          }
          DeleteFileA(_Dest);
          BossSchedule_Map::SaveAll(pMapSchedule);
          CIniFile::Merge_Intersection(&pMapSchedule->m_INIFile, &rhs);
          BossSchedule_Map::LoadAll(pMapSchedule);
          v33 = pMapSchedule;
          CIniFile::~CIniFile(&rhs);
          result = v33;
        }
        else
        {
          result = 0;
        }
      }
      else
      {
        result = 0;
      }
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}


