﻿/*
 * Function: ??$_Uninit_copy@V?$_Vector_iterator@GV?$allocator@G@std@@@std@@PEAGV?$allocator@G@2@@std@@YAPEAGV?$_Vector_iterator@GV?$allocator@G@std@@@0@0PEAGAEAV?$allocator@G@0@U_Nonscalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x140650B60
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


__int64 __fastcall std::_Uninit_copy<std::_Vector_iterator<unsigned short,std::allocator<unsigned short>>,unsigned short *,std::allocator<unsigned short>>(__int64 a1, __int64 a2, __int64 a3, __int64 a4)
{
  __int64 v4; // rax@3
  __int64 i; // [sp+60h] [bp+8h]@1
  __int64 v7; // [sp+68h] [bp+10h]@1
  __int64 v8; // [sp+70h] [bp+18h]@1
  __int64 v9; // [sp+78h] [bp+20h]@1

  v9 = a4;
  v8 = a3;
  v7 = a2;
  for ( i = a1;
        (unsigned __int8)std::_Vector_const_iterator<unsigned short,std::allocator<unsigned short>>::operator!=(i, v7);
        std::_Vector_iterator<unsigned short,std::allocator<unsigned short>>::operator++(i) )
  {
    LODWORD(v4) = std::_Vector_iterator<unsigned short,std::allocator<unsigned short>>::operator*(i);
    std::allocator<unsigned short>::construct(v9, v8, v4);
    v8 += 2i64;
  }
  std::_Vector_iterator<unsigned short,std::allocator<unsigned short>>::~_Vector_iterator<unsigned short,std::allocator<unsigned short>>();
  std::_Vector_iterator<unsigned short,std::allocator<unsigned short>>::~_Vector_iterator<unsigned short,std::allocator<unsigned short>>();
  return v8;
}

