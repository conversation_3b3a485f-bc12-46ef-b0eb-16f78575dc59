﻿/*
 * Function: j_?CheckMaxGuildMoney@CGuildRanking@@AEAAXKPEADPEAN1@Z
 * Address: 0x140004BFB
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CGuildRanking::CheckMaxGuildMoney(CGuildRanking * unsigned int dwGuildSerial, char *wszGuildName, long double *pdDalant, long double *pdGold)
{
  // CGuildRanking::CheckMaxGuildMoney(); // MALFORMED CONSTRUCTOR - COMMENTED OUT
}

