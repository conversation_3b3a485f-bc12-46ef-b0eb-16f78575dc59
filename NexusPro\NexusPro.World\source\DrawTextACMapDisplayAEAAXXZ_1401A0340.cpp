﻿/*
 * Function: ?DrawTextA@CMapDisplay@@AEAAXXZ
 * Address: 0x1401A0340
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// External symbol declarations for decompiled code
extern "C" ULONG_PTR _security_cookie;



void __fastcall CMapDisplay::DrawTextA(CMapDisplay *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  void *v3; // rax@5
  int v4; // eax@5
  int v5; // eax@5
  int v6; // ecx@6
  int v7; // edx@6
  __int64 v8; // r8@6
  int v9; // eax@6
  __int64 v10; // [sp+0h] [bp-138h]@1
  int c; // [sp+20h] [bp-118h]@5
  int v12; // [sp+28h] [bp-110h]@6
  HDC hdc; // [sp+38h] [bp-100h]@4
  int v14; // [sp+44h] [bp-F4h]@4
  char Dest; // [sp+60h] [bp-D8h]@5
  _map_fld *v16; // [sp+E8h] [bp-50h]@5
  int *v17; // [sp+F0h] [bp-48h]@6
  int *v18; // [sp+F8h] [bp-40h]@6
  int *v19; // [sp+100h] [bp-38h]@6
  IDirectDrawSurface7 *v20; // [sp+110h] [bp-28h]@4
  IDirectDrawSurface7 *v21; // [sp+118h] [bp-20h]@7
  unsigned __int64 v22; // [sp+120h] [bp-18h]@4
  CMapDisplay *v23; // [sp+140h] [bp+8h]@1

  v23 = this;
  v1 = &v10;
  for (signed __int64 i = 76; i > 0; --i)
  {
    *(DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v22 = (unsigned __int64)&v10 ^ _security_cookie;
  v20 = CSurface::GetDDrawSurface(v23->m_pSFMap);
  v14 = ((int (__fastcall *)(IDirectDrawSurface7 *, HDC *))v20->vfptr[5].Release)(v20, &hdc);
  if ( !v14 )
  {
    SetBkMode(hdc, 1);
    SetTextAlign(hdc, 0);
    SetTextColor(hdc, 0xFFFFFFu);
    ((DWORD)(v3) = CGdiObject::operator void *(&v23->m_Font);
    SelectObject(hdc, v3);
    v16 = pMap->m_pMapSet;
    v4 = v23->m_wLayerIndex + 1;
    c = v16->m_nLayerNum;
    sprintf(&Dest, "È°¼º¸Ê : %s.. Layer: %d / %d", v16->m_strCode, (unsigned int)v4);
    v5 = strlen_0(&Dest);
    TextOutA(hdc, 0, 0, &Dest, v5);
    if ( v23->m_MapExtend.m_bExtendMode )
    {
      v17 = &v23->m_MapExtend.m_rcExtend.left;
      v18 = CMapData::GetBspInfo(v23->m_pActMap)->m_nMapMinSize;
      v19 = CMapData::GetBspInfo(v23->m_pActMap)->m_nMapMaxSize;
      SetTextColor(hdc, 0xFFFFu);
      v6 = v17[2] + *v18;
      v7 = v19[2] - v17[3];
      v8 = (unsigned int)(*v17 + *v18);
      v12 = v19[2] - v17[1];
      c = v6;
      sprintf(&Dest, "MinX=%d, MinZ=%d, MaxX=%d, MaxZ=%d", v8, (unsigned int)v7);
      v9 = strlen_0(&Dest);
      TextOutA(hdc, 0, 12, &Dest, v9);
    }
    v21 = CSurface::GetDDrawSurface(v23->m_pSFMap);
    ((void (__fastcall *)(IDirectDrawSurface7 *, HDC))v21->vfptr[8].Release)(v21, hdc);
  }
}


