﻿/*
 * Function: ?CheckAlienation@DfAIMgr@@SAHPEAVCMonster@@@Z
 * Address: 0x140151260
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


signed __int64 __usercall DfAIMgr::CheckAlienation@<rax>(CMonster *pMon@<rcx>, float a2@<xmm0>)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 result; // rax@5
  float v5; // xmm0_4@11
  float v6; // xmm0_4@13
  float v7; // xmm0_4@13
  __int64 v8; // [sp+0h] [bp-A8h]@1
  unsigned int dwDist; // [sp+20h] [bp-88h]@11
  CMonster *v10; // [sp+28h] [bp-80h]@11
  float v11; // [sp+30h] [bp-78h]@13
  double v12; // [sp+38h] [bp-70h]@13
  float Dst; // [sp+48h] [bp-60h]@13
  float v14; // [sp+4Ch] [bp-5Ch]@13
  float v15; // [sp+50h] [bp-58h]@13
  char Src; // [sp+78h] [bp-30h]@13
  float v17; // [sp+7Ch] [bp-2Ch]@14
  CMonster *pMona; // [sp+B0h] [bp+8h]@1

  pMona = pMon;
  v2 = &v8;
  for (signed __int64 i = 40; i > 0; --i)
  {
    *(DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( pMona )
  {
    if ( CMonster::IsRoateMonster(pMona) || CMonsterHierarchy::GetParent(&pMona->m_MonHierarcy) )
    {
      result = 0;
    }
    else if ( pMona->m_pMonRec->m_nMobAlienation )
    {
      ((void (__fastcall *)(CMonster *))pMona->vfptr->GetWidth)(pMona);
      v5 = (float)(a2 / 2.0) + 20.0;
      dwDist = (signed int)ffloor(v5);
      v10 = CMonsterHelper::SearchNearMonsterByDistance(pMona, dwDist);
      if ( v10 )
      {
        GetYAngle(pMona->m_fCurPos, v10->m_fCurPos);
        v11 = v5 + 32768.0;
        v12 = 6.283185307 * (float)(v5 + 32768.0) / 65535.0;
        v6 = sin_0(v12);
        Dst = pMona->m_fCurPos[0] - (float)(v6 * (float)(signed int)dwDist);
        v7 = cos_0(v12);
        v15 = pMona->m_fCurPos[2] - (float)(v7 * (float)(signed int)dwDist);
        v14 = pMona->m_fCurPos[1];
        if ( !(unsigned int)CBsp::CanYouGoThere(
                              pMona->m_pCurMap->m_Level.mBsp,
                              pMona->m_fCurPos,
                              &Dst,
                              (float (*)[3])&Src) )
        {
          v17 = pMona->m_fCurPos[1];
          memcpy_0(&Dst, &Src, 0xCui64);
        }
        DfAIMgr::ChangeTargetPos(pMona, &Dst);
        result = 1;
      }
      else
      {
        result = 0;
      }
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}


