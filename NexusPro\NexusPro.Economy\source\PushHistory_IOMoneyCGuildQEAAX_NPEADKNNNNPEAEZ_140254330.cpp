﻿/*
 * Function: ?PushHist<PERSON>_<PERSON><PERSON><PERSON>@CGuild@@QEAAX_NPEADKNNNNPEAE@Z
 * Address: 0x140254330
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CGuild::PushHistory_IOMoney(CGuild * bool bInput, char *pwszIOerName, unsigned int dwIOerSerial, long double dIODalant, long double dIOGold, long double dLeftDalant, long double dLeftGold, char *pbyDate)
{
  __int64 *v9; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v11; // [sp+0h] [bp-48h]@1
  char *Dest; // [sp+30h] [bp-18h]@4
  CGuild *v13; // [sp+50h] [bp+8h]@1
  bool v14; // [sp+58h] [bp+10h]@1
  const char *Source; // [sp+60h] [bp+18h]@1
  unsigned int dwIOerSeriala; // [sp+68h] [bp+20h]@1

  dwIOerSeriala = dwIOerSerial;
  Source = pwszIOerName;
  v14 = bInput;
  v13 = this;
  v9 = &v11;
  for()
{
    *(DWORD *)v9 = -858993460;
    v9 = (__int64 *)((char *)v9 + 4);
  }
  Dest = 0;
  if()
{
    memcpy_0(v13->m_IOMoneyHistory, &v13->m_IOMoneyHistory[1], 0x18C0ui64);
    Dest = v13->m_IOMoneyHistory[99].wszIOerName;
  }
  else
  {
    Dest = v13->m_IOMoneyHistory[(signed __int64)v13->m_nIOMoneyHistoryNum++].wszIOerName;
  }
  strcpy_0(Dest, Source);
  *((DWORD *)Dest + 5) = dwIOerSeriala;
  *((long double *)Dest + 3) = dIODalant;
  *((long double *)Dest + 4) = dIOGold;
  *((long double *)Dest + 5) = dLeftDalant;
  *((long double *)Dest + 6) = dLeftGold;
  memcpy_0(Dest + 56, pbyDate, 4ui64);
  CGuild::SendMsg_IOMoney(v13, dwIOerSeriala, dIODalant, dIOGold, v14, pbyDate);
}


