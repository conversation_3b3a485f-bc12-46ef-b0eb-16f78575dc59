﻿/*
 * Function: _std::_Uninit_move_std::basic_string_char_std::char_traits_char__std::allocator_char________ptr64_std::basic_string_char_std::char_traits_char__std::allocator_char________ptr64_std::allocator_std::basic_string_char_std::char_traits_char__std::allocator_char________::_1_::dtor$0
 * Address: 0x140429BF0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


int __fastcall std::_Uninit_move_std::basic_string_char_std::char_traits_char__std::allocator_char________ptr64_std::basic_string_char_std::char_traits_char__std::allocator_char________ptr64_std::allocator_std::basic_string_char_std::char_traits_char__std::allocator_char________::_1_::dtor_0(__int64 a1, __int64 a2)
{
  return std::basic_string<char,std::char_traits<char>,std::allocator<char>>::~basic_string<char,std::char_traits<char>,std::allocator<char>>(a2 + 56);
}

