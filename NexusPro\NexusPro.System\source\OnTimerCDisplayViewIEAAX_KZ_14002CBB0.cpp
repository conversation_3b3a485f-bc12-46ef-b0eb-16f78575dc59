﻿/*
 * Function: ?OnTimer@CDisplayView@@IEAAX_K@Z
 * Address: 0x14002CBB0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CDisplayView::OnTimer(CDisplayView *this, unsigned __int64 nIDEvent)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-68h]@1
  CClientDC v5; // [sp+28h] [bp-40h]@6
  CDisplayView *v6; // [sp+70h] [bp+8h]@1
  unsigned __int64 v7; // [sp+78h] [bp+10h]@1

  v7 = nIDEvent;
  v6 = this;
  v2 = &v4;
  for (signed __int64 i = 24; i > 0; --i)
  {
    *(DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( !CMainThread::IsExcuteService(&g_Main) )
  {
    if ( unk_1414736F8 )
      return;
    CClientDC::CClientDC(&v5, v6);
    CClientDC::~CClientDC(&v5);
  }
  CWnd::OnTimer((CWnd *)&v6->vfptr, v7);
}


