﻿/*
 * Function: ?SetConsumeDalantFree@GuildCreateEventInfo@@IEAAX_N@Z
 * Address: 0x14025A6E0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall GuildCreateEventInfo::SetConsumeDalantFree(GuildCreateEventInfo *this, bool bEnable)
{
  if()
{
    this->m_dwEstConsumeDalant = 0;
    this->m_dwEmblemDalant = 0;
  }
  else
  {
    this->m_dwEstConsumeDalant = 24000000;
    this->m_dwEmblemDalant = 3000000;
  }
}

