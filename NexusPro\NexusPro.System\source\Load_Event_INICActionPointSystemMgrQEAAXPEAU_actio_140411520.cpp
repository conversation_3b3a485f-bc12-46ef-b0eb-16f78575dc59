﻿/*
 * Function: ?Load_Event_INI@CActionPointSystemMgr@@QEAAXPEAU_action_point_system_ini@@G@Z
 * Address: 0x140411520
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// External symbol declarations for decompiled code
extern "C" ULONG_PTR _security_cookie;



void __fastcall CActionPointSystemMgr::Load_Event_INI(CActionPointSystemMgr *this, _action_point_system_ini *pIni, unsigned __int16 wIndex)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-158h]@1
  unsigned int v6; // [sp+20h] [bp-138h]@5
  UINT v7; // [sp+24h] [bp-134h]@5
  struct tm Tm; // [sp+38h] [bp-120h]@23
  char Dst; // [sp+78h] [bp-E0h]@5
  char v10; // [sp+A8h] [bp-B0h]@5
  char v11; // [sp+D8h] [bp-80h]@5
  char DstBuf; // [sp+108h] [bp-50h]@5
  unsigned __int64 v13; // [sp+140h] [bp-18h]@4
  _action_point_system_ini *v14; // [sp+168h] [bp+10h]@1

  v14 = pIni;
  v3 = &v5;
  for (signed __int64 i = 84; i > 0; --i)
  {
    *(DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v13 = (unsigned __int64)&v5 ^ _security_cookie;
  if ( pIni )
  {
    v6 = wIndex;
    v7 = 0;
    memset_0(&Dst, 0, 0x14ui64);
    memset_0(&v10, 0, 0x14ui64);
    memset_0(&v11, 0, 0x14ui64);
    memset_0(&DstBuf, 0, 0x1Eui64);
    v14->m_bUse_event = 0;
    v14->m_EventTime[0] = 0;
    v14->m_EventTime[1] = 0;
    sprintf_s(&DstBuf, 0x1Eui64, "Action_Select_%d", v6);
    v7 = GetPrivateProfileIntA(&DstBuf, "Use", 1, "./initialize/ActionPointSystem.ini");
    if ( v7 )
      v14->m_bUse_event = 1;
    else
      v14->m_bUse_event = 0;
    v7 = GetPrivateProfileIntA(&DstBuf, "Reset", 1, "./initialize/ActionPointSystem.ini");
    if ( v7 )
      v14->m_bReset = 1;
    else
      v14->m_bReset = 0;
    v14->m_byActionCode = GetPrivateProfileIntA(&DstBuf, "Action_Code", 1, "./initialize/ActionPointSystem.ini");
    v7 = GetPrivateProfileIntA(&DstBuf, "BEGIN_YEAR", 0, "./initialize/ActionPointSystem.ini");
    if ( v7 )
    {
      v14->m_wYear[0] = v7;
      v7 = GetPrivateProfileIntA(&DstBuf, "BEGIN_MONTH", 0, "./initialize/ActionPointSystem.ini");
      if ( v7 )
      {
        v14->m_byMonth[0] = v7;
        v7 = GetPrivateProfileIntA(&DstBuf, "BEGIN_DAY", 0, "./initialize/ActionPointSystem.ini");
        if ( v7 )
        {
          v14->m_byDay[0] = v7;
          v7 = GetPrivateProfileIntA("LimitBox_Item", "BEGIN_HOUR", 0, "./initialize/ActionPointSystem.ini");
          if ( (v7 & 0x80000000) == 0 && (signed int)v7 <= 23 )
          {
            v14->m_byHour[0] = v7;
            v7 = GetPrivateProfileIntA(&DstBuf, "BEGIN_MINUTE", 0, "./initialize/ActionPointSystem.ini");
            if ( (v7 & 0x80000000) == 0 && (signed int)v7 <= 59 )
            {
              v14->m_byMinute[0] = v7;
              memset_0(&Tm, 0, 0x24ui64);
              Tm.tm_year = v14->m_wYear[0] - 1900;
              Tm.tm_mon = v14->m_byMonth[0] - 1;
              Tm.tm_mday = v14->m_byDay[0];
              Tm.tm_hour = v14->m_byHour[0];
              Tm.tm_min = v14->m_byMinute[0];
              Tm.tm_sec = 0;
              Tm.tm_isdst = -1;
              v14->m_EventTime[0] = _mktime32(&Tm);
              if ( v14->m_EventTime[0] == -1 )
              {
                v14->m_bUse_event = 0;
              }
              else
              {
                v7 = GetPrivateProfileIntA(&DstBuf, "END_YEAR", 0, "./initialize/ActionPointSystem.ini");
                if ( v7 )
                {
                  v14->m_wYear[1] = v7;
                  v7 = GetPrivateProfileIntA(&DstBuf, "END_MONTH", 0, "./initialize/ActionPointSystem.ini");
                  if ( v7 )
                  {
                    v14->m_byMonth[1] = v7;
                    v7 = GetPrivateProfileIntA(&DstBuf, "END_DAY", 0, "./initialize/ActionPointSystem.ini");
                    if ( v7 )
                    {
                      v14->m_byDay[1] = v7;
                      v7 = GetPrivateProfileIntA(&DstBuf, "END_HOUR", 0, "./initialize/ActionPointSystem.ini");
                      if ( (v7 & 0x80000000) == 0 && (signed int)v7 <= 23 )
                      {
                        v14->m_byHour[1] = v7;
                        v7 = GetPrivateProfileIntA(&DstBuf, "END_MINUTE", 0, "./initialize/ActionPointSystem.ini");
                        if ( (v7 & 0x80000000) == 0 && (signed int)v7 <= 59 )
                        {
                          v14->m_byMinute[1] = v7;
                          memset_0(&Tm, 0, 0x24ui64);
                          Tm.tm_year = v14->m_wYear[1] - 1900;
                          Tm.tm_mon = v14->m_byMonth[1] - 1;
                          Tm.tm_mday = v14->m_byDay[1];
                          Tm.tm_hour = v14->m_byHour[1];
                          Tm.tm_min = v14->m_byMinute[1];
                          Tm.tm_sec = 0;
                          Tm.tm_isdst = -1;
                          v14->m_EventTime[1] = _mktime32(&Tm);
                          if ( v14->m_EventTime[1] == -1 )
                            v14->m_bUse_event = 0;
                        }
                        else
                        {
                          v14->m_bUse_event = 0;
                        }
                      }
                      else
                      {
                        v14->m_bUse_event = 0;
                      }
                    }
                    else
                    {
                      v14->m_bUse_event = 0;
                    }
                  }
                  else
                  {
                    v14->m_bUse_event = 0;
                  }
                }
                else
                {
                  v14->m_bUse_event = 0;
                }
              }
            }
            else
            {
              v14->m_bUse_event = 0;
            }
          }
          else
          {
            v14->m_bUse_event = 0;
          }
        }
        else
        {
          v14->m_bUse_event = 0;
        }
      }
      else
      {
        v14->m_bUse_event = 0;
      }
    }
    else
    {
      v14->m_bUse_event = 0;
    }
  }
}


