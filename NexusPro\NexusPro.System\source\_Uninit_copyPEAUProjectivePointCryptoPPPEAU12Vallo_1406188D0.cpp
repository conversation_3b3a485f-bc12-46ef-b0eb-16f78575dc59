﻿/*
 * Function: ??$_Uninit_copy@PEAUProjectivePoint@CryptoPP@@PEAU12@V?$allocator@UProjectivePoint@CryptoPP@@@std@@@std@@YAPEAUProjectivePoint@CryptoPP@@PEAU12@00AEAV?$allocator@UProjectivePoint@CryptoPP@@@0@U_Nonscalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x1406188D0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


__int64 __fastcall std::_Uninit_copy<CryptoPP::ProjectivePoint *,CryptoPP::ProjectivePoint *,std::allocator<CryptoPP::ProjectivePoint>>(__int64 a1, __int64 a2, __int64 a3, __int64 a4)
{
  __int64 i; // [sp+40h] [bp+8h]@1
  __int64 v6; // [sp+48h] [bp+10h]@1
  __int64 v7; // [sp+50h] [bp+18h]@1
  __int64 v8; // [sp+58h] [bp+20h]@1

  v8 = a4;
  v7 = a3;
  v6 = a2;
  for ( i = a1; i != v6; i += 120i64 )
  {
    std::allocator<CryptoPP::ProjectivePoint>::construct(v8, v7, i);
    v7 += 120i64;
  }
  return v7;
}

