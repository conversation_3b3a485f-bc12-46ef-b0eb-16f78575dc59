﻿/*
 * Function: ?CheckSch@CWorldSchedule@@QEAAXXZ
 * Address: 0x1403F3AA0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// External symbol declarations for decompiled code
extern "C" ULONG_PTR _security_cookie;



void __fastcall CWorldSchedule::CheckSch(CWorldSchedule *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-128h]@1
  int v4; // [sp+20h] [bp-108h]@17
  int v5; // [sp+28h] [bp-100h]@17
  char *v6; // [sp+30h] [bp-F8h]@17
  DWORD v7; // [sp+40h] [bp-E8h]@4
  DWORD v8; // [sp+44h] [bp-E4h]@4
  int v9; // [sp+48h] [bp-E0h]@4
  int v10; // [sp+4Ch] [bp-DCh]@5
  int n; // [sp+50h] [bp-D8h]@7
  _base_fld *v12; // [sp+58h] [bp-D0h]@9
  _WorldSchedule_fld *pFld; // [sp+60h] [bp-C8h]@9
  char v14; // [sp+68h] [bp-C0h]@9
  char Buffer; // [sp+80h] [bp-A8h]@17
  unsigned __int64 v16; // [sp+110h] [bp-18h]@4
  CWorldSchedule *v17; // [sp+130h] [bp+8h]@1

  v17 = this;
  v1 = &v3;
  for (signed __int64 i = 72; i > 0; --i)
  {
    *(DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v16 = (unsigned __int64)&v3 ^ _security_cookie;
  v7 = timeGetTime();
  v8 = v7 - v17->m_dwLastCheckTime;
  v17->m_dwLastCheckTime = v7;
  v17->m_nCurMilSec += v8;
  v9 = v17->m_nCurMilSec / 60000;
  v17->m_nCurMilSec %= 60000;
  if ( v9 > 0 )
  {
    v17->m_nCurMin += v9;
    v10 = v17->m_nCurMin / 60;
    v17->m_nCurMin %= 60;
    if ( v10 > 0 )
    {
      v17->m_nCurHour += v10;
      v17->m_nCurHour %= 24;
    }
  }
  n = v17->m_nSchCursor + 1;
  if ( n >= v17->m_nMaxSchNum )
    n = 0;
  v12 = CRecordData::GetRecord(&v17->m_tblSch, v17->m_nSchCursor);
  pFld = (_WorldSchedule_fld *)CRecordData::GetRecord(&v17->m_tblSch, n);
  v14 = 0;
  if ( pFld->m_nHour >= (signed int)v12[1].m_dwIndex )
  {
    v14 = 1;
  }
  else if ( v17->m_nCurHour < (signed int)v12[1].m_dwIndex )
  {
    v14 = 1;
  }
  if ( v14 && v17->m_nCurHour >= pFld->m_nHour && v17->m_nCurMin >= pFld->m_nMin )
  {
    _strtime(&Buffer);
    v6 = &Buffer;
    v5 = v17->m_nCurMilSec;
    v4 = v17->m_nCurMin;
    CLogFile::Write(&g_logSchedule, "ÀÌº¥Æ®Ã¼Å©>> cur:%d, h:%d, m:%d, ms:%d (%s)", v17->m_nSchCursor, v17->m_nCurHour);
    CWorldSchedule::ChangeSchCursor(v17, pFld, 0);
    v5 = pFld->m_nEventInfo2;
    v4 = pFld->m_nEventInfo1;
    CLogFile::Write(
      &g_logSchedule,
      "ÀÌº¥Æ®¹ß»ý>> next:%s, evt:%d, info1:%d, info2:%d",
      pFld->m_strCode,
      pFld->m_nEventCode);
    v17->m_nSchCursor = pFld->m_dwIndex;
  }
}


