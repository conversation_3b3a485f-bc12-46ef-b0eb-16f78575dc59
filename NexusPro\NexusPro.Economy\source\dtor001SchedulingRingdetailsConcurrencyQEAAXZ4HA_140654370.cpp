﻿/*
 * Function: ?dtor_0@?0???1SchedulingRing@details@Concurrency@@QEAA@XZ@4HA
 * Address: 0x140654370
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Required includes for Concurrency namespace
#include <ppl.h>
#include <ppltasks.h>
#include <concurrent_vector.h>



int __fastcallConcurrency::details::SchedulingRing::~SchedulingRing'::1'::dtor_0(__int64 a1, __int64 a2)
{
  return CryptoPP::ByteQueue::~ByteQueue((CryptoPP::ByteQueue *)(*(QWORD *)(a2 + 80) + 32i64));
}


