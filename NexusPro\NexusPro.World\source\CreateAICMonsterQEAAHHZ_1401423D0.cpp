﻿/*
 * Function: ?<PERSON><PERSON><PERSON><PERSON>@C<PERSON>onster@@QEAAHH@Z
 * Address: 0x1401423D0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


__int64 __fastcall CMonster::CreateAI(CMonster *this, int nType)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  UsStateTBL *v4; // rax@6
  __int64 v6; // [sp+0h] [bp-68h]@1
  CRFMonsterAIMgr *v7; // [sp+20h] [bp-48h]@4
  UsPoint<UsStateTBL> result; // [sp+38h] [bp-30h]@5
  int v9; // [sp+44h] [bp-24h]@6
  __int64 v10; // [sp+48h] [bp-20h]@4
  Us_HFSM *pHFSM; // [sp+50h] [bp-18h]@6
  CMonster *pObject; // [sp+70h] [bp+8h]@1
  int nIndex; // [sp+78h] [bp+10h]@1

  nIndex = nType;
  pObject = this;
  v2 = &v6;
  for (signed __int64 i = 24; i > 0; --i)
  {
    *(DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v10 = -2i64;
  v7 = CRFMonsterAIMgr::Instance();
  if ( v7 )
  {
    CRFMonsterAIMgr::GetStateTBL(v7, &result, nIndex);
    if ( UsPoint<UsStateTBL>::operator UsStateTBL *(&result) )
    {
      pHFSM = (Us_HFSM *)&pObject->m_AI.vfptr;
      v4 = UsPoint<UsStateTBL>::operator->(&result);
      v9 = UsStateTBL::SetHFSM(v4, pHFSM, pObject);
      UsPoint<UsStateTBL>::~UsPoint<UsStateTBL>(&result);
      return (unsigned int)v9;
    }
    UsPoint<UsStateTBL>::~UsPoint<UsStateTBL>(&result);
  }
  CMonster::SetEmotionState(pObject, 0);
  CMonster::SetCombatState(pObject, 0);
  return 0i64;
}


