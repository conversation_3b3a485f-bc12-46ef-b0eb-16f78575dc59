﻿/*
 * Function: ?CheckMapPortalLink@CMapOperation@@AEAAXXZ
 * Address: 0x140197D40
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CMapOperation::CheckMapPortalLink(CMapOperation *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  _map_fld *v3; // rcx@12
  signed __int64 v4; // rcx@14
  _map_fld *v5; // rdx@14
  __int64 v6; // [sp+0h] [bp-68h]@1
  __int64 v7; // [sp+20h] [bp-48h]@12
  char *v8; // [sp+28h] [bp-40h]@14
  int j; // [sp+30h] [bp-38h]@4
  CMapData *v10; // [sp+38h] [bp-30h]@6
  int nPortalIndex; // [sp+40h] [bp-28h]@6
  _portal_dummy *v12; // [sp+48h] [bp-20h]@9
  CMapData *v13; // [sp+50h] [bp-18h]@11
  _portal_dummy *v14; // [sp+58h] [bp-10h]@13
  CMapOperation *v15; // [sp+70h] [bp+8h]@1

  v15 = this;
  v1 = &v6;
  for (signed __int64 i = 24; i > 0; --i)
  {
    *(DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  for ( j = 0; j < v15->m_nMapNum; ++j )
  {
    v10 = &v15->m_Map[j];
    for ( nPortalIndex = 0; nPortalIndex < v10->m_nPortalNum; ++nPortalIndex )
    {
      v12 = CMapData::GetPortal(v10, nPortalIndex);
      if ( v12->m_pPortalRec && strcmp_0(v12->m_pPortalRec->m_strLinkMapCode, "0") )
      {
        v13 = CMapOperation::GetMap(v15, v12->m_pPortalRec->m_strLinkMapCode);
        if ( v13 )
        {
          v14 = CMapData::GetPortal(v13, v12->m_pPortalRec->m_strLinkPortalCode);
          if ( !v14 )
          {
            v4 = (signed __int64)v12->m_pPortalRec->m_strLinkMapCode;
            v5 = v10->m_pMapSet;
            v8 = v12->m_pPortalRec->m_strLinkPortalCode;
            v7 = v4;
            CLogFile::Write(
              &stru_1799C8F30,
              "Portal Link Check: %s.. %dth >> Map: %s, Portal: LinkPortalCode(%s)",
              v5->m_strCode,
              (unsigned int)nPortalIndex);
          }
        }
        else
        {
          v3 = v10->m_pMapSet;
          v7 = (__int64)v12->m_pPortalRec->m_strLinkMapCode;
          CLogFile::Write(
            &stru_1799C8F30,
            "Portal Link Check: %s.. %dth >> Portal: LinkMapCode(%s)",
            v3->m_strCode,
            (unsigned int)nPortalIndex);
        }
      }
    }
  }
}


