﻿/*
 * Function: ?dtor_0@?0??AllocateScheduleGroup@SchedulingRing@details@Concurrency@@QEAAPEAVScheduleGroupBase@23@XZ@4HA
 * Address: 0x140588CB0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Required includes for Concurrency namespace
#include <ppl.h>
#include <ppltasks.h>
#include <concurrent_vector.h>



void __fastcallConcurrency::details::SchedulingRing::AllocateScheduleGroup'::1'::dtor_0(__int64 a1, __int64 a2)
{
  operator delete(*(void **)(a2 + 40));
}

