﻿/*
 * Function: ?LoadWorldSystemINI@CMainThread@@AEAAHXZ
 * Address: 0x1401E6AE0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// External symbol declarations for decompiled code
extern "C" ULONG_PTR _security_cookie;



signed __int64 __fastcall CMainThread::LoadWorldSystemINI(CMainThread *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 result; // rax@5
  __int64 v4; // [sp+0h] [bp-E8h]@1
  char ReturnedString; // [sp+40h] [bp-A8h]@4
  unsigned __int64 v6; // [sp+D0h] [bp-18h]@4
  CMainThread *v7; // [sp+F0h] [bp+8h]@1

  v7 = this;
  v1 = &v4;
  for (signed __int64 i = 56; i > 0; --i)
  {
    *(DWORD *)v1 = -*********;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v6 = (unsigned __int64)&v4 ^ _security_cookie;
  GetPrivateProfileStringA("System", "AccountAddress", "X", &ReturnedString, 0x80u, ".\\Initialize\\WorldSystem.ini");
  if ( !strcmp_0(&ReturnedString, "X") )
  {
    result = 0xFFFFFFFFi64;
  }
  else
  {
    v7->m_dwAccountIP = inet_addr(&ReturnedString);
    GetPrivateProfileStringA(
      "VersionCheck",
      "Ver_CheckKey",
      "X",
      CMainThread::ms_szClientVerCheck,
      0x21u,
      ".\\Initialize\\WorldSystem.ini");
    result = 0;
  }
  return result;
}


