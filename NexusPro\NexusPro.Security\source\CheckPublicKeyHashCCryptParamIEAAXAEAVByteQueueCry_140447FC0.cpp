﻿/*
 * Function: ?CheckPublicKeyHash@CCryptParam@@IEAAXAEAVByteQueue@CryptoPP@@@Z
 * Address: 0x140447FC0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CCryptParam::CheckPublicKeyHash(CCryptParam *this, CryptoPP::ByteQueue *kQueuePub)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char *v4; // rax@4
  unsigned __int64 v5; // rax@4
  char *v6; // rax@4
  __int64 v7; // [sp+0h] [bp-188h]@1
  CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,1> > v8; // [sp+28h] [bp-160h]@4
  unsigned __int64 length; // [sp+48h] [bp-140h]@4
  CryptoPP::SHA256 v10; // [sp+58h] [bp-130h]@4
  char v11; // [sp+128h] [bp-60h]@5
  unsigned __int8 v12; // [sp+158h] [bp-30h]@5
  __int64 v13; // [sp+160h] [bp-28h]@4
  unsigned __int64 v14; // [sp+168h] [bp-20h]@4
  CryptoPP::ClonableVtbl *v15; // [sp+170h] [bp-18h]@4
  CryptoPP::ByteQueue *v16; // [sp+198h] [bp+10h]@1

  v16 = kQueuePub;
  v2 = &v7;
  for (signed __int64 i = 96; i > 0; --i)
  {
    *(DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v13 = -2i64;
  CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,1>>::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,1>>(
    &v8,
    0i64,
    0x400ui64);
  v14 = CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,1>>::SizeInBytes(&v8);
  v4 = CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,1>>::BytePtr(&v8);
  v15 = v16->vfptr;
  ((DWORD)(v5) = ((int (__fastcall *)(CryptoPP::ByteQueue *, char *, unsigned __int64))v15[8].Clone)(v16, v4, v14);
  length = v5;
  CryptoPP::SHA256::SHA256(&v10);
  v6 = CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,1>>::BytePtr(&v8);
  if ( !CryptoPP::HashTransformation::VerifyDigest(
          (CryptoPP::HashTransformation *)&v10.vfptr,
          g_cbHashVerify,
          v6,
          length) )
  {
    memset(&v12, 0, sizeof(v12));
    std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string<char,std::char_traits<char>,std::allocator<char>>(
      &v11,
      "VerifyDigest Failure",
      v12);
    CxxThrowException_0(&v11, &TI4_AV__basic_string_DU__char_traits_D_std__V__allocator_D_2__std__);
  }
  CryptoPP::SHA256::~SHA256(&v10);
  CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,1>>::~SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,1>>(&v8);
}


