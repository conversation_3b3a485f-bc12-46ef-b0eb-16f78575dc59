﻿/*
 * Function: ?ContinueStartSystem@CHolyStoneSystem@@QEAA_NXZ
 * Address: 0x14027AFF0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


char __fastcall CHolyStoneSystem::ContinueStartSystem(CHolyStoneSystem *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  COreAmountMgr *v4; // rax@10
  COreAmountMgr *v5; // rax@10
  COreAmountMgr *v6; // rax@10
  COreAmountMgr *v7; // rax@10
  COreAmountMgr *v8; // rax@10
  COreAmountMgr *v9; // rax@10
  __int64 v10; // [sp+0h] [bp-98h]@1
  int nChangeReason; // [sp+20h] [bp-78h]@4
  int v12; // [sp+28h] [bp-70h]@10
  int v13; // [sp+30h] [bp-68h]@10
  int v14; // [sp+38h] [bp-60h]@10
  unsigned int v15; // [sp+40h] [bp-58h]@10
  unsigned int v16; // [sp+48h] [bp-50h]@10
  int Dst[7]; // [sp+58h] [bp-40h]@4
  int j; // [sp+74h] [bp-24h]@7
  __int64 *v19; // [sp+78h] [bp-20h]@9
  __int64 v20; // [sp+80h] [bp-18h]@9
  CHolyStoneSystem *v21; // [sp+A0h] [bp+8h]@1

  v21 = this;
  v1 = &v10;
  for (signed __int64 i = 36; i > 0; --i)
  {
    *(DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  memcpy_0(Dst, v21->m_SaveData.m_nStoneHP_Buffer, 0xCui64);
  nChangeReason = 2;
  if ( CHolyStoneSystem::SetScene(
         v21,
         v21->m_SaveData.m_byNumOfTime,
         v21->m_SaveData.m_nSceneCode,
         v21->m_SaveData.m_dwPassTimeInScene,
         2) )
  {
    if ( v21->m_SaveData.m_nSceneCode == 1 )
    {
      for ( j = 0; j < 3; ++j )
      {
        v19 = (__int64 *)&g_Stone[j];
        v20 = *v19;
        (*(void (__fastcall **)(__int64 *, QWORD, QWORD))(v20 + 96))(v19, (unsigned int)Dst[j], 0i64);
      }
    }
    v4 = COreAmountMgr::Instance();
    COreAmountMgr::InitRemainOreAmount(v4, v21->m_SaveData.m_dwOreRemainAmount, v21->m_SaveData.m_dwOreTotalAmount);
    v5 = COreAmountMgr::Instance();
    v21->m_SaveData.m_dwOreRemainAmount = COreAmountMgr::GetRemainOre(v5);
    v6 = COreAmountMgr::Instance();
    v21->m_SaveData.m_dwOreTotalAmount = COreAmountMgr::GetTotalOre(v6);
    v7 = COreAmountMgr::Instance();
    COreAmountMgr::InitTransferOre(v7, v21->m_SaveData.m_dwOreTransferAmount, v21->m_SaveData.m_byOreTransferCount);
    v8 = COreAmountMgr::Instance();
    v21->m_SaveData.m_dwOreTransferAmount = COreAmountMgr::GetOreTransferAmount(v8);
    v9 = COreAmountMgr::Instance();
    v21->m_SaveData.m_byOreTransferCount = COreAmountMgr::GetOreTransferCount(v9);
    v16 = v21->m_SaveData.m_dwOreRemainAmount;
    v15 = v21->m_SaveData.m_dwOreTotalAmount;
    v14 = v21->m_SaveData.m_nHolyMasterRace;
    v13 = v21->m_SaveData.m_nStoneHP_Buffer[2];
    v12 = v21->m_SaveData.m_nStoneHP_Buffer[1];
    nChangeReason = v21->m_SaveData.m_nStoneHP_Buffer[0];
    CLogFile::Write(
      &v21->m_logQuest,
      "Continue Start >> scene:%d, pass:%d, b_stHP:%d, c_stHP:%d, a_stHP:%d, master:%d, total_ore:%d, remain_ore:%d",
      v21->m_SaveData.m_nSceneCode,
      v21->m_SaveData.m_dwPassTimeInScene);
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}


