﻿/*
 * Function: ?dtor_0@?0??MoveCompletionListToRunnables@UMSThreadScheduler@details@Concurrency@@QEAA_NVlocation@3@@Z@4HA_3
 * Address: 0x140659BC0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Required includes for Concurrency namespace
#include <ppl.h>
#include <ppltasks.h>
#include <concurrent_vector.h>



void __fastcallConcurrency::details::UMSThreadScheduler::MoveCompletionListToRunnables'::1'::dtor_0(__int64 a1, __int64 a2)
{
  std::_Deque_iterator<unsigned int,std::allocator<unsigned int> ,0>::~_Deque_iterator<unsigned int,std::allocator<unsigned int>,0>(*(std::_Ranit<unsigned int,__int64,unsigned int const *,unsigned int const &> **)(a2 + 152));
}

