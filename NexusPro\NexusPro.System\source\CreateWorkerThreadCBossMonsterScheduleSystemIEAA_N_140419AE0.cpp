﻿/*
 * Function: ?CreateWorkerThread@CBossMonsterScheduleSystem@@IEAA_NXZ
 * Address: 0x140419AE0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


bool __fastcall CBossMonsterScheduleSystem::CreateWorkerThread(CBossMonsterScheduleSystem *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-68h]@1
  US::ThreadParamInterface<CBossMonsterScheduleSystem,US::AbstractThreadPool> pThreadParam; // [sp+28h] [bp-40h]@4
  CBossMonsterScheduleSystem *v6; // [sp+70h] [bp+8h]@1

  v6 = this;
  v1 = &v4;
  for (signed __int64 i = 24; i > 0; --i)
  {
    *(DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  US::ThreadParamInterface<CBossMonsterScheduleSystem,US::AbstractThreadPool>::ThreadParamInterface<CBossMonsterScheduleSystem,US::AbstractThreadPool>(&pThreadParam);
  pThreadParam.m_pOwner = v6;
  return US::CWinThread<US::ThreadParamInterface<CBossMonsterScheduleSystem,US::AbstractThreadPool>>::Create(
           (US::CWinThread<US::ThreadParamInterface<CBossMonsterScheduleSystem,US::AbstractThreadPool> > *)&v6->vfptr,
           &pThreadParam,
           (unsigned int (__cdecl *)(void *))US::CWinThread<US::ThreadParamInterface<CBossMonsterScheduleSystem,US::AbstractThreadPool>>::WorkerThread) != 0;
}


