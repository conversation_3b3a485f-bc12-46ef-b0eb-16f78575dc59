﻿/*
 * Function: ?dtor_0@?0??MoveCompletionListToRunnables@UMSThreadScheduler@details@Concurrency@@QEAA_NVlocation@3@@Z@4HA
 * Address: 0x14054F220
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Required includes for Concurrency namespace
#include <ppl.h>
#include <ppltasks.h>
#include <concurrent_vector.h>



void __fastcallConcurrency::details::UMSThreadScheduler::MoveCompletionListToRunnables'::1'::dtor_0(__int64 a1, __int64 a2)
{
  std::_Vector_iterator<unsigned int,std::allocator<unsigned int> >::~_Vector_iterator<unsigned int,std::allocator<unsigned int>>(*(std::_Vector_iterator<unsigned int,std::allocator<unsigned int> > **)(a2 + 152));
}

