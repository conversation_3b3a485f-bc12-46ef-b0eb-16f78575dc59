﻿/*
 * Function: ??$_Uninit_move@PEAUCHEAT_COMMAND@@PEAU1@V?$allocator@UCHEAT_COMMAND@@@std@@U_Undefined_move_tag@3@@std@@YAPEAUCHEAT_COMMAND@@PEAU1@00AEAV?$allocator@UCHEAT_COMMAND@@@0@U_Undefined_move_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x140223F00
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


CHEAT_COMMAND *__fastcall std::_Uninit_move<CHEAT_COMMAND *,CHEAT_COMMAND *,std::allocator<CHEAT_COMMAND>,std::_Undefined_move_tag>(CHEAT_COMMAND *_First, CHEAT_COMMAND *_Last, CHEAT_COMMAND *_Dest, std::allocator<CHEAT_COMMAND> *_Al, std::_Undefined_move_tag __formal, std::_Range_checked_iterator_tag a6)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v9; // [sp+0h] [bp-28h]@1
  CHEAT_COMMAND *_Firsta; // [sp+30h] [bp+8h]@1

  _Firsta = _First;
  v6 = &v9;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  return stdext::unchecked_uninitialized_copy<CHEAT_COMMAND *,CHEAT_COMMAND *,std::allocator<CHEAT_COMMAND>>(
           _Firsta,
           _Last,
           _Dest,
           _Al);
}

