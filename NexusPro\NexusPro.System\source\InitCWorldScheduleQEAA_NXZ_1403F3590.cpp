﻿/*
 * Function: ?Init@CWorldSchedule@@QEAA_NXZ
 * Address: 0x1403F3590
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// External symbol declarations
extern "C" IMAGE_DOS_HEADER __ImageBase;
extern "C" DWORD off_14000003C;
extern "C" void* _delayLoadHelper2(ImgDelayDescr* pidd, void** ppfnIATEntry);
extern struct EqSukData { void* pwszEpSuk; } EqSukList[16];
extern void MyMessageBox(const char* title, const char* message);


char __fastcall CWorldSchedule::Init(CWorldSchedule *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  unsigned int v4; // eax@7
  __int64 v5; // [sp+0h] [bp-248h]@1
  bool bDate[4]; // [sp+20h] [bp-228h]@19
  bool bAddCount[4]; // [sp+28h] [bp-220h]@19
  char *v8; // [sp+30h] [bp-218h]@19
  char _Dest[128]; // [sp+50h] [bp-1F8h]@7
  char pszErrMsg; // [sp+F0h] [bp-158h]@8
  int n; // [sp+174h] [bp-D4h]@12
  _WorldSchedule_fld *pFld; // [sp+178h] [bp-D0h]@14
  _base_fld *v13; // [sp+180h] [bp-C8h]@14
  int nPassMin; // [sp+188h] [bp-C0h]@14
  char Buffer; // [sp+1A0h] [bp-A8h]@19
  unsigned __int64 v16; // [sp+230h] [bp-18h]@4
  CWorldSchedule *v17; // [sp+250h] [bp+8h]@1

  v17 = this;
  v1 = &v5;
  for (signed __int64 i = 144; i > 0; --i)
  {
    *(DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v16 = (unsigned __int64)&v5 ^ _security_cookie;
  if ( v17->m_bOper )
  {
    result = 0;
  }
  else
  {
    if ( !g_logSchedule.m_bInit )
    {
      v4 = GetKorLocalTime();
      sprintf_s<128>((char (*)[128])_Dest, "..\\ZoneServerLog\\ServiceLog\\Schedule%d.log", v4);
      CLogFile::SetWriteLogFile(&g_logSchedule, _Dest, 1, 0, 1, 1);
    }
    v17->m_dwLastCheckTime = timeGetTime();
    if ( CRecordData::ReadRecord(&v17->m_tblSch, ".\\script\\Schedule.dat", 0x5Cu, &pszErrMsg) )
    {
      v17->m_nMaxSchNum = CRecordData::GetRecordNum(&v17->m_tblSch);
      if ( CWorldSchedule::DataCheck(v17) )
      {
        v17->m_nCurHour = GetCurrentHour();
        v17->m_nCurMin = GetCurrentMin();
        v17->m_nCurMilSec = 1000 * GetCurrentSec();
        v17->m_nSchCursor = CWorldSchedule::CalcScheduleCursor(v17, v17->m_nCurHour, v17->m_nCurMin);
        n = v17->m_nSchCursor + 1;
        if ( n >= v17->m_nMaxSchNum )
          n = 0;
        pFld = (_WorldSchedule_fld *)CRecordData::GetRecord(&v17->m_tblSch, v17->m_nSchCursor);
        v13 = CRecordData::GetRecord(&v17->m_tblSch, n);
        nPassMin = 0;
        if ( (signed int)v13[1].m_dwIndex < pFld->m_nHour )
        {
          if ( v17->m_nCurHour < pFld->m_nHour )
            nPassMin = v17->m_nCurMin - pFld->m_nMin + 60 * (v17->m_nCurHour + 24 - pFld->m_nHour);
          else
            nPassMin = v17->m_nCurMin - pFld->m_nMin + 60 * (v17->m_nCurHour - pFld->m_nHour);
        }
        else
        {
          nPassMin = v17->m_nCurMin - pFld->m_nMin + 60 * (v17->m_nCurHour - pFld->m_nHour);
        }
        CLogFile::Write(&g_logSchedule, "Ä¿¼­¿¡¼­ Áö³ª°£½Ã°£Ã¼Å©>> %dºÐ", (unsigned int)nPassMin);
        _strtime(&Buffer);
        v8 = &Buffer;
        *(DWORD *)bAddCount = v17->m_nCurMilSec;
        *(DWORD *)bDate = v17->m_nCurMin;
        CLogFile::Write(
          &g_logSchedule,
          "½ÃÀÛÀÌº¥Æ®Ã¼Å©>> cur:%d, h:%d, m:%d, ms:%d (%s)",
          v17->m_nSchCursor,
          v17->m_nCurHour);
        if ( !pFld->m_nEventCode )
          CWorldSchedule::ChangeSchCursor(v17, pFld, nPassMin);
        *(DWORD *)bAddCount = pFld->m_nEventInfo2;
        *(DWORD *)bDate = pFld->m_nEventInfo1;
        CLogFile::Write(
          &g_logSchedule,
          "½ÃÀÛÀÌº¥Æ®¹ß»ý>> str:%s, evt:%d, info1:%d, info2:%d",
          pFld->m_strCode,
          pFld->m_nEventCode);
        CMyTimer::BeginTimer(&v17->m_tmrCheck, 0x3E8u);
        v17->m_bOper = 1;
        result = 1;
      }
      else
      {
        result = 0;
      }
    }
    else
    {
      MyMessageBox("CWorldSchedule Error", &pszErrMsg);
      result = 0;
    }
  }
  return result;
}


