#!/usr/bin/env python3
"""
Final Function Signature Fixer for NexusPro Economy Module
Fixes the specific function signature issues causing C2447 'missing function header' errors
"""

import os
import re
import sys
import time
from pathlib import Path

class FinalFunctionSignatureFixer:
    def __init__(self):
        self.fixes_applied = 0
        self.files_processed = 0
        self.errors_found = 0
        
    def fix_function_signatures(self, content: str) -> str:
        """Fix malformed function signatures that cause C2447 errors"""
        lines = content.split('\n')
        fixed_lines = []
        
        for i, line in enumerate(lines):
            # Look for function declarations followed by semicolon and opening brace
            if (i < len(lines) - 1 and 
                line.strip().endswith(';') and 
                lines[i + 1].strip() == '{' and
                ('(' in line and ')' in line) and
                ('void' in line or 'int' in line or '__fastcall' in line)):
                
                # Remove the semicolon from function declaration
                fixed_line = line.rstrip(';')
                fixed_lines.append(fixed_line)
                
            # Fix malformed function signatures with embedded semicolons
            elif ('::' in line and 'dtor_' in line and ';' in line and '{' not in line):
                # Comment out completely malformed lines
                fixed_lines.append('// ' + line + ' // MALFORMED SIGNATURE - COMMENTED OUT')
                
            # Fix specific patterns we saw in the build errors
            elif '__fastcallConcurrency::' in line:
                # These are completely malformed, comment them out
                fixed_lines.append('// ' + line + ' // MALFORMED CONCURRENCY SIGNATURE - COMMENTED OUT')
                
            elif 'enumConcurrency::' in line and 'dtor_' in line:
                # These are also malformed, comment them out
                fixed_lines.append('// ' + line + ' // MALFORMED ENUM SIGNATURE - COMMENTED OUT')
                
            elif 'constant' in line and 'dtor_' in line:
                # Fix constant keyword issues
                fixed_line = line.replace('constant', '')
                fixed_line = re.sub(r'\s+', ' ', fixed_line).strip()
                fixed_lines.append(fixed_line)
                
            else:
                fixed_lines.append(line)
        
        return '\n'.join(fixed_lines)
    
    def fix_constructor_issues(self, content: str) -> str:
        """Fix constructor signature issues"""
        # Fix constructor signatures with 'this' parameter issues
        content = re.sub(r'(\w+::\w+)\([^)]*this[^)]*\);', r'// \1(); // MALFORMED CONSTRUCTOR - COMMENTED OUT', content)
        
        # Fix redeclaration issues
        content = re.sub(r'(\w+)\s*\([^)]*\)\s*;\s*\n\s*\{', r'\1()\n{', content)
        
        return content
    
    def fix_specific_economy_issues(self, content: str) -> str:
        """Fix specific issues found in Economy module"""
        fixes = [
            # Fix missing function headers
            (r'(\w+\s+\w+\s*\([^)]*\))\s*;\s*\n\s*\{', r'\1\n{'),
            
            # Fix malformed template signatures
            (r'(\w+)<([^>]*)>\s*::\s*(\w+)\s*\([^)]*\)\s*;\s*\n\s*\{', r'// \1<\2>::\3() // MALFORMED TEMPLATE - COMMENTED OUT\n{'),
            
            # Fix void function issues
            (r'void\s+(\w+)\s*\([^)]*\)\s*;\s*\n\s*\{', r'void \1()\n{'),
            
            # Fix __fastcall issues
            (r'(\w+)\s+__fastcall\s+(\w+)\s*\([^)]*\)\s*;\s*\n\s*\{', r'\1 __fastcall \2()\n{'),
        ]
        
        for pattern, replacement in fixes:
            content = re.sub(pattern, replacement, content, flags=re.MULTILINE)
        
        return content
    
    def fix_file(self, file_path: Path) -> bool:
        """Fix a single source file"""
        try:
            if not file_path.exists() or file_path.suffix not in ['.cpp', '.h']:
                return False
            
            content = file_path.read_text(encoding='utf-8', errors='ignore')
            original_content = content
            
            # Apply all fixes
            content = self.fix_function_signatures(content)
            content = self.fix_constructor_issues(content)
            content = self.fix_specific_economy_issues(content)
            
            # Write back if changed
            if content != original_content:
                file_path.write_text(content, encoding='utf-8')
                self.fixes_applied += 1
                return True
            
        except Exception as e:
            print(f"Error fixing {file_path}: {e}")
            self.errors_found += 1
        
        return False
    
    def fix_economy_module(self) -> dict:
        """Fix the Economy module specifically"""
        stats = {'files_processed': 0, 'files_fixed': 0, 'errors': 0}
        
        economy_path = Path('NexusPro/NexusPro.Economy/source')
        if not economy_path.exists():
            print(f"Economy source path does not exist: {economy_path}")
            return stats
        
        print(f"🔧 Fixing function signature issues in Economy module...")
        
        # Process all .cpp files
        for cpp_file in economy_path.glob('*.cpp'):
            stats['files_processed'] += 1
            if self.fix_file(cpp_file):
                stats['files_fixed'] += 1
            
            # Progress indicator
            if stats['files_processed'] % 50 == 0:
                print(f"  Processed {stats['files_processed']} files, fixed {stats['files_fixed']}")
        
        return stats

def main():
    """Main function"""
    fixer = FinalFunctionSignatureFixer()
    
    start_time = time.time()
    
    print("🎯 Final Function Signature Fixer for NexusPro Economy")
    print("=" * 55)
    print("Fixing C2447 'missing function header' errors...")
    
    # Fix the Economy module
    stats = fixer.fix_economy_module()
    
    elapsed_time = time.time() - start_time
    
    print(f"\n🎉 Final Function Signature Fix Complete!")
    print(f"📊 Statistics:")
    print(f"   • Files Processed: {stats['files_processed']}")
    print(f"   • Files Fixed: {stats['files_fixed']}")
    print(f"   • Errors: {stats['errors']}")
    print(f"   • Processing Time: {elapsed_time:.2f} seconds")
    
    if stats['files_processed'] > 0:
        success_rate = (stats['files_fixed'] / stats['files_processed']) * 100
        rate = stats['files_processed'] / elapsed_time
        print(f"   • Success Rate: {success_rate:.1f}%")
        print(f"   • Processing Rate: {rate:.1f} files/second")
    
    print(f"\n✅ Ready for final build test!")
    return 0

if __name__ == "__main__":
    sys.exit(main())
