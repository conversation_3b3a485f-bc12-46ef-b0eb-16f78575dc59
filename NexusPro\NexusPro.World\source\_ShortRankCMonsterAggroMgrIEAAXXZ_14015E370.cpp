﻿/*
 * Function: ?_ShortRank@CMonsterAggroMgr@@IEAAXXZ
 * Address: 0x14015E370
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CMonsterAggroMgr::_ShortRank(CMonsterAggroMgr *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  int v3; // eax@13
  int v4; // eax@22
  int v5; // eax@25
  int v6; // eax@28
  int v7; // eax@31
  __int64 v8; // [sp+0h] [bp-78h]@1
  CCharacter *v9; // [sp+20h] [bp-58h]@4
  int j; // [sp+28h] [bp-50h]@4
  int v11; // [sp+2Ch] [bp-4Ch]@4
  int v12; // [sp+30h] [bp-48h]@4
  int v13; // [sp+34h] [bp-44h]@4
  float v14; // [sp+38h] [bp-40h]@4
  int v15; // [sp+3Ch] [bp-3Ch]@4
  int v16; // [sp+40h] [bp-38h]@4
  float v17; // [sp+44h] [bp-34h]@27
  __int64 v18; // [sp+48h] [bp-30h]@22
  __int64 v19; // [sp+50h] [bp-28h]@25
  __int64 v20; // [sp+58h] [bp-20h]@28
  __int64 v21; // [sp+60h] [bp-18h]@31
  CMonsterAggroMgr *v22; // [sp+80h] [bp+8h]@1

  v22 = this;
  v1 = &v8;
  for (signed __int64 i = 28; i > 0; --i)
  {
    *(DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v9 = v22->m_pTopAggroCharacter;
  v22->m_pTopDamageCharacter = 0;
  v22->m_pTopAggroCharacter = 0;
  v22->m_pKingPowerDamageCharacter = 0;
  v11 = -1000;
  v12 = 0;
  v13 = 0;
  v14 = FLOAT_1000_0;
  v15 = 10000;
  v16 = -1;
  for ( j = 0; j < 10; ++j )
  {
    if ( CAggroNode::IsLive(&v22->m_AggroPool[j]) )
    {
      if ( v22->m_AggroPool[j].m_nDamageData > v12 )
      {
        v12 = v22->m_AggroPool[j].m_nDamageData;
        v22->m_pTopDamageCharacter = v22->m_AggroPool[j].m_pCharacter;
      }
      if ( v22->m_AggroPool[j].m_nKingPowerDamage > v13 )
      {
        v13 = v22->m_AggroPool[j].m_nKingPowerDamage;
        v22->m_pKingPowerDamageCharacter = v22->m_AggroPool[j].m_pCharacter;
      }
      if ( v22->m_AggroPool[j].m_pCharacter && !v22->m_AggroPool[j].m_pCharacter->m_ObjID.m_byID )
      {
        v3 = ((int (__fastcall *)(CCharacter *))v22->m_AggroPool[j].m_pCharacter->vfptr->GetLevel)(v22->m_AggroPool[j].m_pCharacter);
        if ( v3 < v15 )
          v16 = j;
      }
    }
    else
    {
      CAggroNode::Init(&v22->m_AggroPool[j]);
    }
  }
  for ( j = 0; j < 10; ++j )
  {
    if ( CAggroNode::IsLive(&v22->m_AggroPool[j])
      && v22->m_pKingPowerDamageCharacter == v22->m_AggroPool[j].m_pCharacter )
    {
      v18 = 24i64 * j;
      v4 = AggroCaculateData::GetDefault(&g_AggroCaculateData, 0xAu);
      v22->m_AggroPool[j].m_nAggroData = v22->m_AggroPool[(unsigned __int64)v18 / 0x18].m_nAggroData + v4;
    }
    if ( CAggroNode::IsLive(&v22->m_AggroPool[j])
      && v22->m_pKingPowerDamageCharacter == v22->m_AggroPool[j].m_pCharacter )
    {
      v19 = 24i64 * j;
      v5 = AggroCaculateData::GetDefault(&g_AggroCaculateData, 0xBu);
      v22->m_AggroPool[j].m_nAggroData = v22->m_AggroPool[(unsigned __int64)v19 / 0x18].m_nAggroData + v5;
    }
    if ( CAggroNode::IsLive(&v22->m_AggroPool[j]) )
    {
      GetSqrt(v22->m_pMonster->m_fCurPos, v22->m_AggroPool[j].m_pCharacter->m_fCurPos);
      v17 = FLOAT_1000_0;
      CMonster::GetBonusInAreaAggro(v22->m_pMonster);
      if ( v17 <= 1000.0 )
      {
        v20 = 24i64 * j;
        v6 = AggroCaculateData::GetDefault(&g_AggroCaculateData, 8u);
        v22->m_AggroPool[j].m_nAggroData = v22->m_AggroPool[(unsigned __int64)v20 / 0x18].m_nAggroData + v6;
      }
    }
    if ( v16 != -1 && CAggroNode::IsLive(&v22->m_AggroPool[j]) )
    {
      v21 = 24i64 * j;
      v7 = AggroCaculateData::GetDefault(&g_AggroCaculateData, 0xCu);
      v22->m_AggroPool[j].m_nAggroData = v22->m_AggroPool[(unsigned __int64)v21 / 0x18].m_nAggroData + v7;
    }
    if ( CAggroNode::IsLive(&v22->m_AggroPool[j]) && v22->m_AggroPool[j].m_nAggroData > v11 )
    {
      v11 = v22->m_AggroPool[j].m_nAggroData;
      v22->m_pTopAggroCharacter = v22->m_AggroPool[j].m_pCharacter;
    }
  }
  if ( v9 != v22->m_pTopAggroCharacter && v22->m_pTopAggroCharacter )
  {
    CMonster::CheckEventEmotionPresentation(v22->m_pMonster, 10, v22->m_pTopAggroCharacter);
    Us_HFSM::SendExternMsg((Us_HFSM *)&v22->m_pMonster->m_AI.vfptr, 0, v22->m_pTopAggroCharacter, 0);
  }
}


