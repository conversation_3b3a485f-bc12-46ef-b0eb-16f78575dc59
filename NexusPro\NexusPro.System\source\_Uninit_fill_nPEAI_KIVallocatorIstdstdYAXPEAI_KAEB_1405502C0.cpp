﻿/*
 * Function: ??$_Uninit_fill_n@PEAI_KIV?$allocator@I@std@@@std@@YAXPEAI_KAEBIAEAV?$allocator@I@0@U_Scalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x1405502C0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


int std::_Uninit_fill_n<unsigned int *,unsigned __int64,unsigned int,std::allocator<unsigned int>>()
{
  return stdext::unchecked_fill_n<unsigned int *,unsigned __int64,unsigned int>();
}

