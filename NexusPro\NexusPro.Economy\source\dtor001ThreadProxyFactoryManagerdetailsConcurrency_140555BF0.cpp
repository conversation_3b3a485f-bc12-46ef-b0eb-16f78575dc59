﻿/*
 * Function: ?dtor_0@?0???**************************@details@Concurrency@@QEAA@XZ@4HA_1
 * Address: 0x140555BF0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Required includes for Concurrency namespace
#include <ppl.h>
#include <ppltasks.h>
#include <concurrent_vector.h>



int __fastcallConcurrency::details::ThreadProxyFactoryManager::~ThreadProxyFactoryManager'::1'::dtor_0(__int64 a1, __int64 a2)
{
  return CryptoPP::EuclideanDomainOf<CryptoPP::PolynomialMod2> ::~EuclideanDomainOf<CryptoPP::PolynomialMod2>(*(QWORD *)(a2 + 64) + 24i64);
}


