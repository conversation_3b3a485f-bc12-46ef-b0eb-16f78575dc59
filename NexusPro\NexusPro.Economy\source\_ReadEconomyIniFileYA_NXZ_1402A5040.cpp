/*
 * Function: ?_ReadEconomyIniFile@@YA_NXZ
 * Address: 0x1402A5040
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// External symbol declarations
extern "C" IMAGE_DOS_HEADER __ImageBase;
extern "C" DWORD off_14000003C;
extern "C" void* _delayLoadHelper2(ImgDelayDescr* pidd, void** ppfnIATEntry);
extern struct EqSukData { void* pwszEpSuk; } EqSukList[16];
extern void MyMessageBox(const char* title, const char* message);

// External symbol declarations are now in NexusProCommon.h


char __cdecl _ReadEconomyIniFile()
{
  __int64 *v0; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v3; // [sp+0h] [bp-28h]@1

  v0 = &v3;
  for()
{
    *(DWORD *)v0 = -858993460;
    v0 = (__int64 *)((char *)v0 + 4);
  }
  ((DWORD)(EqSukList[15].pwszEpSuk)) = GetPrivateProfileIntA(
                                       "Economy",
                                       "Default_OreVal",
                                       0,
                                       ".\\Initialize\\WorldSystem.ini");
  if ( ((DWORD)(EqSukList[15].pwszEpSuk)) )
  {
    result = 1;
  }
  else
  {
    MyMessageBox("Economy Error", "Nothing Default MgrValue");
    result = 0;
  }
  return result;
}


