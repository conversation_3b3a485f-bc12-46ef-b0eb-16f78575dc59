﻿/*
 * Function: ?Loop@CVoteSystem@@QEAAXXZ
 * Address: 0x1402B0620
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// External symbol declarations for decompiled code
extern "C" ULONG_PTR _security_cookie;



void __fastcall CVoteSystem::Loop(CVoteSystem *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-A8h]@1
  unsigned int v4; // [sp+30h] [bp-78h]@5
  char szMsg[4]; // [sp+48h] [bp-60h]@12
  __int16 v6[3]; // [sp+4Ch] [bp-5Ch]@14
  bool v7; // [sp+52h] [bp-56h]@15
  unsigned int dwClientIndex; // [sp+64h] [bp-44h]@12
  char pbyType; // [sp+74h] [bp-34h]@15
  char v10; // [sp+75h] [bp-33h]@15
  CPlayer *v11; // [sp+88h] [bp-20h]@18
  unsigned __int64 v12; // [sp+98h] [bp-10h]@4
  CVoteSystem *v13; // [sp+B0h] [bp+8h]@1

  v13 = this;
  v1 = &v3;
  for (signed __int64 i = 40; i > 0; --i)
  {
    *(DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v12 = (unsigned __int64)&v3 ^ _security_cookie;
  if ( v13->m_bActive )
  {
    v4 = GetLoopTime();
    if ( v13->m_bHurry )
    {
      if ( v4 - v13->m_dwStartVoteTime > 0x493E0 )
      {
        CVoteSystem::StopVote(v13);
        return;
      }
    }
    else if ( v4 - v13->m_dwStartVoteTime > 0x3A980 )
    {
      v13->m_bHurry = 1;
    }
    v13->m_SendStarted.wLeftSec = (300000 - (v4 - v13->m_dwStartVoteTime)) / 0x3E8;
    if ( v4 - v13->m_dwLastBroadcastTime > 0x2710 )
    {
      v13->m_dwLastBroadcastTime = v4;
      *(DWORD *)szMsg = v13->m_nSerial;
      for ( dwClientIndex = 0; (signed int)dwClientIndex < 3; ++dwClientIndex )
        v6[dwClientIndex] = v13->m_dwPoint[dwClientIndex];
      v7 = v13->m_bHurry;
      pbyType = 26;
      v10 = 4;
      for ( dwClientIndex = 0; (signed int)dwClientIndex < 2532; ++dwClientIndex )
      {
        v11 = &g_Player + (signed int)dwClientIndex;
        if ( v11->m_bLive )
        {
          if ( CPlayerDB::GetRaceCode(&v11->m_Param) == v13->m_byRaceCode )
            CNetProcess::LoadSendMsg(unk_1414F2088, dwClientIndex, &pbyType, szMsg, 0xBu);
        }
      }
    }
  }
}


