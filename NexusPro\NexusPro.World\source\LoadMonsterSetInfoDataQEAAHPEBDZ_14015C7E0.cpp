﻿/*
 * Function: ?Load@MonsterSetInfoData@@QEAAHPEBD@Z
 * Address: 0x14015C7E0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// External symbol declarations for decompiled code
extern "C" ULONG_PTR _security_cookie;



__int64 __fastcall MonsterSetInfoData::Load(MonsterSetInfoData *this, const char *strFileName)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 result; // rax@7
  char v5; // al@12
  __int64 v6; // [sp+0h] [bp-6A8h]@1
  char Dest; // [sp+40h] [bp-668h]@6
  char ReturnedString; // [sp+160h] [bp-548h]@6
  unsigned int j; // [sp+364h] [bp-344h]@4
  int k; // [sp+368h] [bp-340h]@4
  char Dst; // [sp+380h] [bp-328h]@8
  char v12; // [sp+3C0h] [bp-2E8h]@8
  char v13; // [sp+400h] [bp-2A8h]@8
  char v14; // [sp+440h] [bp-268h]@8
  char v15; // [sp+480h] [bp-228h]@8
  char v16; // [sp+4C0h] [bp-1E8h]@8
  char v17; // [sp+500h] [bp-1A8h]@8
  char *ppszDst; // [sp+558h] [bp-150h]@8
  char *v19; // [sp+560h] [bp-148h]@8
  char *v20; // [sp+568h] [bp-140h]@8
  char *v21; // [sp+570h] [bp-138h]@8
  char *v22; // [sp+578h] [bp-130h]@8
  char *v23; // [sp+580h] [bp-128h]@8
  char *v24; // [sp+588h] [bp-120h]@8
  int v25; // [sp+594h] [bp-114h]@8
  char v26; // [sp+598h] [bp-110h]@15
  char v27; // [sp+5B0h] [bp-F8h]@19
  char v28; // [sp+5F0h] [bp-B8h]@19
  char *Source; // [sp+648h] [bp-60h]@19
  char *v30; // [sp+650h] [bp-58h]@19
  int v31; // [sp+664h] [bp-44h]@19
  UINT v32; // [sp+668h] [bp-40h]@31
  char (*v33)[64]; // [sp+678h] [bp-30h]@15
  void *v34; // [sp+680h] [bp-28h]@27
  unsigned __int64 v35; // [sp+688h] [bp-20h]@15
  unsigned __int64 v36; // [sp+690h] [bp-18h]@4
  MonsterSetInfoData *v37; // [sp+6B0h] [bp+8h]@1
  LPCSTR v38; // [sp+6B8h] [bp+10h]@1

  v38 = strFileName;
  v37 = this;
  v2 = &v6;
  for (signed __int64 i = 424; i > 0; --i)
  {
    *(DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v36 = (unsigned __int64)&v6 ^ _security_cookie;
  k = 0;
  for ( j = 0; (signed int)j < 4; ++j )
  {
    sprintf(&Dest, "eLevel_ContSF_Time_%d", j);
    GetPrivateProfileStringA("Common", &Dest, "-1", &ReturnedString, 0x200u, v38);
    if ( !strncmp(&ReturnedString, "-1", 2ui64) )
      return 0i64;
    ppszDst = &Dst;
    v19 = &v12;
    v20 = &v13;
    v21 = &v14;
    v22 = &v15;
    v23 = &v16;
    v24 = &v17;
    memset_0(&Dst, 0, 0x1C0ui64);
    v25 = ParsingCommandA(&ReturnedString, 7, &ppszDst, 64);
    if ( v25 != 7 )
      return 0i64;
    for ( k = 0; k < 7; ++k )
    {
      v5 = atoi((&ppszDst)[8 * k]);
      v37->m_byLevel_ContSFTime[j][k] = v5;
    }
  }
  v37->m_nMonsterLostTargetDistance = GetPrivateProfileIntA("Common", "MonsterLostTargetDistance", 285, v38);
  v37->m_fMonsterForcePowerRate = (float)(signed int)GetPrivateProfileIntA("Common", "MonsterForcePowerRate", 40, v38);
  v37->m_nMonBlkCount = GetPrivateProfileIntA("MonsRotBlk", "m_nMonBlkCount", -1, v38);
  if ( v37->m_nMonBlkCount <= 0 )
    goto LABEL_51;
  v35 = 2 * v37->m_nMonBlkCount;
  v33 = (char (*)[64])operator new[](saturated_mul(0x40ui64, v35));
  v37->m_strRotMonBlk_Ar = v33;
  v26 = 1;
  for ( j = 0; (signed int)j < v37->m_nMonBlkCount; ++j )
  {
    sprintf(&Dest, "Blk_%d", j);
    GetPrivateProfileStringA("MonsRotBlk", &Dest, "-1", &ReturnedString, 0x200u, v38);
    if ( !strncmp(&ReturnedString, "-1", 2ui64) )
    {
      v26 = 0;
      break;
    }
    Source = &v27;
    v30 = &v28;
    memset_0(&v27, 0, 0x80ui64);
    v31 = ParsingCommandA(&ReturnedString, 2, &Source, 64);
    if ( v31 != 2 )
    {
      v26 = 0;
      break;
    }
    for ( k = 0; k < 2; ++k )
      strcpy_0(v37->m_strRotMonBlk_Ar[(signed __int64)(signed int)(k + 2 * j)], (&Source)[8 * k]);
  }
  if ( v26 )
  {
LABEL_51:
    for ( j = 0; (signed int)j < 7; ++j )
    {
      sprintf(&Dest, "lv_%d", j);
      v32 = GetPrivateProfileIntA("MonsDebuffTol", &Dest, 100, v38);
      v37->m_fToleranceProbMax[j] = (float)(signed int)v32 / 100.0;
    }
    v37->m_iMonsterLootRateSame = GetPrivateProfileIntA("DROPRATE", "SAME_WITH_MONSTER", 100, v38);
    if ( v37->m_iMonsterLootRateSame )
    {
      for ( j = 1; (signed int)j <= 10; ++j )
      {
        sprintf(&Dest, "BIG_%d_MONSTER", j);
        v37->m_iMonsterLootingRateUp[j - 1] = GetPrivateProfileIntA("DROPRATE", &Dest, 0, v38);
        if ( !v37->m_iMonsterLootingRateUp[j - 1] )
        {
          if ( j == 1 )
            return 0i64;
          v37->m_iMonsterLootingRateUp[j - 1] = v37->m_iMonsterLootingRateUp[j - 2];
        }
      }
      v37->m_iMonsterLootingRateUp[10] = v37->m_iMonsterLootingRateUp[9];
      for ( j = 1; (signed int)j <= 10; ++j )
      {
        sprintf(&Dest, "SMALL_%d_MONSTER", j);
        v37->m_iMonsterLootingRateDown[j - 1] = GetPrivateProfileIntA("DROPRATE", &Dest, 0, v38);
        if ( !v37->m_iMonsterLootingRateDown[j - 1] )
        {
          if ( j == 1 )
            return 0i64;
          v37->m_iMonsterLootingRateDown[j - 1] = v37->m_iMonsterLootingRateDown[j - 2];
        }
      }
      v37->m_iMonsterLootingRateDown[10] = v37->m_iMonsterLootingRateDown[9];
      v37->m_bLoad = 1;
      result = v37->m_bLoad;
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    if ( v37->m_strRotMonBlk_Ar )
    {
      v34 = v37->m_strRotMonBlk_Ar;
      operator delete[](v34);
      v37->m_strRotMonBlk_Ar = 0;
      v37->m_nMonBlkCount = 0;
    }
    result = 0;
  }
  return result;
}


