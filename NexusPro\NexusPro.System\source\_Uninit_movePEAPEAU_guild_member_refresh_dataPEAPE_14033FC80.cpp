﻿/*
 * Function: ??$_Uninit_move@PEAPEAU_guild_member_refresh_data@@PEAPEAU1@V?$allocator@PEAU_guild_member_refresh_data@@@std@@U_Undefined_move_tag@3@@std@@YAPEAPEAU_guild_member_refresh_data@@PEAPEAU1@00AEAV?$allocator@PEAU_guild_member_refresh_data@@@0@U_Undefined_move_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x14033FC80
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


_guild_member_refresh_data **__fastcall std::_Uninit_move<_guild_member_refresh_data * *,_guild_member_refresh_data * *,std::allocator<_guild_member_refresh_data *>,std::_Undefined_move_tag>(_guild_member_refresh_data **_First, _guild_member_refresh_data **_Last, _guild_member_refresh_data **_Dest, std::allocator<_guild_member_refresh_data *> *_Al, std::_Undefined_move_tag __formal, std::_Range_checked_iterator_tag a6)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v9; // [sp+0h] [bp-28h]@1
  _guild_member_refresh_data **_Firsta; // [sp+30h] [bp+8h]@1

  _Firsta = _First;
  v6 = &v9;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  return stdext::unchecked_uninitialized_copy<_guild_member_refresh_data * *,_guild_member_refresh_data * *,std::allocator<_guild_member_refresh_data *>>(
           _Firsta,
           _Last,
           _Dest,
           _Al);
}

