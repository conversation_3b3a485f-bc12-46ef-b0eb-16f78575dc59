﻿/*
 * Function: ??$_Uninit_copy@V?$_Vector_const_iterator@UECPPoint@CryptoPP@@V?$allocator@UECPPoint@CryptoPP@@@std@@@std@@PEAUECPPoint@CryptoPP@@V?$allocator@UECPPoint@CryptoPP@@@2@@std@@YAPEAUECPPoint@CryptoPP@@V?$_Vector_const_iterator@UECPPoint@CryptoPP@@V?$allocator@UECPPoint@CryptoPP@@@std@@@0@0PEAU12@AEAV?$allocator@UECPPoint@CryptoPP@@@0@U_Nonscalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x1405ACEC0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


CryptoPP::ECPPoint *__fastcall std::_Uninit_copy<std::_Vector_const_iterator<CryptoPP::ECPPoint,std::allocator<CryptoPP::ECPPoint>>,CryptoPP::ECPPoint *,std::allocator<CryptoPP::ECPPoint>>(std::_Vector_const_iterator<CryptoPP::ECPPoint,std::allocator<CryptoPP::ECPPoint> > *a1, std::_Vector_const_iterator<CryptoPP::ECPPoint,std::allocator<CryptoPP::ECPPoint> > *a2, CryptoPP::ECPPoint *a3, std::allocator<CryptoPP::ECPPoint> *a4)
{
  CryptoPP::ECPPoint *v4; // rax@3
  std::_Vector_const_iterator<CryptoPP::ECPPoint,std::allocator<CryptoPP::ECPPoint> > *i; // [sp+60h] [bp+8h]@1
  std::_Vector_const_iterator<CryptoPP::ECPPoint,std::allocator<CryptoPP::ECPPoint> > *_Right; // [sp+68h] [bp+10h]@1
  CryptoPP::ECPPoint *_Ptr; // [sp+70h] [bp+18h]@1
  std::allocator<CryptoPP::ECPPoint> *v9; // [sp+78h] [bp+20h]@1

  v9 = a4;
  _Ptr = a3;
  _Right = a2;
  for ( i = a1;
        std::_Vector_const_iterator<CryptoPP::ECPPoint,std::allocator<CryptoPP::ECPPoint>>::operator!=(i, _Right);
        std::_Vector_const_iterator<CryptoPP::ECPPoint,std::allocator<CryptoPP::ECPPoint>>::operator++(i) )
  {
    LODWORD(v4) = std::_Vector_const_iterator<CryptoPP::ECPPoint,std::allocator<CryptoPP::ECPPoint>>::operator*(i);
    std::allocator<CryptoPP::ECPPoint>::construct(v9, _Ptr, v4);
    ++_Ptr;
  }
  std::_Vector_const_iterator<CryptoPP::ECPPoint,std::allocator<CryptoPP::ECPPoint>>::~_Vector_const_iterator<CryptoPP::ECPPoint,std::allocator<CryptoPP::ECPPoint>>(i);
  std::_Vector_const_iterator<CryptoPP::ECPPoint,std::allocator<CryptoPP::ECPPoint>>::~_Vector_const_iterator<CryptoPP::ECPPoint,std::allocator<CryptoPP::ECPPoint>>(_Right);
  return _Ptr;
}

