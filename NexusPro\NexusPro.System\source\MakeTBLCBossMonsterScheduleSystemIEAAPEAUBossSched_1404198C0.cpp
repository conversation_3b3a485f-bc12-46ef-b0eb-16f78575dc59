﻿/*
 * Function: ?MakeTBL@CBossMonsterScheduleSystem@@IEAAPEAUBossSchedule_TBL@@PEAVCMapOperation@@@Z
 * Address: 0x1404198C0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


BossSchedule_TBL *__fastcall CBossMonsterScheduleSystem::MakeTBL(CBossMonsterScheduleSystem *this, CMapOperation *pMapOper)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  BossSchedule_TBL *result; // rax@6
  BossSchedule_TBL *v5; // rax@8
  __int64 v6; // [sp+0h] [bp-78h]@1
  BossSchedule_TBL *v7; // [sp+20h] [bp-58h]@10
  int nIndex; // [sp+28h] [bp-50h]@10
  CMapData *pMap; // [sp+30h] [bp-48h]@12
  BossSchedule_TBL *v10; // [sp+38h] [bp-40h]@10
  BossSchedule_TBL *v11; // [sp+40h] [bp-38h]@7
  BossSchedule_Map **v12; // [sp+48h] [bp-30h]@10
  __int64 v13; // [sp+50h] [bp-28h]@4
  BossSchedule_TBL *v14; // [sp+58h] [bp-20h]@8
  unsigned __int64 v15; // [sp+60h] [bp-18h]@10
  CBossMonsterScheduleSystem *v16; // [sp+80h] [bp+8h]@1
  CMapOperation *v17; // [sp+88h] [bp+10h]@1

  v17 = pMapOper;
  v16 = this;
  v2 = &v6;
  for (signed __int64 i = 28; i > 0; --i)
  {
    *(DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v13 = -2i64;
  if ( pMapOper && pMapOper->m_nMapNum >= 0 )
  {
    v11 = (BossSchedule_TBL *)operator new(0x10ui64);
    if ( v11 )
    {
      BossSchedule_TBL::BossSchedule_TBL(v11);
      v14 = v5;
    }
    else
    {
      v14 = 0;
    }
    v10 = v14;
    v7 = v14;
    v14->m_nCount = v17->m_nMapNum;
    v15 = v7->m_nCount;
    v12 = (BossSchedule_Map **)operator new[](saturated_mul(8ui64, v15));
    v7->m_MapScheduleList = v12;
    memset_0(v7->m_MapScheduleList, 0, 8i64 * v7->m_nCount);
    for ( nIndex = 0; nIndex < v7->m_nCount; ++nIndex )
    {
      pMap = &v16->m_pMapOper->m_Map[nIndex];
      v7->m_MapScheduleList[nIndex] = CBossMonsterScheduleSystem::MakeMap(v16, nIndex, pMap);
    }
    result = v7;
  }
  else
  {
    result = 0;
  }
  return result;
}


