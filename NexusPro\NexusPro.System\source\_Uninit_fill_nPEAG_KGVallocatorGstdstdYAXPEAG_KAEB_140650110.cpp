﻿/*
 * Function: ??$_Uninit_fill_n@PEAG_KGV?$allocator@G@std@@@std@@YAXPEAG_KAEBGAEAV?$allocator@G@0@U_Scalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x140650110
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


int std::_Uninit_fill_n<unsigned short *,unsigned __int64,unsigned short,std::allocator<unsigned short>>()
{
  return stdext::unchecked_fill_n<unsigned short *,unsigned __int64,unsigned short>();
}

