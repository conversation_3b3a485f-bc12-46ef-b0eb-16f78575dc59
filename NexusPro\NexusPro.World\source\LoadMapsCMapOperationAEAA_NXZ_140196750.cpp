﻿/*
 * Function: ?LoadMaps@CMapOperation@@AEAA_NXZ
 * Address: 0x140196750
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// External symbol declarations
extern "C" IMAGE_DOS_HEADER __ImageBase;
extern "C" DWORD off_14000003C;
extern "C" void* _delayLoadHelper2(ImgDelayDescr* pidd, void** ppfnIATEntry);
extern struct EqSukData { void* pwszEpSuk; } EqSukList[16];
extern void MyMessageBox(const char* title, const char* message);


char __fastcall CMapOperation::LoadMaps(CMapOperation *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char v3; // al@7
  CItemStoreManager *v4; // rax@8
  CItemStoreManager *v5; // rax@16
  std::pair<int,int> *v6; // rax@17
  __int64 v7; // [sp+0h] [bp-488h]@1
  char *ppszMapNameList[100]; // [sp+30h] [bp-458h]@6
  unsigned int dwIndex; // [sp+354h] [bp-134h]@4
  _map_fld *pMapSet; // [sp+358h] [bp-130h]@6
  CMapItemStoreList *v11; // [sp+360h] [bp-128h]@8
  int nStoreNum; // [sp+368h] [bp-120h]@12
  CMerchant *v13; // [sp+370h] [bp-118h]@14
  _npc_create_setdata pData; // [sp+388h] [bp-100h]@16
  CItemStore *v15; // [sp+3C8h] [bp-C0h]@16
  char pszErrMsg; // [sp+3E0h] [bp-A8h]@19
  std::pair<int,int> result; // [sp+470h] [bp-18h]@17
  unsigned __int64 v18; // [sp+478h] [bp-10h]@4
  CMapOperation *v19; // [sp+490h] [bp+8h]@1

  v19 = this;
  v1 = &v7;
  for (signed __int64 i = 288; i > 0; --i)
  {
    *(DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v18 = (unsigned __int64)&v7 ^ _security_cookie;
  for ( dwIndex = 0; (signed int)dwIndex < v19->m_nMapNum; ++dwIndex )
  {
    pMapSet = CMapDataTable::GetRecord(&v19->m_tblMapData, dwIndex);
    CMapData::Init(&v19->m_Map[dwIndex], pMapSet);
    ppszMapNameList[dwIndex] = pMapSet->m_strCode;
    NetTrace("%s¸Ê ·Îµå½ÃÀÛ", pMapSet->m_strCode);
    if ( !CMapData::OpenMap(&v19->m_Map[dwIndex], pMapSet->m_strFileName, pMapSet, 1) )
    {
      MyMessageBox("Map Load Error", "%s - Read Error", pMapSet->m_strFileName);
      return 0;
    }
    v4 = CItemStoreManager::Instance();
    v11 = CItemStoreManager::GetMapItemStoreListByNum(v4, dwIndex);
    CMapItemStoreList::SetTypeNSerial(v11, 0, v19->m_Map[dwIndex].m_nMapIndex);
    if ( !CMapItemStoreList::CreateStores(v11, &v19->m_Map[dwIndex]) )
    {
      MyMessageBox("ItemStore Load Error", "LoadMaps() : pMapItemStoreList->CreateStores(%s)", pMapSet->m_strFileName);
      return 0;
    }
    NetTrace("%s¸Ê ·Îµå¿Ï·á", pMapSet->m_strCode);
    if ( !pMapSet->m_nMapType )
    {
      if ( v19->m_Map[dwIndex].m_pItemStoreDummy )
      {
        for ( nStoreNum = 0; nStoreNum < v19->m_Map[dwIndex].m_nItemStoreDumNum; ++nStoreNum )
        {
          v13 = FindEmptyNPC(g_NPC, 500);
          if ( !v13 )
          {
            MyMessageBox("error", "CMapOperation::LoadMaps() : NPC°¡ ¸ðÁú¶ó..");
            break;
          }
          _npc_create_setdata::_npc_create_setdata(&pData);
          CMapData::GetRandPosInDummy(
            &v19->m_Map[dwIndex],
            v19->m_Map[dwIndex].m_pItemStoreDummy[nStoreNum].m_pDumPos,
            pData.m_fStartPos,
            1);
          v5 = CItemStoreManager::Instance();
          v15 = CItemStoreManager::GetMapItemStoreFromList(v5, dwIndex, nStoreNum);
          pData.m_pLinkItemStore = v15;
          pData.m_pMap = &v19->m_Map[dwIndex];
          pData.m_nLayerIndex = 0;
          pData.m_pRecordSet = CItemStore::GetNpcRecord(v15);
          pData.m_byRaceCode = v15->m_byNpcRaceCode;
          CMerchant::Create(v13, &pData);
        }
      }
      v6 = std::make_pair<int,int>(&result, v19->m_Map[dwIndex].m_nMapCode, v19->m_nStdMapNum);
      std::vector<std::pair<int,int>,std::allocator<std::pair<int,int>>>::push_back(&v19->m_vecStandardMapCodeTable, v6);
      ++v19->m_nStdMapNum;
    }
  }
  CMapOperation::CheckMapPortalLink(v19);
  if ( LoadRegionData(v19->m_nMapNum, ppszMapNameList, &pszErrMsg) )
  {
    v3 = 1;
  }
  else
  {
    MyMessageBox("LoadMaps", "%s", &pszErrMsg);
    v3 = 0;
  }
  return v3;
}


