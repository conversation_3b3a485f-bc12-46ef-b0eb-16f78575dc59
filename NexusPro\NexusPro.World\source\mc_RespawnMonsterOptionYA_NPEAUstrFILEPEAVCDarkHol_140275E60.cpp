﻿/*
 * Function: ?mc_RespawnMonsterOption@@YA_NPEAUstrFILE@@PEAVCDarkHoleDungeonQuestSetup@@PEAD@Z
 * Address: 0x140275E60
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// External symbol declarations for decompiled code
extern "C" ULONG_PTR _security_cookie;



bool __fastcall mc_RespawnMonsterOption(strFILE *fstr, CDarkHoleDungeonQuestSetup *pSetup, char *pszoutErrMsg)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  __int64 v6; // [sp+0h] [bp-228h]@1
  char poutszWord; // [sp+30h] [bp-1F8h]@4
  char v8; // [sp+D0h] [bp-158h]@6
  char Source; // [sp+170h] [bp-B8h]@8
  int v10; // [sp+1F4h] [bp-34h]@12
  __respawn_monster *v11; // [sp+1F8h] [bp-30h]@12
  char *v12; // [sp+208h] [bp-20h]@13
  unsigned __int64 v13; // [sp+210h] [bp-18h]@4
  strFILE *fstra; // [sp+230h] [bp+8h]@1
  CDarkHoleDungeonQuestSetup *pSetupa; // [sp+238h] [bp+10h]@1

  pSetupa = pSetup;
  fstra = fstr;
  v3 = &v6;
  for (signed __int64 i = 136; i > 0; --i)
  {
    *(DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v13 = (unsigned __int64)&v6 ^ _security_cookie;
  if ( strFILE::word(fstra, &poutszWord) )
  {
    if ( strFILE::word(fstra, &v8) )
    {
      if ( strFILE::word(fstra, &Source) )
      {
        if ( pSetupa->m_pCurLoadMission->nRespawnMonsterNum )
        {
          v10 = pSetupa->m_pCurLoadMission->nRespawnMonsterNum - 1;
          v11 = pSetupa->m_pCurLoadMission->pRespawnMonster[v10];
          if ( !v11->pszDefineCode )
          {
            v12 = (char *)operator new[](0x21ui64);
            v11->pszDefineCode = v12;
          }
          strcpy_0(v11->pszDefineCode, &Source);
          v11->bCallEvent = 1;
          result = 1;
        }
        else
        {
          result = _false(fstra, pSetupa);
        }
      }
      else
      {
        result = _false(fstra, pSetupa);
      }
    }
    else
    {
      result = _false(fstra, pSetupa);
    }
  }
  else
  {
    result = _false(fstra, pSetupa);
  }
  return result;
}


