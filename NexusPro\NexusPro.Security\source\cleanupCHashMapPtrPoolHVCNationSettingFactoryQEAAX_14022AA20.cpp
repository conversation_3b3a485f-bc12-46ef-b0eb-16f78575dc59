﻿/*
 * Function: ?cleanup@?$CHashMapPtrPool@HVCNationSettingFactory@@@@QEAAXXZ
 * Address: 0x14022AA20
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CHashMapPtrPool<int,CNationSettingFactory>::cleanup(CHashMapPtrPool<int,CNationSettingFactory> *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  std::pair<int const ,CNationSettingFactory *> *v3; // rax@6
  __int64 v4; // [sp+0h] [bp-128h]@1
  stdext::_Hash<stdext::_Hmap_traits<int,CNationSettingFactory *,stdext::hash_compare<int,std::less<int> >,std::allocator<std::pair<int const ,CNationSettingFactory *> >,0> > *v5; // [sp+20h] [bp-108h]@4
  std::list<std::pair<int const ,CNationSettingFactory *>,std::allocator<std::pair<int const ,CNationSettingFactory *> > >::_Const_iterator<0> _Right; // [sp+38h] [bp-F0h]@4
  std::list<std::pair<int const ,CNationSettingFactory *>,std::allocator<std::pair<int const ,CNationSettingFactory *> > >::_Const_iterator<0> v7; // [sp+68h] [bp-C0h]@4
  std::pair<int,CNationSettingFactory *> v8; // [sp+98h] [bp-90h]@6
  std::list<std::pair<int const ,CNationSettingFactory *>,std::allocator<std::pair<int const ,CNationSettingFactory *> > >::_Iterator<0> result; // [sp+B8h] [bp-70h]@4
  std::list<std::pair<int const ,CNationSettingFactory *>,std::allocator<std::pair<int const ,CNationSettingFactory *> > >::_Iterator<0> v10; // [sp+D0h] [bp-58h]@4
  void *v11; // [sp+E8h] [bp-40h]@6
  __int64 v12; // [sp+F0h] [bp-38h]@4
  std::list<std::pair<int const ,CNationSettingFactory *>,std::allocator<std::pair<int const ,CNationSettingFactory *> > >::_Iterator<0> *v13; // [sp+F8h] [bp-30h]@4
  std::list<std::pair<int const ,CNationSettingFactory *>,std::allocator<std::pair<int const ,CNationSettingFactory *> > >::_Const_iterator<0> *__that; // [sp+100h] [bp-28h]@4
  std::list<std::pair<int const ,CNationSettingFactory *>,std::allocator<std::pair<int const ,CNationSettingFactory *> > >::_Iterator<0> *v15; // [sp+108h] [bp-20h]@4
  std::list<std::pair<int const ,CNationSettingFactory *>,std::allocator<std::pair<int const ,CNationSettingFactory *> > >::_Const_iterator<0> *v16; // [sp+110h] [bp-18h]@4
  CHashMapPtrPool<int,CNationSettingFactory> *v17; // [sp+130h] [bp+8h]@1

  v17 = this;
  v1 = &v4;
  for (signed __int64 i = 72; i > 0; --i)
  {
    *(DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v12 = -2i64;
  v5 = (stdext::_Hash<stdext::_Hmap_traits<int,CNationSettingFactory *,stdext::hash_compare<int,std::less<int> >,std::allocator<std::pair<int const ,CNationSettingFactory *> >,0> > *)&v17->m_mapData._Myfirstiter;
  v13 = stdext::_Hash<stdext::_Hmap_traits<int,CNationSettingFactory *,stdext::hash_compare<int,std::less<int>>,std::allocator<std::pair<int const,CNationSettingFactory *>>,0>>::end(
          (stdext::_Hash<stdext::_Hmap_traits<int,CNationSettingFactory *,stdext::hash_compare<int,std::less<int> >,std::allocator<std::pair<int const ,CNationSettingFactory *> >,0> > *)&v17->m_mapData._Myfirstiter,
          &result);
  __that = (std::list<std::pair<int const ,CNationSettingFactory *>,std::allocator<std::pair<int const ,CNationSettingFactory *> > >::_Const_iterator<0> *)v13;
  std::list<std::pair<int const,CNationSettingFactory *>,std::allocator<std::pair<int const,CNationSettingFactory *>>>::_Const_iterator<0>::_Const_iterator<0>(
    &_Right,
    (std::list<std::pair<int const ,CNationSettingFactory *>,std::allocator<std::pair<int const ,CNationSettingFactory *> > >::_Const_iterator<0> *)&v13->_Mycont);
  std::list<std::pair<int const,CNationSettingFactory *>,std::allocator<std::pair<int const,CNationSettingFactory *>>>::_Iterator<0>::~_Iterator<0>(&result);
  v15 = stdext::_Hash<stdext::_Hmap_traits<int,CNationSettingFactory *,stdext::hash_compare<int,std::less<int>>,std::allocator<std::pair<int const,CNationSettingFactory *>>,0>>::begin(
          v5,
          &v10);
  v16 = (std::list<std::pair<int const ,CNationSettingFactory *>,std::allocator<std::pair<int const ,CNationSettingFactory *> > >::_Const_iterator<0> *)v15;
  std::list<std::pair<int const,CNationSettingFactory *>,std::allocator<std::pair<int const,CNationSettingFactory *>>>::_Const_iterator<0>::_Const_iterator<0>(
    &v7,
    (std::list<std::pair<int const ,CNationSettingFactory *>,std::allocator<std::pair<int const ,CNationSettingFactory *> > >::_Const_iterator<0> *)&v15->_Mycont);
  std::list<std::pair<int const,CNationSettingFactory *>,std::allocator<std::pair<int const,CNationSettingFactory *>>>::_Iterator<0>::~_Iterator<0>(&v10);
  while ( std::list<std::pair<int const,CNationSettingFactory *>,std::allocator<std::pair<int const,CNationSettingFactory *>>>::_Const_iterator<0>::operator!=(
            &v7,
            &_Right) )
  {
    v3 = std::list<std::pair<int const,CNationSettingFactory *>,std::allocator<std::pair<int const,CNationSettingFactory *>>>::_Const_iterator<0>::operator*(&v7);
    std::pair<int,CNationSettingFactory *>::pair<int,CNationSettingFactory *>(&v8, v3);
    v11 = v8.second;
    operator delete(v8.second);
    std::list<std::pair<int const,CNationSettingFactory *>,std::allocator<std::pair<int const,CNationSettingFactory *>>>::_Const_iterator<0>::operator++(&v7);
  }
  std::list<std::pair<int const,CNationSettingFactory *>,std::allocator<std::pair<int const,CNationSettingFactory *>>>::_Const_iterator<0>::~_Const_iterator<0>(&v7);
  std::list<std::pair<int const,CNationSettingFactory *>,std::allocator<std::pair<int const,CNationSettingFactory *>>>::_Const_iterator<0>::~_Const_iterator<0>(&_Right);
}


