﻿/*
 * Function: ?Initialize@ClassOrderProcessor@@UEAA_NXZ
 * Address: 0x1402B8250
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// External symbol declarations for decompiled code
extern "C" ULONG_PTR _security_cookie;



char __fastcall ClassOrderProcessor::Initialize(ClassOrderProcessor *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  unsigned int v3; // eax@4
  __int64 v5; // [sp+0h] [bp-168h]@1
  char _Dest[256]; // [sp+40h] [bp-128h]@4
  unsigned __int64 v7; // [sp+150h] [bp-18h]@4
  ClassOrderProcessor *v8; // [sp+170h] [bp+8h]@1

  v8 = this;
  v1 = &v5;
  for (signed __int64 i = 88; i > 0; --i)
  {
    *(DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v7 = (unsigned __int64)&v5 ^ _security_cookie;
  _Dest[0] = 0;
  memset(&_Dest[1], 0, 0xFFui64);
  v3 = GetKorLocalTime();
  sprintf_s<256>((char (*)[256])_Dest, "..\\ZoneServerLog\\SystemLog\\Patriarch\\ClassOrderProcessor_%d.log", v3);
  CLogFile::SetWriteLogFile(&v8->_kSysLog, _Dest, 1, 0, 1, 1);
  ElectProcessor::Initialize((ElectProcessor *)&v8->vfptr);
  CMainThread::PushDQSData(&g_Main, 0xFFFFFFFF, 0i64, 120, 0i64, 0);
  return 1;
}


