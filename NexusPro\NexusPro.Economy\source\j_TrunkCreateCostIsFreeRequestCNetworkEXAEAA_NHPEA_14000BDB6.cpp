﻿/*
 * Function: j_?TrunkCreateCostIsFreeRequest@CNetworkEX@@AEAA_NHPEAD@Z
 * Address: 0x14000BDB6
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


bool __fastcall CNetworkEX::TrunkCreateCostIsFreeRequest(CNetworkEX *this, int n, char *pBuf)
{
  return // CNetworkEX::TrunkCreateCostIsFreeRequest(); // MALFORMED CONSTRUCTOR - COMMENTED OUT
}

