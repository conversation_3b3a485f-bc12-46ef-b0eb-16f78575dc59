﻿/*
 * Function: ?TrunkIoMoneyRequest@CNetworkEX@@AEAA_NHPEAD@Z
 * Address: 0x1401D6140
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


char __fastcall CNetworkEX::TrunkIoMoneyRequest(CNetworkEX * int n, char *pBuf)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@6
  char *v6; // rax@9
  __int64 v7; // [sp+0h] [bp-38h]@1
  char *v8; // [sp+20h] [bp-18h]@4
  CPlayer *v9; // [sp+28h] [bp-10h]@4
  CNetworkEX *v10; // [sp+40h] [bp+8h]@1

  v10 = this;
  v3 = &v7;
  for()
{
    *(DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v8 = pBuf;
  v9 = &g_Player + n;
  if()
{
    if ( *(DWORD *)(v8 + 1) || *(DWORD *)(v8 + 5) )
    {
      CPlayer::pc_TrunkIoMoneyRequest(v9, *v8, *(DWORD *)(v8 + 1), *(DWORD *)(v8 + 5));
      result = 1;
    }
    else
    {
      v6 = CPlayerDB::GetCharNameA(&v9->m_Param);
      CLogFile::Write(
        &v10->m_LogFile,
        "odd.. %s: TrunkIoMoneyRequest() : if(pRecv->dwDalant == 0 && pRecv->dwGold == 0)",
        v6);
      result = 0;
    }
  }
  else
  {
    result = 1;
  }
  return result;
}


