﻿/*
 * Function: ?UpdateTab@CMapTab@@QEAAXXZ
 * Address: 0x14002E6E0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CMapTab::UpdateTab(CMapTab *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // rdx@7
  const char *v4; // rax@7
  __int64 v5; // r9@7
  const char *v6; // rax@7
  __int64 v7; // r9@7
  const char *v8; // rax@7
  __int64 v9; // r9@7
  const char *v10; // rax@7
  const char *v11; // rax@7
  const char *v12; // rax@7
  const char *v13; // rax@7
  const char *v14; // rax@7
  const char *v15; // rax@7
  __int64 v16; // [sp+0h] [bp-B8h]@1
  char *v17; // [sp+20h] [bp-98h]@7
  char v18; // [sp+38h] [bp-80h]@5
  int nIndex; // [sp+44h] [bp-74h]@5
  CMapData *v20; // [sp+48h] [bp-70h]@7
  _bsp_info *v21; // [sp+50h] [bp-68h]@7
  int *v22; // [sp+58h] [bp-60h]@7
  _sec_info *v23; // [sp+60h] [bp-58h]@7
  __int64 v24; // [sp+68h] [bp-50h]@4
  __int64 v25; // [sp+70h] [bp-48h]@7
  __int64 v26; // [sp+78h] [bp-40h]@7
  __int64 v27; // [sp+80h] [bp-38h]@7
  __int64 v28; // [sp+88h] [bp-30h]@7
  __int64 v29; // [sp+90h] [bp-28h]@7
  __int64 v30; // [sp+98h] [bp-20h]@7
  __int64 v31; // [sp+A0h] [bp-18h]@7
  __int64 v32; // [sp+A8h] [bp-10h]@7
  CMapTab *v33; // [sp+C0h] [bp+8h]@1

  v33 = this;
  v1 = &v16;
  for (signed __int64 i = 44; i > 0; --i)
  {
    *(DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v24 = -2i64;
  if ( !CMainThread::IsExcuteService(&g_Main) )
  {
    CTreeCtrl::DeleteAllItems(&v33->m_trMap);
    ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<char>>>::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<char>>>(&v18);
    for ( nIndex = 0; nIndex < dword_141470B98; ++nIndex )
    {
      v20 = CMapOperation::GetMap(&g_MapOper, nIndex);
      v3 = *(QWORD *)(unk_141470BC8 + 1504i64 * nIndex + 744);
      v17 = pszUse[(unsigned __int64)v20->m_bUse];
      ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<char>>>::Format(
        &v18,
        "%dth Map : %s..(%s)",
        (unsigned int)nIndex);
      ((DWORD)(v4) = ATL::CSimpleStringT<char,1>::operator char const *(&v18);
      v33->m_hMap[nIndex] = CTreeCtrl::InsertItem(
                              &v33->m_trMap,
                              v4,
                              (struct _TREEITEM *)0xFFFF0000,
                              (struct _TREEITEM *)0xFFFF0002);
      v21 = CMapData::GetBspInfo(v20);
      v22 = v21->m_nMapMaxSize;
      ((DWORD)(v17) = v21->m_nMapMaxSize[2];
      v5 = v21->m_nMapMaxSize[1];
      ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<char>>>::Format(
        &v18,
        "+Size : [%d][%d][%d]",
        v21->m_nMapMaxSize[0]);
      v25 = nIndex;
      ((DWORD)(v6) = ATL::CSimpleStringT<char,1>::operator char const *(&v18);
      CTreeCtrl::InsertItem(&v33->m_trMap, v6, v33->m_hMap[v25], (struct _TREEITEM *)0xFFFF0002);
      v22 = v21->m_nMapMinSize;
      ((DWORD)(v17) = v21->m_nMapMinSize[2];
      v7 = v21->m_nMapMinSize[1];
      ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<char>>>::Format(
        &v18,
        "-Size : [%d][%d][%d]",
        v21->m_nMapMinSize[0]);
      v26 = nIndex;
      ((DWORD)(v8) = ATL::CSimpleStringT<char,1>::operator char const *(&v18);
      CTreeCtrl::InsertItem(&v33->m_trMap, v8, v33->m_hMap[v26], (struct _TREEITEM *)0xFFFF0002);
      v22 = v21->m_nMapSize;
      ((DWORD)(v17) = v21->m_nMapSize[2];
      v9 = v21->m_nMapSize[1];
      ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<char>>>::Format(
        &v18,
        "MapSize : [%d][%d][%d]",
        v21->m_nMapSize[0]);
      v27 = nIndex;
      ((DWORD)(v10) = ATL::CSimpleStringT<char,1>::operator char const *(&v18);
      CTreeCtrl::InsertItem(&v33->m_trMap, v10, v33->m_hMap[v27], (struct _TREEITEM *)0xFFFF0002);
      ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<char>>>::Format(&v18, "LeafNum : %d", v21->m_nLeafNum);
      v28 = nIndex;
      ((DWORD)(v11) = ATL::CSimpleStringT<char,1>::operator char const *(&v18);
      CTreeCtrl::InsertItem(&v33->m_trMap, v11, v33->m_hMap[v28], (struct _TREEITEM *)0xFFFF0002);
      ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<char>>>::Format(&v18, "Width/Sec : %d", 100i64);
      v29 = nIndex;
      ((DWORD)(v12) = ATL::CSimpleStringT<char,1>::operator char const *(&v18);
      CTreeCtrl::InsertItem(&v33->m_trMap, v12, v33->m_hMap[v29], (struct _TREEITEM *)0xFFFF0002);
      ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<char>>>::Format(&v18, "Height/Sec : %d", 100i64);
      v30 = nIndex;
      ((DWORD)(v13) = ATL::CSimpleStringT<char,1>::operator char const *(&v18);
      CTreeCtrl::InsertItem(&v33->m_trMap, v13, v33->m_hMap[v30], (struct _TREEITEM *)0xFFFF0002);
      v23 = CMapData::GetSecInfo(v20);
      ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<char>>>::Format(
        &v18,
        "WidthSecNum : %d",
        v23->m_nSecNumW);
      v31 = nIndex;
      ((DWORD)(v14) = ATL::CSimpleStringT<char,1>::operator char const *(&v18);
      CTreeCtrl::InsertItem(&v33->m_trMap, v14, v33->m_hMap[v31], (struct _TREEITEM *)0xFFFF0002);
      ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<char>>>::Format(
        &v18,
        "HeightSecNum : %d",
        v23->m_nSecNumH);
      v32 = nIndex;
      ((DWORD)(v15) = ATL::CSimpleStringT<char,1>::operator char const *(&v18);
      CTreeCtrl::InsertItem(&v33->m_trMap, v15, v33->m_hMap[v32], (struct _TREEITEM *)0xFFFF0002);
    }
    ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<char>>>::~CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<char>>>(&v18);
  }
}


