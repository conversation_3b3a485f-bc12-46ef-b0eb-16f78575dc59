﻿/*
 * Function: ??0CMapDisplay@@QEAA@XZ
 * Address: 0x14019D560
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CMapDisplay::CMapDisplay(CMapDisplay *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-98h]@1
  int j; // [sp+80h] [bp-18h]@4
  int k; // [sp+84h] [bp-14h]@9
  __int64 v6; // [sp+88h] [bp-10h]@4
  CMapDisplay *v7; // [sp+A0h] [bp+8h]@1

  v7 = this;
  v1 = &v3;
  for (signed __int64 i = 36; i > 0; --i)
  {
    *(DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v6 = -2i64;
  CDisplay::CDisplay((CDisplay *)&v7->vfptr);
  v7->vfptr = (CDisplayVtbl *)&CMapDisplay::`vftable';
  `eh vector constructor iterator'(
    v7->m_CollLineDraw,
    0x40ui64,
    60,
    (void (__cdecl *)(void *))CCollLineDraw::CCollLineDraw,
    (void (__cdecl *)(void *))CCollLineDraw::~CCollLineDraw);
  CRect::CRect(&v7->m_rcWnd);
  CMyTimer::CMyTimer(&v7->m_tmrDraw);
  CFont::CFont(&v7->m_Font);
  CMapExtend::CMapExtend(&v7->m_MapExtend);
  v7->m_bDisplayMode = 0;
  v7->m_pActMap = 0;
  v7->m_wLayerIndex = 0;
  v7->m_pOldActMap = 0;
  CMyTimer::BeginTimer(&v7->m_tmrDraw, 0x64u);
  for ( j = 0; j < 60; ++j )
  {
    v7->m_DummyDraw[j] = 0;
    v7->m_nDummyDrawNum[j] = 0;
  }
  CDummyDraw::InitPen();
  CCollLineDraw::InitPen();
  v7->m_hPenBorder = CreatePen(0, 2, 0);
  for ( j = 0; j < 2; ++j )
  {
    for ( k = 0; k < 13; ++k )
      v7->m_pSFObj[j][k] = 0;
  }
  v7->m_pSFMap = 0;
  v7->m_pSFSelect = 0;
  v7->m_pSFCircle = 0;
  v7->m_pSFBuf = 0;
  v7->m_pSFCorpse = 0;
  CFont::CreateFontA(&v7->m_Font, 15, 0, 0, 0, 400, 0, 0, 0, 0, 0, 0, 0, 0x20u, "Arial");
  CMapExtend::Init(&v7->m_MapExtend, &v7->m_pSFMap);
}


