﻿/*
 * Function: ??1CMapDisplay@@UEAA@XZ
 * Address: 0x14019D9C0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CMapDisplay::~CMapDisplay(CMapDisplay *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // rax@11
  __int64 v4; // [sp+0h] [bp-68h]@1
  int j; // [sp+20h] [bp-48h]@6
  CDummyDraw *v6; // [sp+28h] [bp-40h]@9
  CDummyDraw *v7; // [sp+30h] [bp-38h]@9
  CDummyDraw *v8; // [sp+38h] [bp-30h]@9
  __int64 v9; // [sp+40h] [bp-28h]@4
  __int64 v10; // [sp+48h] [bp-20h]@11
  __int64 v11; // [sp+50h] [bp-18h]@13
  CMapDisplay *v12; // [sp+70h] [bp+8h]@1

  v12 = this;
  v1 = &v4;
  for (signed __int64 i = 24; i > 0; --i)
  {
    *(DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v9 = -2i64;
  v12->vfptr = (CDisplayVtbl *)&CMapDisplay::`vftable';
  if ( v12->m_bDisplayMode )
    CMapDisplay::ReleaseDisplay(v12);
  CDummyDraw::DeletePen();
  CCollLineDraw::DeletePen();
  DeleteObject(v12->m_hPenBorder);
  CGdiObject::DeleteObject((CGdiObject *)&v12->m_Font.vfptr);
  for ( j = 0; j < 60; ++j )
  {
    if ( v12->m_DummyDraw[j] )
    {
      v8 = v12->m_DummyDraw[j];
      v7 = v8;
      v6 = v8;
      if ( v8 )
      {
        if ( ((DWORD)(v6[-1].m_fScrExt[7]) )
        {
          ((DWORD)(v3) = ((int (__fastcall *)(CDummyDraw *, signed __int64))v7->vfptr->__vecDelDtor)(v7, 3i64);
          v10 = v3;
        }
        else
        {
          operator delete[](&v6[-1].m_fScrExt[7]);
          v10 = 0;
        }
        v11 = v10;
      }
      else
      {
        v11 = 0;
      }
    }
  }
  CMapExtend::~CMapExtend(&v12->m_MapExtend);
  CFont::~CFont(&v12->m_Font);
  CMyTimer::~CMyTimer(&v12->m_tmrDraw);
  `eh vector destructor iterator'(
    v12->m_CollLineDraw,
    0x40ui64,
    60,
    (void (__cdecl *)(void *))CCollLineDraw::~CCollLineDraw);
  CDisplay::~CDisplay((CDisplay *)&v12->vfptr);
}


