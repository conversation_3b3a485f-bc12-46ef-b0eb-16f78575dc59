﻿/*
 * Function: ?CreateSurfaceFromBitmap@CDisplay@@QEAAJPEAPEAVCSurface@@PEADKK@Z
 * Address: 0x140433B00
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


signed __int64 __fastcall CDisplay::CreateSurfaceFromBitmap(CDisplay *this, CSurface **ppSurface, char *strBMP, unsigned int dwDesiredWidth, unsigned int dwDesiredHeight)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 result; // rax@7
  HMODULE v8; // rax@8
  __int64 v9; // rax@12
  __int64 v10; // [sp+0h] [bp-168h]@1
  int cy; // [sp+20h] [bp-148h]@19
  UINT fuLoad; // [sp+28h] [bp-140h]@19
  int v13; // [sp+30h] [bp-138h]@14
  HANDLE h; // [sp+38h] [bp-130h]@4
  char pv; // [sp+48h] [bp-120h]@11
  int v16; // [sp+4Ch] [bp-11Ch]@11
  int v17; // [sp+50h] [bp-118h]@11
  int Dst; // [sp+90h] [bp-D8h]@11
  int v19; // [sp+94h] [bp-D4h]@11
  int v20; // [sp+98h] [bp-D0h]@11
  int v21; // [sp+9Ch] [bp-CCh]@11
  int v22; // [sp+100h] [bp-68h]@11
  CSurface *v23; // [sp+128h] [bp-40h]@14
  CSurface *v24; // [sp+130h] [bp-38h]@11
  CSurface *v25; // [sp+138h] [bp-30h]@15
  CSurface *v26; // [sp+140h] [bp-28h]@15
  __int64 v27; // [sp+148h] [bp-20h]@4
  CSurface *v28; // [sp+150h] [bp-18h]@12
  void *v29; // [sp+158h] [bp-10h]@16
  CDisplay *v30; // [sp+170h] [bp+8h]@1
  CSurface **v31; // [sp+178h] [bp+10h]@1
  char *name; // [sp+180h] [bp+18h]@1
  unsigned int v33; // [sp+188h] [bp+20h]@1

  v33 = dwDesiredWidth;
  name = strBMP;
  v31 = ppSurface;
  v30 = this;
  v5 = &v10;
  for (signed __int64 i = 88; i > 0; --i)
  {
    *(DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v27 = -2i64;
  h = 0;
  if ( v30->m_pDD && strBMP && ppSurface )
  {
    *ppSurface = 0;
    v8 = GetModuleHandleA(0i64);
    h = LoadImageA(v8, name, 0, v33, dwDesiredHeight, 0x2000u);
    if ( h || (h = LoadImageA(0i64, name, 0, v33, dwDesiredHeight, 0x2010u)) != 0i64 )
    {
      GetObjectA(h, 32, &pv);
      memset_0(&Dst, 0, 0x88ui64);
      Dst = 136;
      v19 = 7;
      v22 = 64;
      v21 = v16;
      v20 = v17;
      v24 = (CSurface *)operator new(0x98ui64);
      if ( v24 )
      {
        CSurface::CSurface(v24);
        v28 = (CSurface *)v9;
      }
      else
      {
        v28 = 0;
      }
      v23 = v28;
      *v31 = v28;
      v13 = CSurface::Create(*v31, v30->m_pDD, (_DDSURFACEDESC2 *)&Dst);
      if ( v13 >= 0 )
      {
        fuLoad = 0;
        cy = 0;
        v13 = CSurface::DrawBitmap(*v31, (HBITMAP__ *)h, 0, 0, 0, 0);
        if ( v13 >= 0 )
        {
          DeleteObject(h);
          result = 0;
        }
        else
        {
          DeleteObject(h);
          result = (unsigned int)v13;
        }
      }
      else
      {
        v26 = *v31;
        v25 = v26;
        if ( v26 )
          v29 = CSurface::`scalar deleting destructor'(v25, 1u);
        else
          v29 = 0;
        result = (unsigned int)v13;
      }
    }
    else
    {
      result = 2147500037i64;
    }
  }
  else
  {
    result = 2147942487i64;
  }
  return result;
}


