﻿/*
 * Function: ?LoadStateData@CHolyStoneSystemDataMgr@@SA_NAEAVCHolyStoneSaveData@@@Z
 * Address: 0x1402861A0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// External symbol declarations for decompiled code
extern "C" ULONG_PTR _security_cookie;



char __fastcall CHolyStoneSystemDataMgr::LoadStateData(CHolyStoneSaveData *clsSaveDummy)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  INT v4; // eax@19
  INT v5; // eax@19
  INT v6; // eax@19
  INT v7; // eax@19
  INT v8; // eax@19
  __int64 v9; // [sp+0h] [bp-88h]@1
  unsigned int j; // [sp+20h] [bp-68h]@8
  char Dest; // [sp+38h] [bp-50h]@10
  UINT v12; // [sp+64h] [bp-24h]@19
  unsigned __int64 v13; // [sp+70h] [bp-18h]@4
  CHolyStoneSaveData *v14; // [sp+90h] [bp+8h]@1

  v14 = clsSaveDummy;
  v1 = &v9;
  for ( i = 32; i; --i )
  {
    *(DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v13 = (unsigned __int64)&v9 ^ _security_cookie;
  v14->m_nSceneCode = GetPrivateProfileIntA("HSK", "scene", -1, "..\\SystemSave\\ServerState.ini");
  if ( v14->m_nSceneCode == -1 )
  {
    v14->m_nSceneCode = 0;
    CLogFile::Write(&stru_1799C8F30, "CHolyStoneSystemDataeMgr::LoadStateData() >> m_nSceneCode fail");
    result = 0;
  }
  else
  {
    v14->m_dwPassTimeInScene = GetPrivateProfileIntA("HSK", "pass", -1, "..\\SystemSave\\ServerState.ini");
    if ( v14->m_dwPassTimeInScene == -1 )
    {
      CLogFile::Write(&stru_1799C8F30, "CHolyStoneSystemDataeMgr::LoadStateData() >> m_dwPassTimeInScene fail");
      result = 0;
    }
    else
    {
      for ( j = 0; (signed int)j < 3; ++j )
      {
        sprintf(&Dest, "%d S_hp", j);
        v14->m_nStoneHP_Buffer[j] = GetPrivateProfileIntA("HSK", &Dest, -1, "..\\SystemSave\\ServerState.ini");
      }
      v14->m_nStartStoneHP = GetPrivateProfileIntA("HSK", "starthp", 1000000, "..\\SystemSave\\ServerState.ini");
      v14->m_nHolyMasterRace = GetPrivateProfileIntA("HSK", "master", 100, "..\\SystemSave\\ServerState.ini");
      if ( v14->m_nHolyMasterRace == 100 )
      {
        CLogFile::Write(&stru_1799C8F30, "CHolyStoneSystemDataeMgr::LoadStateData() >> m_nHolyMasterRace fail");
        result = 0;
      }
      else
      {
        v14->m_nDestroyStoneRace = GetPrivateProfileIntA("HSK", "destroyedrace", 100, "..\\SystemSave\\ServerState.ini");
        if ( v14->m_nDestroyStoneRace == 100 )
        {
          CLogFile::Write(&stru_1799C8F30, "CHolyStoneSystemDataeMgr::LoadStateData() >> m_byDestroyStoneRace fail");
          result = 0;
        }
        else
        {
          v14->m_dwTerm[0] = GetPrivateProfileIntA("HSK", "plustime", -1, "..\\SystemSave\\ServerState.ini");
          if ( v14->m_dwTerm[0] == -1 )
          {
            CLogFile::Write(&stru_1799C8F30, "CHolyStoneSystemDataeMgr::LoadStateData() >> m_dwKeeperPlusTime fail");
            result = 0;
          }
          else
          {
            v14->m_dwTerm[1] = GetPrivateProfileIntA("HSK", "controlterm", 0, "..\\SystemSave\\ServerState.ini");
            if ( v14->m_dwTerm[1] )
            {
              v14->m_byNumOfTime = GetPrivateProfileIntA("HSK", "numoftime", 255, "..\\SystemSave\\ServerState.ini");
              v14->m_dwCumPlayerNum = GetPrivateProfileIntA("HSK", "cumplayernum", 0, "..\\SystemSave\\ServerState.ini");
              v14->m_dwCumCount = GetPrivateProfileIntA("HSK", "cumcount", 0, "..\\SystemSave\\ServerState.ini");
              v4 = GetCurrentYear();
              v14->m_wStartYear = GetPrivateProfileIntA("HSK", "startyear", v4, "..\\SystemSave\\ServerState.ini");
              v5 = GetCurrentMonth();
              v14->m_byStartMonth = GetPrivateProfileIntA("HSK", "startmonth", v5, "..\\SystemSave\\ServerState.ini");
              v6 = GetCurrentDay();
              v14->m_byStartDay = GetPrivateProfileIntA("HSK", "startday", v6, "..\\SystemSave\\ServerState.ini");
              v7 = GetCurrentHour();
              v14->m_byStartHour = GetPrivateProfileIntA("HSK", "starthour", v7, "..\\SystemSave\\ServerState.ini");
              v8 = GetCurrentMin();
              v14->m_byStartMin = GetPrivateProfileIntA("HSK", "startmin", v8, "..\\SystemSave\\ServerState.ini");
              v12 = GetPrivateProfileIntA("HSK", "destroyer", -1, "..\\SystemSave\\ServerState.ini");
              if ( v12 != -1 )
                v14->m_dwDestroyerSerial = v12;
              v12 = GetPrivateProfileIntA("HSK", "destroyerstate", 3, "..\\SystemSave\\ServerState.ini");
              if ( (v12 & 0x80000000) == 0 && (signed int)v12 < 3 )
                v14->m_eDestroyerState = v12;
              v14->m_dwOreRemainAmount = GetPrivateProfileIntA(
                                           "HSK",
                                           "oreremain",
                                           -1,
                                           "..\\SystemSave\\ServerState.ini");
              v14->m_dwOreTotalAmount = GetPrivateProfileIntA("HSK", "oretotal", -1, "..\\SystemSave\\ServerState.ini");
              v14->m_dwDestroyerGuildSerial = GetPrivateProfileIntA(
                                                "HSK",
                                                "destroyerguild",
                                                -1,
                                                "..\\SystemSave\\ServerState.ini");
              v14->m_byOreTransferCount = GetPrivateProfileIntA(
                                            "HSK",
                                            "oretransfercount",
                                            0,
                                            "..\\SystemSave\\ServerState.ini");
              v14->m_dwOreTransferAmount = GetPrivateProfileIntA(
                                             "HSK",
                                             "oretransferamount",
                                             0,
                                             "..\\SystemSave\\ServerState.ini");
              result = 1;
            }
            else
            {
              CLogFile::Write(&stru_1799C8F30, "CHolyStoneSystemDataeMgr::LoadStateData() >> m_dwChaosTerm fail");
              result = 0;
            }
          }
        }
      }
    }
  }
  return result;
}


