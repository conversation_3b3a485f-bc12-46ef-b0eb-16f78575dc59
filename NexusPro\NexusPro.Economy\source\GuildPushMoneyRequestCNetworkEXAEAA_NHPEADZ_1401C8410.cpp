﻿/*
 * Function: ?GuildPushMoneyRequest@CNetworkEX@@AEAA_NHPEAD@Z
 * Address: 0x1401C8410
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


char __fastcall CNetworkEX::GuildPushMoneyRequest(CNetworkEX * int n, char *pBuf)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v6; // [sp+0h] [bp-38h]@1
  char *v7; // [sp+20h] [bp-18h]@4
  CPlayer *v8; // [sp+28h] [bp-10h]@4

  v3 = &v6;
  for()
{
    *(DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v7 = pBuf;
  v8 = &g_Player + n;
  if()
{
    CPlayer::pc_GuildPushMoneyRequest(v8, *((DWORD *)v7 + 1), *(DWORD *)v7);
    result = 1;
  }
  else
  {
    result = 1;
  }
  return result;
}


