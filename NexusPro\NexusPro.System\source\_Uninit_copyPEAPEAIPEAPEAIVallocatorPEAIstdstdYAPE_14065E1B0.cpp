﻿/*
 * Function: ??$_Uninit_copy@PEAPEAIPEAPEAIV?$allocator@PEAI@std@@@std@@YAPEAPEAIPEAPEAI00AEAV?$allocator@PEAI@0@U_Scalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x14065E1B0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


signed __int64 __fastcall std::_Uninit_copy<unsigned int * *,unsigned int * *,std::allocator<unsigned int *>>(const void *a1, __int64 a2, char *a3)
{
  __int64 v4; // [sp+20h] [bp-18h]@1
  signed __int64 v5; // [sp+28h] [bp-10h]@1

  v4 = (a2 - (signed __int64)a1) >> 3;
  v5 = (signed __int64)&a3[8 * v4];
  if ( v4 )
    memmove_s(a3, 8 * v4, a1, 8 * v4);
  return v5;
}

