﻿/*
 * Function: ?dtor_0@?0??AddRunnableContext@ScheduleGroupSegmentBase@details@Concurrency@@IEAAXPEAVInternalContextBase@23@Vlocation@3@@Z@4HA_2
 * Address: 0x1405FA460
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Required includes for Concurrency namespace
#include <ppl.h>
#include <ppltasks.h>
#include <concurrent_vector.h>



void __fastcallConcurrency::details::ScheduleGroupSegmentBase::AddRunnableContext'::1'::dtor_0(__int64 a1, __int64 a2)
{
  CryptoPP::Filter::~Filter(*(CryptoPP::Filter **)(a2 + 192));
}

