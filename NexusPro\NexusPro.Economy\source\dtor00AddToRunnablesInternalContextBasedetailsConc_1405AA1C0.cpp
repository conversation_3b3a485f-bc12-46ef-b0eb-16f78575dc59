﻿/*
 * Function: ?dtor_0@?0??AddToRunnables@InternalContextBase@details@Concurrency@@MEAAXVlocation@3@@Z@4HA_5
 * Address: 0x1405AA1C0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Required includes for Concurrency namespace
#include <ppl.h>
#include <ppltasks.h>
#include <concurrent_vector.h>



void __fastcallConcurrency::details::InternalContextBase::AddToRunnables'::1'::dtor_0(__int64 a1, __int64 a2)
{
  CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer> ::~BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>(*(QWORD *)(a2 + 264));
}


