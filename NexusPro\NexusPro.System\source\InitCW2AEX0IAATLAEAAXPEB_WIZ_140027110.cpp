﻿/*
 * Function: ?Init@?$CW2AEX@$0IA@@ATL@@AEAAXPEB_WI@Z
 * Address: 0x140027110
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall ATL::CW2AEX<128>::Init(ATL::CW2AEX<128> *this, const wchar_t *psz, unsigned int nConvertCodePage)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-68h]@1
  LPSTR lpMultiByteStr; // [sp+20h] [bp-48h]@6
  int cbMultiByte; // [sp+28h] [bp-40h]@6
  LPCSTR lpDefaultChar; // [sp+30h] [bp-38h]@6
  LPBOOL lpUsedDefaultChar; // [sp+38h] [bp-30h]@6
  int cchWideChar; // [sp+40h] [bp-28h]@6
  int nLength; // [sp+44h] [bp-24h]@6
  int v12; // [sp+48h] [bp-20h]@6
  int v13; // [sp+4Ch] [bp-1Ch]@6
  int v14; // [sp+50h] [bp-18h]@8
  ATL::CW2AEX<128> *ppBuff; // [sp+70h] [bp+8h]@1
  LPCWSTR lpString; // [sp+78h] [bp+10h]@1
  unsigned int CodePage; // [sp+80h] [bp+18h]@1

  CodePage = nConvertCodePage;
  lpString = psz;
  ppBuff = this;
  v3 = &v5;
  for (signed __int64 i = 24; i > 0; --i)
  {
    *(DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( psz )
  {
    cchWideChar = lstrlenW(psz) + 1;
    nLength = 4 * cchWideChar;
    ATL::AtlConvAllocMemory<char>(&ppBuff->m_psz, 4 * cchWideChar, ppBuff->m_szBuffer, 128);
    lpUsedDefaultChar = 0;
    lpDefaultChar = 0;
    cbMultiByte = nLength;
    lpMultiByteStr = ppBuff->m_psz;
    v13 = WideCharToMultiByte(CodePage, 0, lpString, cchWideChar, lpMultiByteStr, nLength, 0i64, 0i64) == 0;
    v12 = v13;
    if ( v13 && GetLastError() == 122 )
    {
      nLength = WideCharToMultiByte(CodePage, 0, lpString, cchWideChar, 0i64, 0, 0i64, 0i64);
      ATL::AtlConvAllocMemory<char>(&ppBuff->m_psz, nLength, ppBuff->m_szBuffer, 128);
      lpUsedDefaultChar = 0;
      lpDefaultChar = 0;
      cbMultiByte = nLength;
      lpMultiByteStr = ppBuff->m_psz;
      v14 = WideCharToMultiByte(CodePage, 0, lpString, cchWideChar, lpMultiByteStr, nLength, 0i64, 0i64) == 0;
      v12 = v14;
    }
    if ( v12 )
      ATL::AtlThrowLastWin32();
  }
  else
  {
    ppBuff->m_psz = 0;
  }
}


