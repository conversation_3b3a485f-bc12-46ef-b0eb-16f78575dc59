﻿/*
 * Function: ??$_Uninit_fill_n@PEAPEAVCRaceBuffInfoByHolyQuestfGroup@@_KPEAV1@V?$allocator@PEAVCRaceBuffInfoByHolyQuestfGroup@@@std@@@std@@YAXPEAPEAVCRaceBuffInfoByHolyQuestfGroup@@_KAEBQEAV1@AEAV?$allocator@PEAVCRaceBuffInfoByHolyQuestfGroup@@@0@U_Scalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x1403BBF20
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall std::_Uninit_fill_n<CRaceBuffInfoByHolyQuestfGroup * *,unsigned __int64,CRaceBuffInfoByHolyQuestfGroup *,std::allocator<CRaceBuffInfoByHolyQuestfGroup *>>(CRaceBuffInfoByHolyQuestfGroup **_First, unsigned __int64 _Count, CRaceBuffInfoByHolyQuestfGroup *const *_Val, std::allocator<CRaceBuffInfoByHolyQuestfGroup *> *__formal, std::_Scalar_ptr_iterator_tag a5, std::_Range_checked_iterator_tag a6)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v8; // [sp+0h] [bp-28h]@1
  CRaceBuffInfoByHolyQuestfGroup **_Firsta; // [sp+30h] [bp+8h]@1

  _Firsta = _First;
  v6 = &v8;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  stdext::unchecked_fill_n<CRaceBuffInfoByHolyQuestfGroup * *,unsigned __int64,CRaceBuffInfoByHolyQuestfGroup *>(
    _Firsta,
    _Count,
    _Val);
}

