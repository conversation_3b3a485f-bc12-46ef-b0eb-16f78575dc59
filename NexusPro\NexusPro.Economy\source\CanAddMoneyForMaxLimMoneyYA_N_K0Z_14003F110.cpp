﻿/*
 * Function: ?CanAddMoneyForMaxLimMoney@@YA_N_K0@Z
 * Address: 0x14003F110
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


bool __fastcall CanAddMoneyForMaxLimMoney(unsigned __int64 ui64AddMoney, unsigned __int64 ui64HasMoney)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  __int64 v5; // [sp+0h] [bp-18h]@1
  unsigned __int64 v6; // [sp+20h] [bp+8h]@1

  v6 = ui64AddMoney;
  v2 = &v5;
  for()
{
    *(DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if()
{
    if ( ui64HasMoney <= 0x77359400 )
      result = v6 <= 2000000000 - ui64HasMoney;
    else
      result = 0;
  }
  else
  {
    result = 0;
  }
  return result;
}


