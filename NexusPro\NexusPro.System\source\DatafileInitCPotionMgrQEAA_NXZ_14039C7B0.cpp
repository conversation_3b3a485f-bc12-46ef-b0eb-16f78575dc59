﻿/*
 * Function: ?DatafileInit@CPotionMgr@@QEAA_NXZ
 * Address: 0x14039C7B0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// External symbol declarations
extern "C" IMAGE_DOS_HEADER __ImageBase;
extern "C" DWORD off_14000003C;
extern "C" void* _delayLoadHelper2(ImgDelayDescr* pidd, void** ppfnIATEntry);
extern struct EqSukData { void* pwszEpSuk; } EqSukList[16];
extern void MyMessageBox(const char* title, const char* message);


char __fastcall CPotionMgr::DatafileInit(CPotionMgr *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v4; // [sp+0h] [bp-D8h]@1
  char pszErrMsg; // [sp+30h] [bp-A8h]@4
  unsigned __int64 v6; // [sp+C0h] [bp-18h]@4
  CPotionMgr *v7; // [sp+E0h] [bp+8h]@1

  v7 = this;
  v1 = &v4;
  for (signed __int64 i = 52; i > 0; --i)
  {
    *(DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v6 = (unsigned __int64)&v4 ^ _security_cookie;
  if ( CRecordData::ReadRecord(&v7->m_tblPotionEffectData, ".\\script\\PotionItemEffect.dat", 0x490u, &pszErrMsg) )
  {
    if ( CRecordData::ReadRecord(&v7->m_tblPotionCheckData, ".\\script\\CheckPotionEffect.dat", 0xDCu, &pszErrMsg) )
    {
      result = 1;
    }
    else
    {
      MyMessageBox("DatafileInit", &pszErrMsg);
      result = 0;
    }
  }
  else
  {
    MyMessageBox("DatafileInit", &pszErrMsg);
    result = 0;
  }
  return result;
}


