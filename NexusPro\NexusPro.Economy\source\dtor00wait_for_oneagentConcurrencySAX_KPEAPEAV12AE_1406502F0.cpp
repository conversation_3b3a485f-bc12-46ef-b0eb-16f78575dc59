﻿/*
 * Function: ?dtor_0@?0??wait_for_one@agent@Concurrency@@SAX_KPEAPEAV12@AEAW4agent_status@2@AEA_KI@Z@4HA_1
 * Address: 0x1406502F0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Required includes for Concurrency namespace
#include <ppl.h>
#include <ppltasks.h>
#include <concurrent_vector.h>



intConcurrency::agent::wait_for_one'::1'::dtor_0()
{
  return std::_Vb_reference<std::vector<bool,std::allocator<bool> >>::~_Vb_reference<std::vector<bool,std::allocator<bool>>>();
}

