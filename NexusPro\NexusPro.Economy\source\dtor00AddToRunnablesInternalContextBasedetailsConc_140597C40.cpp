﻿/*
 * Function: ?dtor_0@?0??AddToRunnables@InternalContextBase@details@Concurrency@@MEAAXVlocation@3@@Z@4HA
 * Address: 0x140597C40
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Required includes for Concurrency namespace
#include <ppl.h>
#include <ppltasks.h>
#include <concurrent_vector.h>



int __fastcallConcurrency::details::InternalContextBase::AddToRunnables'::1'::dtor_0(__int64 a1, __int64 a2)
{
  __int64 v2; // rcx@1

  v2 = *(QWORD *)(a2 + 264);
  return std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer> ,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>::~_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>();
}


