﻿/*
 * Function: ?regist@?$CHashMapPtrPool@HVCNationSettingFactory@@@@QEAAHPEAVCNationSettingFactory@@@Z
 * Address: 0x14021ACE0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


signed __int64 __fastcall CHashMapPtrPool<int,CNationSettingFactory>::regist(CHashMapPtrPool<int,CNationSettingFactory> *this, CNationSettingFactory *pData)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 v4; // rax@6
  int v5; // eax@9
  std::pair<int,CNationSettingFactory *> *v6; // rax@9
  __int64 v7; // [sp+0h] [bp-D8h]@1
  std::list<std::pair<int const ,CNationSettingFactory *>,std::allocator<std::pair<int const ,CNationSettingFactory *> > >::_Iterator<0> result; // [sp+28h] [bp-B0h]@7
  int _Keyval; // [sp+44h] [bp-94h]@7
  bool v10; // [sp+48h] [bp-90h]@7
  std::list<std::pair<int const ,CNationSettingFactory *>,std::allocator<std::pair<int const ,CNationSettingFactory *> > >::_Iterator<0> v11; // [sp+50h] [bp-88h]@7
  unsigned int v12; // [sp+68h] [bp-70h]@8
  std::pair<int const ,CNationSettingFactory *> _Val; // [sp+70h] [bp-68h]@9
  std::pair<int,CNationSettingFactory *> v14; // [sp+80h] [bp-58h]@9
  std::pair<std::list<std::pair<int const ,CNationSettingFactory *>,std::allocator<std::pair<int const ,CNationSettingFactory *> > >::_Iterator<0>,bool> v15; // [sp+90h] [bp-48h]@9
  unsigned int v16; // [sp+B0h] [bp-28h]@9
  __int64 v17; // [sp+B8h] [bp-20h]@4
  std::list<std::pair<int const ,CNationSettingFactory *>,std::allocator<std::pair<int const ,CNationSettingFactory *> > >::_Iterator<0> *v18; // [sp+C0h] [bp-18h]@7
  std::list<std::pair<int const ,CNationSettingFactory *>,std::allocator<std::pair<int const ,CNationSettingFactory *> > >::_Const_iterator<0> *v19; // [sp+C8h] [bp-10h]@7
  CHashMapPtrPool<int,CNationSettingFactory> *v20; // [sp+E0h] [bp+8h]@1
  CNationSettingFactory *_Val2; // [sp+E8h] [bp+10h]@1

  _Val2 = pData;
  v20 = this;
  v2 = &v7;
  for (signed __int64 i = 52; i > 0; --i)
  {
    *(DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v17 = -2i64;
  if ( pData && !CNationSettingFactory::IsNULL(pData) )
  {
    _Keyval = CNationSettingFactory::GetKey(_Val2);
    stdext::_Hash<stdext::_Hmap_traits<int,CNationSettingFactory *,stdext::hash_compare<int,std::less<int>>,std::allocator<std::pair<int const,CNationSettingFactory *>>,0>>::find(
      (stdext::_Hash<stdext::_Hmap_traits<int,CNationSettingFactory *,stdext::hash_compare<int,std::less<int> >,std::allocator<std::pair<int const ,CNationSettingFactory *> >,0> > *)&v20->m_mapData._Myfirstiter,
      &result,
      &_Keyval);
    v18 = stdext::_Hash<stdext::_Hmap_traits<int,CNationSettingFactory *,stdext::hash_compare<int,std::less<int>>,std::allocator<std::pair<int const,CNationSettingFactory *>>,0>>::end(
            (stdext::_Hash<stdext::_Hmap_traits<int,CNationSettingFactory *,stdext::hash_compare<int,std::less<int> >,std::allocator<std::pair<int const ,CNationSettingFactory *> >,0> > *)&v20->m_mapData._Myfirstiter,
            &v11);
    v19 = (std::list<std::pair<int const ,CNationSettingFactory *>,std::allocator<std::pair<int const ,CNationSettingFactory *> > >::_Const_iterator<0> *)v18;
    v10 = std::list<std::pair<int const,CNationSettingFactory *>,std::allocator<std::pair<int const,CNationSettingFactory *>>>::_Const_iterator<0>::operator!=(
            (std::list<std::pair<int const ,CNationSettingFactory *>,std::allocator<std::pair<int const ,CNationSettingFactory *> > >::_Const_iterator<0> *)&v18->_Mycont,
            (std::list<std::pair<int const ,CNationSettingFactory *>,std::allocator<std::pair<int const ,CNationSettingFactory *> > >::_Const_iterator<0> *)&result._Mycont);
    std::list<std::pair<int const,CNationSettingFactory *>,std::allocator<std::pair<int const,CNationSettingFactory *>>>::_Iterator<0>::~_Iterator<0>(&v11);
    if ( v10 )
    {
      v12 = -2;
      std::list<std::pair<int const,CNationSettingFactory *>,std::allocator<std::pair<int const,CNationSettingFactory *>>>::_Iterator<0>::~_Iterator<0>(&result);
      v4 = v12;
    }
    else
    {
      v5 = CNationSettingFactory::GetKey(_Val2);
      v6 = std::make_pair<int,CNationSettingFactory *>(&v14, v5, _Val2);
      std::pair<int const,CNationSettingFactory *>::pair<int const,CNationSettingFactory *>(&_Val, v6);
      stdext::_Hash<stdext::_Hmap_traits<int,CNationSettingFactory *,stdext::hash_compare<int,std::less<int>>,std::allocator<std::pair<int const,CNationSettingFactory *>>,0>>::insert(
        (stdext::_Hash<stdext::_Hmap_traits<int,CNationSettingFactory *,stdext::hash_compare<int,std::less<int> >,std::allocator<std::pair<int const ,CNationSettingFactory *> >,0> > *)&v20->m_mapData._Myfirstiter,
        &v15,
        &_Val);
      std::pair<std::list<std::pair<int const,CNationSettingFactory *>,std::allocator<std::pair<int const,CNationSettingFactory *>>>::_Iterator<0>,bool>::~pair<std::list<std::pair<int const,CNationSettingFactory *>,std::allocator<std::pair<int const,CNationSettingFactory *>>>::_Iterator<0>,bool>(&v15);
      v16 = 0;
      std::list<std::pair<int const,CNationSettingFactory *>,std::allocator<std::pair<int const,CNationSettingFactory *>>>::_Iterator<0>::~_Iterator<0>(&result);
      v4 = v16;
    }
  }
  else
  {
    v4 = 0xFFFFFFFFi64;
  }
  return v4;
}


