﻿/*
 * Function: ?MakeSystemTower@CMainThread@@AEAAXXZ
 * Address: 0x1401EE040
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// External symbol declarations for decompiled code
extern "C" ULONG_PTR _security_cookie;



void __fastcall CMainThread::MakeSystemTower(CMainThread *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  float v3; // xmm0_4@14
  float v4; // xmm0_4@16
  float v5; // xmm0_4@18
  __int64 v6; // [sp+0h] [bp-3E8h]@1
  DWORD nSize[2]; // [sp+20h] [bp-3C8h]@9
  LPCSTR lpFileName; // [sp+28h] [bp-3C0h]@9
  int v9; // [sp+30h] [bp-3B8h]@19
  char KeyName; // [sp+50h] [bp-398h]@11
  char ReturnedString; // [sp+F0h] [bp-2F8h]@9
  float fPos; // [sp+188h] [bp-260h]@14
  float v13; // [sp+18Ch] [bp-25Ch]@16
  float v14; // [sp+190h] [bp-258h]@18
  char szRecordCode; // [sp+1C0h] [bp-228h]@20
  LPCSTR lpAppName; // [sp+258h] [bp-190h]@4
  const char *v17; // [sp+260h] [bp-188h]@4
  const char *v18; // [sp+268h] [bp-180h]@4
  unsigned int j; // [sp+274h] [bp-174h]@4
  int nIniIndex; // [sp+278h] [bp-170h]@6
  char Dest; // [sp+290h] [bp-158h]@9
  CMapData *pMap; // [sp+318h] [bp-D0h]@10
  char Str1; // [sp+330h] [bp-B8h]@12
  _base_fld *v24; // [sp+3B8h] [bp-30h]@22
  CLogFile *v25; // [sp+3C8h] [bp-20h]@19
  unsigned __int64 v26; // [sp+3D0h] [bp-18h]@4
  CMainThread *v27; // [sp+3F0h] [bp+8h]@1

  v27 = this;
  v1 = &v6;
  for (signed __int64 i = 248; i > 0; --i)
  {
    *(DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v26 = (unsigned __int64)&v6 ^ _security_cookie;
  lpAppName = "BELLATO";
  v17 = "CORA";
  v18 = "ACCRETIA";
  for ( j = 0; (signed int)j < 3; ++j )
  {
    for ( nIniIndex = 0; nIniIndex < 50; ++nIniIndex )
    {
      sprintf(&Dest, "Map%d", (unsigned int)nIniIndex);
      lpFileName = ".\\Script\\SystemGuardTower.ini";
      nSize[0] = 128;
      GetPrivateProfileStringA(
        (&lpAppName)[8 * (signed int)j],
        &Dest,
        "NULL",
        &ReturnedString,
        0x80u,
        ".\\Script\\SystemGuardTower.ini");
      if ( strcmp_0(&ReturnedString, "NULL") )
      {
        pMap = CMapOperation::GetMap(&g_MapOper, &ReturnedString);
        if ( pMap )
        {
          sprintf(&KeyName, "Pos%d_x", (unsigned int)nIniIndex);
          lpFileName = ".\\Script\\SystemGuardTower.ini";
          nSize[0] = 128;
          GetPrivateProfileStringA(
            (&lpAppName)[8 * (signed int)j],
            &KeyName,
            "NULL",
            &Str1,
            0x80u,
            ".\\Script\\SystemGuardTower.ini");
          if ( !strcmp_0(&Str1, "NULL") )
          {
            WritePrivateProfileStringA(
              (&lpAppName)[8 * (signed int)j],
              &Dest,
              "NULL",
              "..\\Script\\SystemGuardTower.ini");
            *(QWORD *)nSize = &Str1;
            CLogFile::Write(
              &v27->m_logLoadingError,
              "systower >> race(%d), index(%d) : À§Ä¡(x) ¹®ÀÚ ¿À·ù(%s)",
              j,
              (unsigned int)nIniIndex);
          }
          else
          {
            v3 = atof(&Str1);
            fPos = v3;
            sprintf(&KeyName, "Pos%d_y", (unsigned int)nIniIndex);
            lpFileName = ".\\Script\\SystemGuardTower.ini";
            nSize[0] = 128;
            GetPrivateProfileStringA(
              (&lpAppName)[8 * (signed int)j],
              &KeyName,
              "NULL",
              &Str1,
              0x80u,
              ".\\Script\\SystemGuardTower.ini");
            if ( !strcmp_0(&Str1, "NULL") )
            {
              WritePrivateProfileStringA(
                (&lpAppName)[8 * (signed int)j],
                &Dest,
                "NULL",
                ".\\Script\\SystemGuardTower.ini");
              *(QWORD *)nSize = &Str1;
              CLogFile::Write(
                &v27->m_logLoadingError,
                "systower >> race(%d), index(%d) : À§Ä¡(y) ¹®ÀÚ ¿À·ù(%s)",
                j,
                (unsigned int)nIniIndex);
            }
            else
            {
              v4 = atof(&Str1);
              v13 = v4;
              sprintf(&KeyName, "Pos%d_z", (unsigned int)nIniIndex);
              lpFileName = ".\\Script\\SystemGuardTower.ini";
              nSize[0] = 128;
              GetPrivateProfileStringA(
                (&lpAppName)[8 * (signed int)j],
                &KeyName,
                "NULL",
                &Str1,
                0x80u,
                ".\\Script\\SystemGuardTower.ini");
              if ( !strcmp_0(&Str1, "NULL") )
              {
                WritePrivateProfileStringA(
                  (&lpAppName)[8 * (signed int)j],
                  &Dest,
                  "NULL",
                  ".\\Script\\SystemGuardTower.ini");
                *(QWORD *)nSize = &Str1;
                CLogFile::Write(
                  &v27->m_logLoadingError,
                  "systower >> race(%d), index(%d) : À§Ä¡(z) ¹®ÀÚ ¿À·ù(%s)",
                  j,
                  (unsigned int)nIniIndex);
              }
              else
              {
                v5 = atof(&Str1);
                v14 = v5;
                if ( CMapData::IsMapIn(pMap, &fPos) )
                {
                  sprintf(&KeyName, "Code%d", (unsigned int)nIniIndex);
                  lpFileName = ".\\Script\\SystemGuardTower.ini";
                  nSize[0] = 128;
                  GetPrivateProfileStringA(
                    (&lpAppName)[8 * (signed int)j],
                    &KeyName,
                    "NULL",
                    &szRecordCode,
                    0x80u,
                    ".\\Script\\SystemGuardTower.ini");
                  if ( !strcmp_0(&szRecordCode, "NULL") )
                  {
                    WritePrivateProfileStringA(
                      (&lpAppName)[8 * (signed int)j],
                      &Dest,
                      "NULL",
                      ".\\Script\\SystemGuardTower.ini");
                    *(QWORD *)nSize = &szRecordCode;
                    CLogFile::Write(
                      &v27->m_logLoadingError,
                      "systower >> race(%d), index(%d) : Å¸¿öÄÚµå ¹®ÀÚ ¿À·ù(%s)",
                      j,
                      (unsigned int)nIniIndex);
                  }
                  else
                  {
                    v24 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + 25, &szRecordCode);
                    if ( v24 )
                    {
                      ((DWORD)(lpFileName) = nIniIndex;
                      LOBYTE(nSize[0]) = j;
                      if ( !CreateSystemTower(pMap, 0, &fPos, v24->m_dwIndex, j, nIniIndex) )
                      {
                        WritePrivateProfileStringA(
                          (&lpAppName)[8 * (signed int)j],
                          &Dest,
                          "NULL",
                          ".\\Script\\SystemGuardTower.ini");
                        CLogFile::Write(
                          &v27->m_logLoadingError,
                          "systower >> race(%d), index(%d) : Å¸¿öÁþ±â ½ÇÆÐ",
                          j,
                          (unsigned int)nIniIndex);
                      }
                    }
                    else
                    {
                      WritePrivateProfileStringA(
                        (&lpAppName)[8 * (signed int)j],
                        &Dest,
                        "NULL",
                        ".\\Script\\SystemGuardTower.ini");
                      CLogFile::Write(
                        &v27->m_logLoadingError,
                        "systower >> race(%d), index(%d) : ¾ø´Â Å¸¿öÄÚµå",
                        j,
                        (unsigned int)nIniIndex);
                    }
                  }
                }
                else
                {
                  WritePrivateProfileStringA(
                    (&lpAppName)[8 * (signed int)j],
                    &Dest,
                    "NULL",
                    ".\\Script\\SystemGuardTower.ini");
                  v25 = &v27->m_logLoadingError;
                  v9 = (signed int)ffloor(v14);
                  ((DWORD)(lpFileName) = (signed int)ffloor(v13);
                  nSize[0] = (signed int)ffloor(fPos);
                  CLogFile::Write(
                    &v27->m_logLoadingError,
                    "systower >> race(%d), index(%d) : À§Ä¡¿À·ù(%d, %d, %d)",
                    j,
                    (unsigned int)nIniIndex);
                }
              }
            }
          }
        }
        else
        {
          WritePrivateProfileStringA(
            (&lpAppName)[8 * (signed int)j],
            &KeyName,
            "NULL",
            ".\\Script\\SystemGuardTower.ini");
          CLogFile::Write(
            &v27->m_logLoadingError,
            "systower >> race(%d), index(%d) : ¸Ê ¹®ÀÚ ¿À·ù",
            j,
            (unsigned int)nIniIndex);
        }
      }
    }
  }
}


