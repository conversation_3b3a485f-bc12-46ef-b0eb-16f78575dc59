﻿/*
 * Function: ?Init@CReturnGateController@@QEAA_NI@Z
 * Address: 0x140250220
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


char __fastcall CReturnGateController::Init(CReturnGateController *this, unsigned int uiSize)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v5; // rax@7
  __int64 v6; // rax@10
  __int64 v7; // rax@13
  __int64 v8; // rax@23
  __int64 v9; // [sp+0h] [bp-C8h]@1
  _object_id pID; // [sp+24h] [bp-A4h]@20
  unsigned int j; // [sp+34h] [bp-94h]@20
  unsigned int dwIndex; // [sp+38h] [bp-90h]@28
  CMyTimer *v13; // [sp+40h] [bp-88h]@9
  CMyTimer *v14; // [sp+48h] [bp-80h]@6
  CNetIndexList *v15; // [sp+50h] [bp-78h]@12
  CNetIndexList *v16; // [sp+58h] [bp-70h]@9
  CNetIndexList *v17; // [sp+60h] [bp-68h]@15
  CNetIndexList *v18; // [sp+68h] [bp-60h]@12
  CReturnGate **v19; // [sp+70h] [bp-58h]@15
  CReturnGate *v20; // [sp+78h] [bp-50h]@25
  CReturnGate *v21; // [sp+80h] [bp-48h]@22
  __int64 v22; // [sp+88h] [bp-40h]@4
  CMyTimer *v23; // [sp+90h] [bp-38h]@7
  CNetIndexList *v24; // [sp+98h] [bp-30h]@10
  CNetIndexList *v25; // [sp+A0h] [bp-28h]@13
  unsigned __int64 v26; // [sp+A8h] [bp-20h]@15
  CReturnGate *v27; // [sp+B0h] [bp-18h]@23
  CReturnGateController *v28; // [sp+D0h] [bp+8h]@1

  v28 = this;
  v2 = &v9;
  for (signed __int64 i = 48; i > 0; --i)
  {
    *(DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v22 = -2i64;
  if ( uiSize )
  {
    v28->m_uiGateTotCnt = uiSize;
    v14 = (CMyTimer *)operator new(0x18ui64);
    if ( v14 )
    {
      CMyTimer::CMyTimer(v14);
      v23 = (CMyTimer *)v5;
    }
    else
    {
      v23 = 0;
    }
    v13 = v23;
    v28->m_pkTimer = v23;
    v16 = (CNetIndexList *)operator new(0xA0ui64);
    if ( v16 )
    {
      CNetIndexList::CNetIndexList(v16);
      v24 = (CNetIndexList *)v6;
    }
    else
    {
      v24 = 0;
    }
    v15 = v24;
    v28->m_pkEmptyInxList = v24;
    v18 = (CNetIndexList *)operator new(0xA0ui64);
    if ( v18 )
    {
      CNetIndexList::CNetIndexList(v18);
      v25 = (CNetIndexList *)v7;
    }
    else
    {
      v25 = 0;
    }
    v17 = v25;
    v28->m_pkUseInxList = v25;
    v26 = v28->m_uiGateTotCnt;
    v19 = (CReturnGate **)operator new[](saturated_mul(8ui64, v26));
    v28->m_ppkGatePool = v19;
    if ( v28->m_pkTimer && v28->m_pkEmptyInxList && v28->m_pkUseInxList && v28->m_ppkGatePool )
    {
      memset_0(v28->m_ppkGatePool, 0, 8i64 * v28->m_uiGateTotCnt);
      _object_id::_object_id(&pID, 1, 3, 0);
      for ( j = 0; j < v28->m_uiGateTotCnt; ++j )
      {
        pID.m_wIndex = j;
        v21 = (CReturnGate *)operator new(0xF0ui64);
        if ( v21 )
        {
          CReturnGate::CReturnGate(v21, &pID);
          v27 = (CReturnGate *)v8;
        }
        else
        {
          v27 = 0;
        }
        v20 = v27;
        v28->m_ppkGatePool[j] = v27;
        if ( !v28->m_ppkGatePool[j] )
        {
          CLogFile::Write(&stru_1799C97D0, "CReturnGateController::Init NULL == new CReturnGate!");
          return 0;
        }
      }
      CNetIndexList::SetList(v28->m_pkEmptyInxList, v28->m_uiGateTotCnt);
      CNetIndexList::SetList(v28->m_pkUseInxList, v28->m_uiGateTotCnt);
      for ( dwIndex = 0; dwIndex < v28->m_uiGateTotCnt; ++dwIndex )
        CNetIndexList::PushNode_Back(v28->m_pkEmptyInxList, dwIndex);
      CMyTimer::BeginTimer(v28->m_pkTimer, 0x2710u);
      result = 1;
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    CLogFile::Write(&stru_1799C97D0, "CReturnGateController::Init 0 == uiSize!");
    result = 0;
  }
  return result;
}


