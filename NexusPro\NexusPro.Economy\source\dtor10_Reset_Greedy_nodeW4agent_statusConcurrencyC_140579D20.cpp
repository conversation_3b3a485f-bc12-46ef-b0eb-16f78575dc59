﻿/*
 * Function: ?dtor_1@?0??_Reset@?$_Greedy_node@W4agent_status@Concurrency@@@Concurrency@@UEAAXXZ@4HA_0
 * Address: 0x140579D20
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Required includes for Concurrency namespace
#include <ppl.h>
#include <ppltasks.h>
#include <concurrent_vector.h>



int __fastcallConcurrency::_Greedy_node<enumConcurrency::agent_status> ::_Reset'::1'::dtor_1(__int64 a1, __int64 a2)
{
  return CryptoPP::AlgorithmParameters<CryptoPP::AlgorithmParameters<CryptoPP::NullNameValuePairs,int const *> ,int>::~AlgorithmParameters<CryptoPP::AlgorithmParameters<CryptoPP::NullNameValuePairs,int const *>,int>(a2 + 72);
}

