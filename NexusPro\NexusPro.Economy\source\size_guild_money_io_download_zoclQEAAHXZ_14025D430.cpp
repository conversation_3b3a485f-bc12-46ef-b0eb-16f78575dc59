﻿/*
 * Function: ?size@_guild_money_io_download_zocl@@QEAAHXZ
 * Address: 0x14025D430
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


signed __int64 __fastcall _guild_money_io_download_zocl::size(_guild_money_io_download_zocl *)
{
  if ( (signed int)this->wDataSize > 10000 )
    this->wDataSize = 0;
  return 10002i64 - (10000 - this->wDataSize);
}

