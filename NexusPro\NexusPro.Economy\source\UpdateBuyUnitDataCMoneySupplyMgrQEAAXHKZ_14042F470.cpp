﻿/*
 * Function: ?UpdateBuyUnitData@CMoneySupplyMgr@@QEAAXHK@Z
 * Address: 0x14042F470
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CMoneySupplyMgr::UpdateBuyUnitData(CMoneySupplyMgr *this, int nLv, unsigned int nAmount)
{
  this->m_MS_data.dwAmount[7] += nAmount;
  switch()
{
    case 30:
      ++this->m_MS_data.nBuyUnitLv[0];
      break;
    case 40:
      ++this->m_MS_data.nBuyUnitLv[1];
      break;
    case 50:
      ++this->m_MS_data.nBuyUnitLv[2];
      break;
    case 60:
      ++this->m_MS_data.nBuyUnitLv[3];
      break;
  }
}

