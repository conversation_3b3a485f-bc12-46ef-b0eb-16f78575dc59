﻿/*
 * Function: ?_Create<PERSON>on@@YAPEAVCMonster@@PEAD0MMM@Z
 * Address: 0x140406260
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


CMonster *__fastcall _CreateMon(char *strMapName, char *MonCode, float fX, float fY, float fZ)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  CMonster *result; // rax@5
  __int64 v8; // [sp+0h] [bp-98h]@1
  float fPos; // [sp+58h] [bp-40h]@4
  float v10; // [sp+5Ch] [bp-3Ch]@4
  float v11; // [sp+60h] [bp-38h]@4
  CMapData *pMap; // [sp+78h] [bp-20h]@4
  char *szMapCode; // [sp+A0h] [bp+8h]@1
  char *pszMonsterCode; // [sp+A8h] [bp+10h]@1

  pszMonsterCode = MonCode;
  szMapCode = strMapName;
  v5 = &v8;
  for (signed __int64 i = 36; i > 0; --i)
  {
    *(DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  fPos = fX;
  v10 = fY;
  v11 = fZ;
  pMap = CMapOperation::GetMap(&g_MapOper, szMapCode);
  if ( pMap )
    result = CreateRepMonster(pMap, 0, &fPos, pszMonsterCode, 0i64, 0, 1, 0, 0, 1);
  else
    result = 0;
  return result;
}


