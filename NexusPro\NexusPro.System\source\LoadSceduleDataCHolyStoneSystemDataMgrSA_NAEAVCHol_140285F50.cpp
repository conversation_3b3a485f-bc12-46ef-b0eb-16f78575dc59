﻿/*
 * Function: ?LoadSceduleData@CHolyStoneSystemDataMgr@@SA_NAEAVCHolyScheduleData@@@Z
 * Address: 0x140285F50
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// External symbol declarations for decompiled code
extern "C" ULONG_PTR _security_cookie;



char __fastcall CHolyStoneSystemDataMgr::LoadSceduleData(CHolyScheduleData *clsDummy)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@16
  __int64 v4; // [sp+0h] [bp-B8h]@1
  char Dest; // [sp+30h] [bp-88h]@9
  unsigned int j; // [sp+74h] [bp-44h]@4
  int k; // [sp+78h] [bp-40h]@4
  char v8; // [sp+7Ch] [bp-3Ch]@4
  UINT v9; // [sp+80h] [bp-38h]@9
  CHolyScheduleData::__HolyScheduleNode *v10; // [sp+88h] [bp-30h]@9
  CHolyScheduleData::__HolyScheduleNode *v11; // [sp+98h] [bp-20h]@7
  unsigned __int64 v12; // [sp+A0h] [bp-18h]@7
  unsigned __int64 v13; // [sp+A8h] [bp-10h]@4
  CHolyScheduleData *v14; // [sp+C0h] [bp+8h]@1

  v14 = clsDummy;
  v1 = &v4;
  for (signed __int64 i = 44; i > 0; --i)
  {
    *(DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v13 = (unsigned __int64)&v4 ^ _security_cookie;
  CHolyScheduleData::Init(v14);
  j = 0;
  k = 0;
  v8 = 0;
  v14->m_nTotalSchedule = GetPrivateProfileIntA(
                            "total_schedule_count",
                            "schedule_count",
                            0,
                            ".\\Initialize\\NewHolySystem.ini");
  if ( v14->m_nTotalSchedule > 0 || v14->m_nTotalSchedule >= 3 )
  {
    v12 = v14->m_nTotalSchedule;
    v11 = (CHolyScheduleData::__HolyScheduleNode *)operator new[](saturated_mul(0x1Cui64, v12));
    v14->m_pSchedule = v11;
    for ( j = 0; (signed int)j < v14->m_nTotalSchedule; ++j )
    {
      v9 = -1;
      sprintf(&Dest, "Schedule_%d", j);
      v10 = &v14->m_pSchedule[j];
      for ( k = 0; k < 7; ++k )
      {
        v9 = GetPrivateProfileIntA(&Dest, c_TempPair[k], -1, ".\\Initialize\\NewHolySystem.ini");
        if ( v9 == -1 )
        {
          v8 = k + 2;
          goto LABEL_15;
        }
        v10->m_nSceneTime[k] = v9;
      }
    }
  }
  else
  {
    v8 = 1;
  }
LABEL_15:
  if ( v8 )
  {
    CHolyScheduleData::Init(v14);
    result = 0;
  }
  else
  {
    v14->m_bSet = 1;
    result = 1;
  }
  return result;
}


