﻿/*
 * Function: ?Check<PERSON><PERSON>@DfAIMgr@@SAHPEAVCMonsterAI@@PEAVCMonster@@@Z
 * Address: 0x140152150
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


signed __int64 __usercall DfAIMgr::CheckSPF@<rax>(CMonsterAI *pAI@<rcx>, CMonster *pMon@<rdx>, float a3@<xmm0>)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 result; // rax@6
  int v6; // eax@16
  int v7; // eax@18
  unsigned int v8; // eax@19
  int v9; // eax@20
  int v10; // eax@23
  __int64 v11; // [sp+0h] [bp-B8h]@1
  CCharacter **v12; // [sp+20h] [bp-98h]@20
  int nIndex; // [sp+30h] [bp-88h]@7
  int v14; // [sp+34h] [bp-84h]@10
  CMonsterSkill *pSkill; // [sp+38h] [bp-80h]@10
  CCharacter *pTarget; // [sp+48h] [bp-70h]@20
  __int64 v17; // [sp+68h] [bp-50h]@20
  int v18; // [sp+74h] [bp-44h]@20
  float v19; // [sp+78h] [bp-40h]@26
  int v20; // [sp+7Ch] [bp-3Ch]@16
  int v21; // [sp+80h] [bp-38h]@20
  int (__fastcall **v22)(QWORD, QWORD, QWORD, QWORD); // [sp+88h] [bp-30h]@20
  int v23; // [sp+90h] [bp-28h]@23
  int (__fastcall **v24)(QWORD, QWORD, QWORD, QWORD); // [sp+98h] [bp-20h]@23
  int v25; // [sp+A0h] [bp-18h]@23
  CMonsterAI *v26; // [sp+C0h] [bp+8h]@1
  CMonster *pMona; // [sp+C8h] [bp+10h]@1

  pMona = pMon;
  v26 = pAI;
  v3 = &v11;
  for (signed __int64 i = 44; i > 0; --i)
  {
    *(DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( v26 && pMon )
  {
    for ( nIndex = 0; nIndex < 16; ++nIndex )
    {
      v14 = rand() % 10000;
      pSkill = CMonsterSkillPool::GetMonSkill(&pMona->m_MonsterSkillPool, nIndex);
      if ( pSkill )
      {
        if ( CMonsterSkill::GetType(pSkill) )
        {
          if ( CMonsterSkill::GetMotive(pSkill) < 4
            && CMonsterSkill::GetMotive(pSkill) >= 0
            && CMonsterSkill::IsExit(pSkill) )
          {
            v20 = CMonsterSkill::GetAccumulationCount(pSkill);
            v6 = CMonsterSkill::GetSPLimitCount(pSkill);
            if ( v20 < v6 )
            {
              if ( CMonsterSkill::GetType(pSkill) && (v7 = CMonsterSkill::GetSPActionProbability(pSkill), v7 < v14) )
              {
                v8 = GetLoopTime();
                CMonsterSkill::Use(pSkill, v8, 0);
              }
              else
              {
                pTarget = 0;
                v17 = 0;
                v18 = 1;
                v21 = CMonsterSkill::GetMotiveValue(pSkill);
                v9 = CMonsterSkill::GetMotive(pSkill);
                v22 = DfAIMgr::ms_CheckMotiveFunction;
                v12 = &pTarget;
                if ( ((int (__fastcall **)(CMonsterSkill *, QWORD, CMonsterAI *, CMonster *))DfAIMgr::ms_CheckMotiveFunction)[v9](
                       pSkill,
                       (unsigned int)v21,
                       v26,
                       pMona) == 1 )
                {
                  if ( CMonsterSkill::GetExceptMotive(pSkill) > 0 && CMonsterSkill::GetExceptMotive(pSkill) < 4 )
                  {
                    v23 = CMonsterSkill::GetExceptMotiveValue(pSkill);
                    v10 = CMonsterSkill::GetExceptMotive(pSkill);
                    v24 = DfAIMgr::ms_CheckMotiveFunction;
                    v12 = (CCharacter **)&v17;
                    v25 = ((int (__fastcall **)(CMonsterSkill *, QWORD, CMonsterAI *, CMonster *))DfAIMgr::ms_CheckMotiveFunction)[v10](
                            pSkill,
                            (unsigned int)v23,
                            v26,
                            pMona) == 0;
                    v18 = v25;
                  }
                  if ( v18 )
                  {
                    if ( pTarget )
                    {
                      Get3DSqrt(pMona->m_fCurPos, pTarget->m_fCurPos);
                      v19 = a3;
                      CMonsterSkill::GetAttackDist(pSkill);
                      if ( a3 >= v19 )
                      {
                        if ( DfAIMgr::UseSkill_Target(pMona, pTarget, pSkill) )
                          return 1i64;
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
    result = 0;
  }
  else
  {
    result = 0;
  }
  return result;
}


