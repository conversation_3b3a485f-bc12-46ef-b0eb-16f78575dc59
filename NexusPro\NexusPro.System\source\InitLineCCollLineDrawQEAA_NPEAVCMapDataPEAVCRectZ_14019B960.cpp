﻿/*
 * Function: ?InitLine@CCollLineDraw@@QEAA_NPEAVCMapData@@PEAVCRect@@@Z
 * Address: 0x14019B960
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// External symbol declarations
extern "C" IMAGE_DOS_HEADER __ImageBase;
extern "C" DWORD off_14000003C;
extern "C" void* _delayLoadHelper2(ImgDelayDescr* pidd, void** ppfnIATEntry);
extern struct EqSukData { void* pwszEpSuk; } EqSukList[16];
extern void MyMessageBox(const char* title, const char* message);


char __fastcall CCollLineDraw::InitLine(CCollLineDraw *this, CMapData *pMap, CRect *prcWnd)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  char *v6; // rax@7
  __int64 v7; // [sp+0h] [bp-78h]@1
  int v8; // [sp+20h] [bp-58h]@7
  int j; // [sp+30h] [bp-48h]@10
  float *pPos; // [sp+38h] [bp-40h]@12
  _coll_point *v11; // [sp+40h] [bp-38h]@9
  char v12; // [sp+48h] [bp-30h]@10
  unsigned __int64 v13; // [sp+58h] [bp-20h]@9
  CCollLineDraw *v14; // [sp+80h] [bp+8h]@1
  CMapData *pMapa; // [sp+88h] [bp+10h]@1
  CRect *v16; // [sp+90h] [bp+18h]@1

  v16 = prcWnd;
  pMapa = pMap;
  v14 = this;
  v3 = &v7;
  for (signed __int64 i = 26; i > 0; --i)
  {
    *(DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( v14->m_pMap )
  {
    result = 0;
  }
  else
  {
    v14->m_pMap = pMap;
    v14->m_nPointNum = pMap->m_Level.mBsp->mCFVertexNum;
    if ( v14->m_nPointNum <= 15000 )
    {
      if ( !v14->m_Point )
      {
        v13 = v14->m_nPointNum;
        v11 = (_coll_point *)operator new[](saturated_mul(0x20ui64, v13));
        v14->m_Point = v11;
      }
      qmemcpy(&v12, v16, 0x10ui64);
      qmemcpy(&v14->m_rcWnd, &v12, sizeof(v14->m_rcWnd));
      for ( j = 0; j < v14->m_nPointNum; ++j )
      {
        pPos = pMapa->m_Level.mBsp->mCFVertex[j];
        _coll_point::InitPoint(&v14->m_Point[j], pMapa, pPos, &v14->m_rcWnd);
      }
      v14->m_nLineNum = pMapa->m_Level.mBsp->mCFLineNum;
      v14->m_pLine = pMapa->m_Level.mBsp->mCFLine;
      result = 1;
    }
    else
    {
      v6 = pMap->m_pMapSet->m_strCode;
      v8 = 15000;
      MyMessageBox(
        "CCollLineDraw::InitLine",
        "%s map's coll_point_num(%d) is more than max_coll_point(%d)",
        v6,
        v14->m_nPointNum);
      result = 0;
    }
  }
  return result;
}


