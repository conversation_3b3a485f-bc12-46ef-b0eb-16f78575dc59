﻿/*
 * Function: ?_InitSDM_LootTBL@CMonster@@SAXXZ
 * Address: 0x1401492B0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void CMonster::_InitSDM_LootTBL(void)
{
  __int64 *v0; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v2; // [sp+0h] [bp-68h]@1
  int v3; // [sp+20h] [bp-48h]@5
  int n; // [sp+24h] [bp-44h]@5
  _base_fld *v5; // [sp+28h] [bp-40h]@8
  _base_fld *v6; // [sp+30h] [bp-38h]@8
  int j; // [sp+38h] [bp-30h]@10
  _base_fld *v8; // [sp+40h] [bp-28h]@11
  struct _monster_loot_index *v9; // [sp+48h] [bp-20h]@5
  __int64 v10; // [sp+50h] [bp-18h]@5

  v0 = &v2;
  for (signed __int64 i = 24; i > 0; --i)
  {
    *(DWORD *)v0 = -858993460;
    v0 = (__int64 *)((char *)v0 + 4);
  }
  if ( !CMonster::s_idxMonsterLoot )
  {
    v3 = CRecordData::GetRecordNum(&stru_1799C6210);
    v10 = v3;
    v9 = (struct _monster_loot_index *)operator new[](saturated_mul(8ui64, v3));
    CMonster::s_idxMonsterLoot = v9;
    for ( n = 0; n < v3; ++n )
    {
      v5 = CRecordData::GetRecord(&stru_1799C6210, n);
      v6 = CRecordData::GetRecord(&stru_1799C6638, v5->m_strCode);
      if ( v6 )
      {
        CMonster::s_idxMonsterLoot[n].nEndRecIndex = v6->m_dwIndex;
        CMonster::s_idxMonsterLoot[n].nStartRecIndex = CMonster::s_idxMonsterLoot[n].nEndRecIndex;
        for ( j = CMonster::s_idxMonsterLoot[n].nStartRecIndex; ; CMonster::s_idxMonsterLoot[n].nEndRecIndex = j )
        {
          v8 = CRecordData::GetRecord(&stru_1799C6638, ++j);
          if ( !v8 || strncmp(v8->m_strCode, v5->m_strCode, 5ui64) )
            break;
        }
      }
      else
      {
        CMonster::s_idxMonsterLoot[n].nStartRecIndex = -1;
      }
    }
  }
}


