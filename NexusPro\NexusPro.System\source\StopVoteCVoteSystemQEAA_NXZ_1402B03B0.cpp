﻿/*
 * Function: ?StopVote@CVoteSystem@@QEAA_NXZ
 * Address: 0x1402B03B0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// External symbol declarations for decompiled code
extern "C" ULONG_PTR _security_cookie;



char __fastcall CVoteSystem::StopVote(CVoteSystem *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v4; // [sp+0h] [bp-98h]@1
  char szMsg[4]; // [sp+38h] [bp-60h]@6
  __int16 v6[12]; // [sp+3Ch] [bp-5Ch]@8
  unsigned int dwClientIndex; // [sp+54h] [bp-44h]@6
  char pbyType; // [sp+64h] [bp-34h]@9
  char v9; // [sp+65h] [bp-33h]@9
  CPlayer *v10; // [sp+78h] [bp-20h]@12
  unsigned __int64 v11; // [sp+88h] [bp-10h]@4
  CVoteSystem *v12; // [sp+A0h] [bp+8h]@1

  v12 = this;
  v1 = &v4;
  for (signed __int64 i = 36; i > 0; --i)
  {
    *(DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v11 = (unsigned __int64)&v4 ^ _security_cookie;
  if ( v12->m_bActive )
  {
    v12->m_bActive = 0;
    CNetIndexList::ResetList(&v12->m_listVote);
    *(DWORD *)szMsg = v12->m_nSerial;
    for ( dwClientIndex = 0; (signed int)dwClientIndex < 3; ++dwClientIndex )
      v6[dwClientIndex] = v12->m_dwPoint[dwClientIndex];
    pbyType = 26;
    v9 = 7;
    for ( dwClientIndex = 0; (signed int)dwClientIndex < 2532; ++dwClientIndex )
    {
      v10 = &g_Player + (signed int)dwClientIndex;
      if ( v10->m_bLive )
      {
        if ( CPlayerDB::GetRaceCode(&v10->m_Param) == v12->m_byRaceCode )
          CNetProcess::LoadSendMsg(unk_1414F2088, dwClientIndex, &pbyType, szMsg, 0xAu);
      }
    }
    if ( v12->m_bPunishment && v12->m_dwPoint[0] > v12->m_dwPoint[1] )
      CVoteSystem::ProcessPunishment(v12);
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}


