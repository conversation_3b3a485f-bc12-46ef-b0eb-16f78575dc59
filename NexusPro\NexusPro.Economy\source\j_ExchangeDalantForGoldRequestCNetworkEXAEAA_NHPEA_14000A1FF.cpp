﻿/*
 * Function: j_?ExchangeDalantForGoldRequest@CNetworkEX@@AEAA_NHPEAD@Z
 * Address: 0x14000A1FF
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


bool __fastcall CNetworkEX::ExchangeDalantForGoldRequest(CNetworkEX *this, int n, char *pBuf)
{
  return // CNetworkEX::ExchangeDalantForGoldRequest(); // MALFORMED CONSTRUCTOR - COMMENTED OUT
}

