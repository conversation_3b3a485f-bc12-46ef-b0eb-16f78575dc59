#!/usr/bin/env python3
"""
Advanced Function Signature Parser for RF Online Decompiled Code
Handles malformed function signatures with special characters and invalid syntax
"""

import os
import re
import sys
from pathlib import Path
from typing import List, Dict, Tuple, Optional

class AdvancedSignatureParser:
    def __init__(self):
        self.special_char_patterns = {
            r'0x60': '`',
            r'0x40': '@', 
            r'0x7E': '~',
            r'0x21': '!',
            r'0x24': '$',
            r'0x25': '%',
            r'0x5E': '^',
            r'0x26': '&',
            r'0x2A': '*',
            r'0x28': '(',
            r'0x29': ')',
            r'0x2D': '-',
            r'0x2B': '+',
            r'0x3D': '=',
            r'0x7B': '{',
            r'0x7D': '}',
            r'0x5B': '[',
            r'0x5D': ']',
            r'0x7C': '|',
            r'0x5C': '\\',
            r'0x3A': ':',
            r'0x22': '"',
            r'0x3B': ';',
            r'0x27': "'",
            r'0x3C': '<',
            r'0x3E': '>',
            r'0x3F': '?',
            r'0x2C': ',',
            r'0x2E': '.',
            r'0x2F': '/',
        }
        
        self.malformed_patterns = [
            # Function signatures with 'this' parameters
            (r'(\w+)\s*\(\s*(\w+\s*\*\s*)?this\s*[,)]', r'\1(\2'),
            
            # Malformed template declarations
            (r'std::allocator<([^>]+)>\s*::\s*(\w+)\s*\([^)]*this[^)]*\)', r'// Removed malformed allocator function'),
            
            # Invalid character sequences in function names
            (r'(\w+)0x[0-9A-Fa-f]+(\w*)', r'\1_\2'),
            
            # Malformed destructor patterns
            (r'~(\w+)\s*\(\s*this\s*\)', r'~\1()'),
            
            # Invalid template specializations
            (r'std::(\w+)<[^>]*this[^>]*>', r'void /* malformed std::\1 */'),
            
            # Malformed member function pointers
            (r'(\w+)\s*::\s*(\w+)\s*\([^)]*this[^)]*\)\s*\{', r'void \1_\2() {'),
            
            # Invalid operator overloads
            (r'operator\s*0x[0-9A-Fa-f]+', r'operator_invalid'),
            
            # Malformed namespace declarations
            (r'(\w+)\s*::\s*0x[0-9A-Fa-f]+', r'\1::invalid_symbol'),
        ]
        
        self.stl_replacements = {
            # Complex STL patterns that cause issues
            r'std::_List_nod<[^>]+>': 'void*',
            r'std::_Iterator<[^>]+>': 'void*',
            r'std::allocator<[^>]+>::_Node': 'void*',
            r'std::allocator<[^>]+>::construct\s*\([^)]*this[^)]*\)': '// Removed malformed construct',
            r'std::allocator<[^>]+>::deallocate\s*\([^)]*this[^)]*\)': '// Removed malformed deallocate',
            r'std::allocator<[^>]+>::destroy\s*\([^)]*this[^)]*\)': '// Removed malformed destroy',
            r'std::list<[^>]+>::_Iterator': 'void*',
            r'std::vector<[^>]+>::_Iterator': 'void*',
        }

    def clean_special_characters(self, content: str) -> str:
        """Remove or replace special character sequences in code"""
        for hex_pattern, replacement in self.special_char_patterns.items():
            # Remove hex character references
            content = re.sub(hex_pattern, '', content)
        
        # Remove unknown character errors
        content = re.sub(r"error C2018: unknown character '[^']*'", '// Removed unknown character', content)
        
        return content

    def fix_malformed_signatures(self, content: str) -> str:
        """Fix malformed function signatures and declarations"""
        for pattern, replacement in self.malformed_patterns:
            content = re.sub(pattern, replacement, content, flags=re.MULTILINE)
        
        return content

    def fix_stl_templates(self, content: str) -> str:
        """Fix malformed STL template declarations"""
        for pattern, replacement in self.stl_replacements.items():
            content = re.sub(pattern, replacement, content, flags=re.MULTILINE)
        
        return content

    def fix_function_headers(self, content: str) -> str:
        """Fix malformed function headers and missing semicolons"""
        lines = content.split('\n')
        fixed_lines = []
        
        for line in lines:
            # Fix missing function headers
            if re.search(r'error C2447.*missing function header', line):
                continue  # Skip error lines
            
            # Fix lines that start with invalid syntax
            if re.match(r'^\s*[{}]\s*$', line):
                # Standalone braces - likely malformed function body
                fixed_lines.append('// Removed malformed function body')
                continue
            
            # Fix malformed function declarations
            if re.search(r'^\s*(\w+)\s*\([^)]*this[^)]*\)\s*$', line):
                # Function declaration with 'this' parameter
                match = re.match(r'^\s*(\w+)\s*\([^)]*this[^)]*\)\s*$', line)
                if match:
                    func_name = match.group(1)
                    fixed_lines.append(f'void {func_name}(); // Fixed malformed declaration')
                    continue
            
            fixed_lines.append(line)
        
        return '\n'.join(fixed_lines)

    def add_missing_includes(self, content: str) -> str:
        """Add missing includes for external functions"""
        if 'CN_InvalidateNature' in content or 'ReleaseVertexShaderList' in content:
            if '#include "NexusProCommon.h"' not in content:
                # Add after existing includes
                include_pattern = r'(#include\s+[<"][^>"]+[>"].*?\n)'
                if re.search(include_pattern, content):
                    content = re.sub(
                        r'((?:#include\s+[<"][^>"]+[>"].*?\n)+)',
                        r'\1#include "NexusProCommon.h"\n',
                        content,
                        count=1
                    )
                else:
                    # Add at the beginning
                    content = '#include "NexusProCommon.h"\n\n' + content
        
        return content

    def process_file(self, file_path: Path) -> bool:
        """Process a single file and apply all fixes"""
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            original_content = content
            
            # Apply all fixes in sequence
            content = self.clean_special_characters(content)
            content = self.fix_malformed_signatures(content)
            content = self.fix_stl_templates(content)
            content = self.fix_function_headers(content)
            content = self.add_missing_includes(content)
            
            # Only write if content changed
            if content != original_content:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                print(f"✅ Fixed: {file_path}")
                return True
            else:
                print(f"⏭️  No changes: {file_path}")
                return False
                
        except Exception as e:
            print(f"❌ Error processing {file_path}: {e}")
            return False

    def process_directory(self, directory: Path, file_extensions: List[str] = ['.cpp', '.h']) -> Dict[str, int]:
        """Process all files in a directory"""
        stats = {'processed': 0, 'fixed': 0, 'errors': 0}
        
        for ext in file_extensions:
            for file_path in directory.rglob(f'*{ext}'):
                stats['processed'] += 1
                try:
                    if self.process_file(file_path):
                        stats['fixed'] += 1
                except Exception as e:
                    stats['errors'] += 1
                    print(f"❌ Error: {e}")
        
        return stats

def main():
    if len(sys.argv) < 2:
        print("Usage: python advanced_signature_parser.py <directory_path>")
        sys.exit(1)
    
    directory = Path(sys.argv[1])
    if not directory.exists():
        print(f"Directory not found: {directory}")
        sys.exit(1)
    
    parser = AdvancedSignatureParser()
    print(f"🔧 Processing directory: {directory}")
    
    stats = parser.process_directory(directory)
    
    print(f"\n📊 Processing Summary:")
    print(f"   Files processed: {stats['processed']}")
    print(f"   Files fixed: {stats['fixed']}")
    print(f"   Errors: {stats['errors']}")

if __name__ == "__main__":
    main()
