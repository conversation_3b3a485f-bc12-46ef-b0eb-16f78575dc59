﻿/*
 * Function: ??$_Uninit_move@PEAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@PEAV12@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@YAPEAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@0@PEAV10@00AEAV?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@0@U_Swap_move_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x140429AB0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// External symbol declarations for decompiled code
extern "C" ULONG_PTR _security_cookie;



std::basic_string<char,std::char_traits<char>,std::allocator<char> > *__fastcall std::_Uninit_move<std::basic_string<char,std::char_traits<char>,std::allocator<char>> *,std::basic_string<char,std::char_traits<char>,std::allocator<char>> *,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char>>>>(std::basic_string<char,std::char_traits<char>,std::allocator<char> > *_First, std::basic_string<char,std::char_traits<char>,std::allocator<char> > *_Last, std::basic_string<char,std::char_traits<char>,std::allocator<char> > *_Dest, std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > *_Al, std::_Swap_move_tag __formal, std::_Range_checked_iterator_tag a6)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  BYTE *v8; // rax@4
  __int64 v10; // [sp+0h] [bp-A8h]@1
  std::basic_string<char,std::char_traits<char>,std::allocator<char> > *v11; // [sp+20h] [bp-88h]@4
  std::basic_string<char,std::char_traits<char>,std::allocator<char> > _Val; // [sp+38h] [bp-70h]@4
  std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Has_debug_it v13; // [sp+80h] [bp-28h]@4
  std::basic_string<char,std::char_traits<char>,std::allocator<char> > *v14; // [sp+88h] [bp-20h]@7
  __int64 v15; // [sp+90h] [bp-18h]@4
  unsigned __int64 v16; // [sp+98h] [bp-10h]@4
  std::basic_string<char,std::char_traits<char>,std::allocator<char> > *_Right; // [sp+B0h] [bp+8h]@1
  std::basic_string<char,std::char_traits<char>,std::allocator<char> > *v18; // [sp+B8h] [bp+10h]@1
  std::basic_string<char,std::char_traits<char>,std::allocator<char> > *_Ptr; // [sp+C0h] [bp+18h]@1
  std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > *v20; // [sp+C8h] [bp+20h]@1

  v20 = _Al;
  _Ptr = _Dest;
  v18 = _Last;
  _Right = _First;
  v6 = &v10;
  for (signed __int64 i = 40; i > 0; --i)
  {
    *(DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  v15 = -2i64;
  v16 = (unsigned __int64)&v10 ^ _security_cookie;
  v11 = _Dest;
  std::basic_string<char,std::char_traits<char>,std::allocator<char>>::_Has_debug_it::_Has_debug_it(&v13, 0);
  std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string<char,std::char_traits<char>,std::allocator<char>>(
    &_Val,
    *v8);
  while ( _Right != v18 )
  {
    std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char>>>::construct(v20, _Ptr, &_Val);
    std::swap<char,std::char_traits<char>,std::allocator<char>>(_Ptr, _Right);
    ++_Ptr;
    ++_Right;
  }
  v14 = _Ptr;
  std::basic_string<char,std::char_traits<char>,std::allocator<char>>::~basic_string<char,std::char_traits<char>,std::allocator<char>>(&_Val);
  return v14;
}


