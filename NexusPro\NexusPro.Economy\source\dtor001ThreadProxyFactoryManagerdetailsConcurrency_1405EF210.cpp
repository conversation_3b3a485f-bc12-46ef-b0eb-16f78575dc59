﻿/*
 * Function: ?dtor_0@?0???**************************@details@Concurrency@@QEAA@XZ@4HA_3
 * Address: 0x1405EF210
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Required includes for Concurrency namespace
#include <ppl.h>
#include <ppltasks.h>
#include <concurrent_vector.h>



void __fastcallConcurrency::details::ThreadProxyFactoryManager::~ThreadProxyFactoryManager'::1'::dtor_0(__int64 a1, __int64 a2)
{
  CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0> >::~Sec<PERSON><PERSON><unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>((CryptoPP::Se<PERSON><PERSON><PERSON><unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0> > *)(*(QWORD *)(a2 + 64) + 24i64));
}


