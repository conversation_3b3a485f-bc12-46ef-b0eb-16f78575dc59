﻿/*
 * Function: ?Instance@CMoneySupplyMgr@@SAPEAV1@XZ
 * Address: 0x140095070
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


CMoneySupplyMgr *__cdecl CMoneySupplyMgr::Instance()
{
  __int64 *v0; // rdi@1
  signed __int64 i; // rcx@1
  struct CMoneySupplyMgr *v2; // rax@6
  __int64 v4; // [sp+0h] [bp-48h]@1
  struct CMoneySupplyMgr *v5; // [sp+20h] [bp-28h]@8
  CMoneySupplyMgr *v6; // [sp+28h] [bp-20h]@5
  __int64 v7; // [sp+30h] [bp-18h]@4
  struct CMoneySupplyMgr *v8; // [sp+38h] [bp-10h]@6

  v0 = &v4;
  for()
{
    *(DWORD *)v0 = -858993460;
    v0 = (__int64 *)((char *)v0 + 4);
  }
  v7 = -2i64;
  if()
{
    v6 = (CMoneySupplyMgr *)operator new(0x9A8ui64);
    if()
{
      CMoneySupplyMgr::CMoneySupplyMgr(v6);
      v8 = v2;
    }
    else
    {
      v8 = 0;
    }
    v5 = v8;
    CMoneySupplyMgr::pInstance = v8;
  }
  return CMoneySupplyMgr::pInstance;
}


