﻿/*
 * Function: ?CreateObjSurface@CMapDisplay@@AEAAJXZ
 * Address: 0x14019F690
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


__int64 __fastcall CMapDisplay::CreateObjSurface(CMapDisplay *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  HRESULT v3; // eax@17
  HRESULT v4; // eax@20
  __int64 v6; // [sp+0h] [bp-118h]@1
  unsigned int dwDesiredHeight; // [sp+20h] [bp-F8h]@8
  int v8; // [sp+30h] [bp-E8h]@4
  char *strBMP; // [sp+50h] [bp-C8h]@4
  const char *v10; // [sp+58h] [bp-C0h]@4
  const char *v11; // [sp+60h] [bp-B8h]@4
  const char *v12; // [sp+68h] [bp-B0h]@4
  const char *v13; // [sp+70h] [bp-A8h]@4
  const char *v14; // [sp+78h] [bp-A0h]@4
  const char *v15; // [sp+80h] [bp-98h]@4
  const char *v16; // [sp+88h] [bp-90h]@4
  const char *v17; // [sp+90h] [bp-88h]@4
  const char *v18; // [sp+98h] [bp-80h]@4
  const char *v19; // [sp+A0h] [bp-78h]@4
  const char *v20; // [sp+A8h] [bp-70h]@4
  const char *v21; // [sp+B0h] [bp-68h]@4
  char *v22; // [sp+D8h] [bp-40h]@4
  const char *v23; // [sp+E0h] [bp-38h]@4
  const char *v24; // [sp+E8h] [bp-30h]@4
  const char *v25; // [sp+F0h] [bp-28h]@4
  const char *v26; // [sp+F8h] [bp-20h]@4
  int j; // [sp+104h] [bp-14h]@4
  CMapDisplay *v28; // [sp+120h] [bp+8h]@1

  v28 = this;
  v1 = &v6;
  for (signed __int64 i = 68; i > 0; --i)
  {
    *(DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v8 = 0;
  strBMP = ".\\Bitmap\\Player.bmp";
  v10 = ".\\Bitmap\\Monster.bmp";
  v11 = ".\\Bitmap\\NPC.bmp";
  v12 = ".\\Bitmap\\Animus.bmp";
  v13 = ".\\Bitmap\\Tower.bmp";
  v14 = ".\\Bitmap\\Stone.bmp";
  v15 = ".\\Bitmap\\Keeper.bmp";
  v16 = ".\\Bitmap\\Trap.bmp";
  v17 = ".\\Bitmap\\Trap.bmp";
  v18 = ".\\Bitmap\\Trap.bmp";
  v19 = ".\\Bitmap\\Trap.bmp";
  v20 = ".\\Bitmap\\Trap.bmp";
  v21 = ".\\Bitmap\\Trap.bmp";
  v22 = ".\\Bitmap\\Item.bmp";
  v23 = ".\\Bitmap\\DungeonGate.bmp";
  v24 = ".\\Bitmap\\ParkingUnit.bmp";
  v25 = ".\\Bitmap\\Item.bmp";
  v26 = ".\\Bitmap\\Item.bmp";
  for ( j = 0; j < 13; ++j )
  {
    if ( !(&strBMP)[8 * j] )
      (&strBMP)[8 * j] = ".\\Bitmap\\Trap.bmp";
    dwDesiredHeight = 7;
    v8 = CDisplay::CreateSurfaceFromBitmap(
           (CDisplay *)&v28->vfptr,
           (CSurface **)v28->m_pSFObj + j,
           (&strBMP)[8 * j],
           7u,
           7u);
    if ( v8 < 0 )
      return (unsigned int)v8;
    CSurface::SetColorKey(v28->m_pSFObj[0][j], 0);
  }
  for ( j = 0; j < 5; ++j )
  {
    dwDesiredHeight = 7;
    v8 = CDisplay::CreateSurfaceFromBitmap((CDisplay *)&v28->vfptr, &v28->m_pSFObj[1][j], (&v22)[8 * j], 7u, 7u);
    if ( v8 < 0 )
      return (unsigned int)v8;
    CSurface::SetColorKey(v28->m_pSFObj[1][j], 0);
  }
  v8 = CDisplay::CreateSurfaceFromBitmap((CDisplay *)&v28->vfptr, &v28->m_pSFSelect, ".\\Bitmap\\Select.bmp", 7u, 7u);
  if ( v8 >= 0 )
  {
    CSurface::SetColorKey(v28->m_pSFSelect, 0);
    v3 = CDisplay::CreateSurfaceFromBitmap((CDisplay *)&v28->vfptr, &v28->m_pSFCircle, ".\\Bitmap\\Circle.bmp", 7u, 7u);
    v8 = v3;
    if ( v3 >= 0 )
    {
      CSurface::SetColorKey(v28->m_pSFCircle, 0);
      v4 = CDisplay::CreateSurfaceFromBitmap(
             (CDisplay *)&v28->vfptr,
             &v28->m_pSFCorpse,
             ".\\Bitmap\\Corpse.bmp",
             7u,
             7u);
      v8 = v4;
      if ( v4 >= 0 )
        CSurface::SetColorKey(v28->m_pSFCorpse, 0);
    }
  }
  return (unsigned int)v8;
}


