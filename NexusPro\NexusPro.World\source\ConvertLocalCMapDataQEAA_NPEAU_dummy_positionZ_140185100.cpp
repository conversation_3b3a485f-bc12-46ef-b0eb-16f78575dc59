﻿/*
 * Function: ?ConvertLocal@CMapData@@QEAA_NPEAU_dummy_position@@@Z
 * Address: 0x140185100
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// External symbol declarations
extern "C" IMAGE_DOS_HEADER __ImageBase;
extern "C" DWORD off_14000003C;
extern "C" void* _delayLoadHelper2(ImgDelayDescr* pidd, void** ppfnIATEntry);
extern struct EqSukData { void* pwszEpSuk; } EqSukList[16];
extern void MyMessageBox(const char* title, const char* message);


char __fastcall CMapData::ConvertLocal(CMapData *this, _dummy_position *pPos)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int32 v4; // eax@4
  char result; // al@5
  unsigned __int32 v6; // eax@6
  unsigned __int32 v7; // eax@8
  unsigned __int32 v8; // eax@10
  __int64 v9; // kr00_8@12
  __int64 v10; // kr08_8@12
  unsigned __int32 v11; // eax@12
  __int64 v12; // [sp+0h] [bp-148h]@1
  float v13; // [sp+28h] [bp-120h]@4
  float v14; // [sp+2Ch] [bp-11Ch]@4
  float v15; // [sp+30h] [bp-118h]@4
  float v16; // [sp+58h] [bp-F0h]@4
  float v17; // [sp+5Ch] [bp-ECh]@4
  float v18; // [sp+60h] [bp-E8h]@4
  float v19; // [sp+88h] [bp-C0h]@8
  float v20; // [sp+8Ch] [bp-BCh]@8
  float v21; // [sp+90h] [bp-B8h]@8
  float v22; // [sp+B8h] [bp-90h]@8
  float v23; // [sp+BCh] [bp-8Ch]@8
  float v24; // [sp+C0h] [bp-88h]@8
  float v25; // [sp+E8h] [bp-60h]@12
  float v26; // [sp+ECh] [bp-5Ch]@12
  float v27; // [sp+F0h] [bp-58h]@12
  CExtDummy *v28; // [sp+108h] [bp-40h]@4
  CExtDummy *v29; // [sp+110h] [bp-38h]@6
  CExtDummy *v30; // [sp+118h] [bp-30h]@8
  CExtDummy *v31; // [sp+120h] [bp-28h]@10
  int v32; // [sp+128h] [bp-20h]@12
  int v33; // [sp+12Ch] [bp-1Ch]@12
  CExtDummy *v34; // [sp+130h] [bp-18h]@12
  CMapData *v35; // [sp+150h] [bp+8h]@1
  _dummy_position *v36; // [sp+158h] [bp+10h]@1

  v36 = pPos;
  v35 = this;
  v2 = &v12;
  for (signed __int64 i = 80; i > 0; --i)
  {
    *(DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v13 = (float)pPos->m_zLocalMin[0];
  v14 = (float)pPos->m_zLocalMin[1];
  v15 = (float)pPos->m_zLocalMin[2];
  v16 = (float)pPos->m_zLocalMax[0];
  v17 = (float)pPos->m_zLocalMax[1];
  v18 = (float)pPos->m_zLocalMax[2];
  v4 = pPos->m_wLineIndex;
  v28 = &v35->m_Dummy;
  if ( CExtDummy::GetWorldFromLocal(&v35->m_Dummy, (float (*)[3])pPos->m_fMin, v4, &v13) )
  {
    v6 = v36->m_wLineIndex;
    v29 = &v35->m_Dummy;
    if ( CExtDummy::GetWorldFromLocal(&v35->m_Dummy, (float (*)[3])v36->m_fMax, v6, &v16) )
    {
      v19 = (float)v36->m_zLocalMax[0];
      v20 = (float)v36->m_zLocalMin[1];
      v21 = (float)v36->m_zLocalMin[2];
      v22 = (float)v36->m_zLocalMin[0];
      v23 = (float)v36->m_zLocalMax[1];
      v24 = (float)v36->m_zLocalMax[2];
      v7 = v36->m_wLineIndex;
      v30 = &v35->m_Dummy;
      if ( CExtDummy::GetWorldFromLocal(&v35->m_Dummy, (float (*)[3])v36->m_fRT, v7, &v19) )
      {
        v8 = v36->m_wLineIndex;
        v31 = &v35->m_Dummy;
        if ( CExtDummy::GetWorldFromLocal(&v35->m_Dummy, (float (*)[3])v36->m_fLB, v8, &v22) )
        {
          v32 = v36->m_zLocalMin[0];
          v9 = v36->m_zLocalMax[0] - v32;
          v25 = (float)((((signed int)v9 - HIDWORD(v9)) >> 1) + v32);
          v33 = v36->m_zLocalMin[1];
          v10 = v36->m_zLocalMax[1] - v33;
          v26 = (float)((((signed int)v10 - HIDWORD(v10)) >> 1) + v33);
          v27 = (float)v36->m_zLocalMin[2];
          v11 = v36->m_wLineIndex;
          v34 = &v35->m_Dummy;
          if ( CExtDummy::GetWorldFromLocal(&v35->m_Dummy, (float (*)[3])v36->m_fDirection, v11, &v25) )
          {
            result = 1;
          }
          else
          {
            MyMessageBox("CMapData Error", "ConvertLocal map:%s, dummy:%s", v35->m_pMapSet->m_strCode, v36);
            result = 0;
          }
        }
        else
        {
          MyMessageBox("CMapData Error", "ConvertLocal map:%s, dummy:%s", v35->m_pMapSet->m_strCode, v36);
          result = 0;
        }
      }
      else
      {
        MyMessageBox("CMapData Error", "ConvertLocal map:%s, dummy:%s", v35->m_pMapSet->m_strCode, v36);
        result = 0;
      }
    }
    else
    {
      MyMessageBox("CMapData Error", "ConvertLocal map:%s, dummy:%s", v35->m_pMapSet->m_strCode, v36);
      result = 0;
    }
  }
  else
  {
    MyMessageBox("CMapData Error", "ConvertLocal map:%s, dummy:%s", v35->m_pMapSet->m_strCode, v36);
    result = 0;
  }
  return result;
}


