﻿/*
 * Function: ?Init@CWeeklyGuildRankInfo@@QEAA_NXZ
 * Address: 0x1402CA8E0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


char __fastcall CWeeklyGuildRankInfo::Init(CWeeklyGuildRankInfo *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v4; // rax@9
  __int64 v5; // [sp+0h] [bp-68h]@1
  int j; // [sp+20h] [bp-48h]@6
  int k; // [sp+24h] [bp-44h]@16
  CWeeklyGuildRankRecord **v8; // [sp+28h] [bp-40h]@4
  CWeeklyGuildRankRecord *v9; // [sp+30h] [bp-38h]@11
  CWeeklyGuildRankRecord *v10; // [sp+38h] [bp-30h]@8
  _weekly_guild_rank_result_zocl *v11; // [sp+40h] [bp-28h]@14
  __int64 v12; // [sp+48h] [bp-20h]@4
  CWeeklyGuildRankRecord *v13; // [sp+50h] [bp-18h]@9
  CWeeklyGuildRankInfo *v14; // [sp+70h] [bp+8h]@1

  v14 = this;
  v1 = &v5;
  for (signed __int64 i = 24; i > 0; --i)
  {
    *(DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v12 = -2i64;
  v8 = (CWeeklyGuildRankRecord **)operator new[](0xFA0ui64);
  v14->m_ppkInfo = v8;
  if ( v14->m_ppkInfo )
  {
    for ( j = 0; j < 500; ++j )
    {
      v10 = (CWeeklyGuildRankRecord *)operator new(0x30ui64);
      if ( v10 )
      {
        CWeeklyGuildRankRecord::CWeeklyGuildRankRecord(v10);
        v13 = (CWeeklyGuildRankRecord *)v4;
      }
      else
      {
        v13 = 0;
      }
      v9 = v13;
      v14->m_ppkInfo[j] = v13;
      if ( !v14->m_ppkInfo[j] )
        return 0;
    }
    v11 = (_weekly_guild_rank_result_zocl *)operator new[](0x30Cui64);
    v14->m_pkSendList = v11;
    if ( v14->m_pkSendList )
    {
      for ( k = 0; k < 3; ++k )
        v14->m_pkSendList[k].dwVer = 0;
      v14->m_bInit = 1;
      CWeeklyGuildRankInfo::Clear(v14);
      if ( CMainThread::IsTestServer(&g_Main) )
        SETTLEMENT_AREA_MANAGE_OWNER_LIMIT_GRADE = 1;
      result = 1;
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}


