﻿/*
 * Function: ?ManagePopGuildMoney@CGuild@@QEAAEKKK@Z
 * Address: 0x140258EE0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// External symbol declarations for decompiled code
extern "C" ULONG_PTR _security_cookie;



char __usercall CGuild::ManagePopGuildMoney@<al>(CGuild *this@<rcx>, unsigned int dwDest@<edx>, unsigned int dwDalant@<r8d>, unsigned int dwGold@<r9d>, double a5@<xmm0>)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@6
  unsigned int v8; // eax@13
  unsigned int v9; // eax@15
  char *v10; // rax@17
  int v11; // eax@17
  __int64 v12; // [sp+0h] [bp-C8h]@1
  _guild_member_info *v13; // [sp+30h] [bp-98h]@4
  int Dst; // [sp+50h] [bp-78h]@17
  char Dest; // [sp+54h] [bp-74h]@17
  int v16; // [sp+68h] [bp-60h]@17
  unsigned int v17; // [sp+6Ch] [bp-5Ch]@17
  unsigned int v18; // [sp+70h] [bp-58h]@17
  unsigned int v19; // [sp+74h] [bp-54h]@17
  char v20; // [sp+78h] [bp-50h]@17
  char v21; // [sp+79h] [bp-4Fh]@17
  char v22; // [sp+7Ah] [bp-4Eh]@17
  char v23; // [sp+7Bh] [bp-4Dh]@17
  __int64 v24; // [sp+80h] [bp-48h]@17
  __int64 v25; // [sp+88h] [bp-40h]@17
  unsigned __int64 v26; // [sp+B0h] [bp-18h]@4
  CGuild *v27; // [sp+D0h] [bp+8h]@1
  int dwMemberSerial; // [sp+D8h] [bp+10h]@1
  unsigned int ui64AddMoney; // [sp+E0h] [bp+18h]@1
  unsigned int ui64AddGold; // [sp+E8h] [bp+20h]@1

  ui64AddGold = dwGold;
  ui64AddMoney = dwDalant;
  dwMemberSerial = dwDest;
  v27 = this;
  v5 = &v12;
  for (signed __int64 i = 48; i > 0; --i)
{
    *(DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v26 = (unsigned __int64)&v12 ^ _security_cookie;
  v13 = CGuild::GetMemberFromSerial(v27, dwDest);
  if()
{
    if()
{
      CGuild::GetTotalDalant(v27);
      if ( (double)(signed int)ui64AddMoney <= a5 && (CGuild::GetTotalGold(v27), (double)(signed int)ui64AddGold <= a5) )
      {
        v8 = CPlayerDB::GetDalant(&v13->pPlayer->m_Param);
        if ( CanAddMoneyForMaxLimMoney(ui64AddMoney, v8) )
        {
          v9 = CPlayerDB::GetGold(&v13->pPlayer->m_Param);
          if ( CanAddMoneyForMaxLimGold(ui64AddGold, v9) )
          {
            memset_0(&Dst, 0, 0x48ui64);
            Dst = dwMemberSerial;
            v16 = v27->m_nIndex;
            v17 = v27->m_dwSerial;
            v19 = ui64AddMoney;
            v18 = ui64AddGold;
            v24 = 0;
            v25 = 0;
            v20 = GetCurrentMonth();
            v21 = GetCurrentDay();
            v22 = GetCurrentHour();
            v23 = GetCurrentMin();
            v10 = CPlayerDB::GetCharNameW(&v13->pPlayer->m_Param);
            strcpy_0(&Dest, v10);
            v11 = _qry_case_outputgmoney::size((_qry_case_outputgmoney *)&Dst);
            if ( CMainThread::PushDQSData(&g_Main, 0xFFFFFFFF, 0, 20, (char *)&Dst, v11) )
            {
              v27->m_bIOWait = 1;
              result = 0;
            }
            else
            {
              result = 6;
            }
          }
          else
          {
            result = 105;
          }
        }
        else
        {
          result = 105;
        }
      }
      else
      {
        result = 102;
      }
    }
    else
    {
      result = 101;
    }
  }
  else
  {
    result = -54;
  }
  return result;
}


