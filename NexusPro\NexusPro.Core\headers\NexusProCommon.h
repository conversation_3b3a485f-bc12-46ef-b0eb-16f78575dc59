#pragma once
#ifndef NEXUSPRO_COMMON_H
#define NEXUSPRO_COMMON_H

/*
 * NexusPro Common Header
 * This file contains common includes, types, and definitions used across all NexusPro modules
 * Generated for RF Online decompiled source code compatibility
 */

// Windows and system includes
#include <windows.h>
#include <tchar.h>
#include <imagehlp.h>    // For image base and import descriptors

// Delay load imports - avoid conflicts with standard headers
#ifndef DELAYIMP_INSECURE_WRITABLE_HOOKS
#define DELAYIMP_INSECURE_WRITABLE_HOOKS
#endif
#include <delayimp.h>

// Standard C++ Library includes
#include <iostream>
#include <string>
#include <vector>
#include <list>
#include <map>
#include <unordered_map>
#include <set>
#include <unordered_set>
#include <queue>
#include <stack>
#include <deque>
#include <array>
#include <memory>
#include <utility>
#include <algorithm>
#include <functional>
#include <iterator>
#include <numeric>
#include <thread>
#include <mutex>
#include <atomic>
#include <chrono>
#include <random>
#include <fstream>
#include <sstream>
#include <iomanip>
#include <exception>
#include <stdexcept>
#include <typeinfo>
#include <type_traits>
#include <limits>
#include <cstdint>
#include <cstdlib>
#include <cstring>
#include <cmath>
#include <cassert>

// Common type definitions for RF Online compatibility
typedef unsigned char       BYTE;
typedef unsigned short      WORD;
typedef unsigned long       DWORD;
typedef unsigned __int64    QWORD;
typedef __int64             INT64;
typedef unsigned __int64    UINT64;
typedef float               FLOAT;
typedef double              DOUBLE;

// Common constants
#ifndef NULL
#define NULL 0
#endif

#ifndef TRUE
#define TRUE 1
#endif

#ifndef FALSE
#define FALSE 0
#endif

// Forward declarations for common RF Online classes
class CAsyncLogInfo;
class CUserDB;
class CMainThread;
class CNetwork;
class CGameObject;
class CMonster;
class CPlayer;
class CItem;
class CSkill;
class CQuest;
class CGuild;
class CMap;
class CMapData;
class CDatabase;
class CBilling;
class CSecurity;
class CHackShield;

// Common enumerations
enum ASYNC_LOG_TYPE {
    ASYNC_LOG_NONE = -1,
    ASYNC_LOG_INFO = 0,
    ASYNC_LOG_WARNING = 1,
    ASYNC_LOG_ERROR = 2,
    ASYNC_LOG_DEBUG = 3
};

// Common structures
struct SYSTEMTIME_EX {
    WORD wYear;
    WORD wMonth;
    WORD wDayOfWeek;
    WORD wDay;
    WORD wHour;
    WORD wMinute;
    WORD wSecond;
    WORD wMilliseconds;
};

struct _CLID {
    DWORD dwID;
    DWORD dwSubID;
};

struct _WA_AVATOR_CODE {
    DWORD dwCode;
    DWORD dwSubCode;
};

// Common macros for decompiled code compatibility
#define QEAA    // __thiscall convention marker from IDA Pro
#define UEAA    // __thiscall convention marker from IDA Pro
#define MEAA    // __thiscall convention marker from IDA Pro
#define AEAA    // __thiscall convention marker from IDA Pro
#define YEAA    // __thiscall convention marker from IDA Pro
#define PEAA    // __thiscall convention marker from IDA Pro

// Memory management helpers
#define SAFE_DELETE(p)       { if(p) { delete (p);     (p)=NULL; } }
#define SAFE_DELETE_ARRAY(p) { if(p) { delete[] (p);   (p)=NULL; } }
#define SAFE_RELEASE(p)      { if(p) { (p)->Release(); (p)=NULL; } }

// Debug helpers
#ifdef _DEBUG
#define RF_ASSERT(expr) assert(expr)
#define RF_TRACE(msg) OutputDebugStringA(msg)
#else
#define RF_ASSERT(expr) ((void)0)
#define RF_TRACE(msg) ((void)0)
#endif

// Common function calling conventions
#ifndef __fastcall
#define __fastcall
#endif

// External symbols commonly used in decompiled code
extern "C" {
    extern IMAGE_DOS_HEADER __ImageBase;  // Image base for PE files

    // Delay load helper functions
    FARPROC WINAPI __delayLoadHelper2(
        PCImgDelayDescr pidd,
        FARPROC * ppfnIATEntry
    );
}

// Common decompiled code types and external symbols
extern "C" {
    // Image base symbol used in decompiled code
    extern IMAGE_DOS_HEADER __ImageBase;

    // Common external symbols from decompiled code
    extern DWORD off_14000003C;
    extern ULONG_PTR _security_cookie;

    // Delay load helper function
    extern void* _delayLoadHelper2(ImgDelayDescr* pidd, void** ppfnIATEntry);
}

// Common decompiled code macros and helper functions
#define LODWORD(x) ((DWORD)(x))
#define HIDWORD(x) ((DWORD)((x) >> 32))

// RF Online specific external symbols
extern struct EqSukData {
    void* pwszEpSuk;
} EqSukList[16];

extern void MyMessageBox(const char* title, const char* message);

// Namespace for NexusPro
namespace NexusPro {
    namespace Core {
        // Core utility functions will be defined here
    }
    
    namespace Authentication {
        // Authentication module types
    }
    
    namespace Network {
        // Network module types
    }
    
    namespace Database {
        // Database module types
    }
    
    namespace Security {
        // Security module types
    }
    
    namespace Player {
        // Player module types
    }
    
    namespace Items {
        // Items module types
    }
    
    namespace Economy {
        // Economy module types
    }
    
    namespace Combat {
        // Combat module types
    }
    
    namespace World {
        // World module types
    }
    
    namespace System {
        // System module types
    }
}

// Common error codes
#define RF_SUCCESS              0
#define RF_ERROR_GENERAL        -1
#define RF_ERROR_INVALID_PARAM  -2
#define RF_ERROR_OUT_OF_MEMORY  -3
#define RF_ERROR_NOT_FOUND      -4
#define RF_ERROR_ACCESS_DENIED  -5

#endif // NEXUSPRO_COMMON_H
