﻿/*
 * Function: j_?check_min_max_guild_money@CMainThread@@QEAAEKPEAN0@Z
 * Address: 0x1400048B3
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


char __fastcall CMainThread::check_min_max_guild_money(CMainThread *this, unsigned int dwGuildSerial, long double *pdDalant, long double *pdGold)
{
  return // CMainThread::check_min_max_guild_money(); // MALFORMED CONSTRUCTOR - COMMENTED OUT
}

