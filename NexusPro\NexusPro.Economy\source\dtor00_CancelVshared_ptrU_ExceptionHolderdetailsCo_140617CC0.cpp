﻿/*
 * Function: ?dtor_0@?0???$_Cancel@V?$shared_ptr@U_ExceptionHolder@details@Concurrency@@@std@@@?$task_completion_event@E@Concurrency@@QEBA_NV?$shared_ptr@U_ExceptionHolder@details@Concurrency@@@std@@PEAX@Z@4HA_9
 * Address: 0x140617CC0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Required includes for Concurrency namespace
#include <ppl.h>
#include <ppltasks.h>
#include <concurrent_vector.h>



int __fastcallConcurrency::task_completion_event<unsigned char> ::_Cancel<std::shared_ptr<Concurrency::details::_ExceptionHolder>>'::1'::dtor_0(__int64 a1, __int64 a2)
{
  __int64 v2; // rcx@1

  v2 = *(QWORD *)(a2 + 120);
  return std::_Vb_iterator<std::vector<bool,std::allocator<bool> >>::~_Vb_iterator<std::vector<bool,std::allocator<bool>>>();
}


